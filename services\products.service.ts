import {Observable} from "rxjs";
import {Product} from "@/models/product";
import ApiService from "./api.service";
import {PaginationResponse} from "@/models/pagination";

class ProductService {
  public static getRecommended(
    page: number = 1,
    pageSize: number = 10
  ): Observable<PaginationResponse<Product>> {
    return ApiService.request("GET", "/app/products/catalog", {
      page: page,
      pageSize: pageSize
    });
  }

  public static getById(id: string): Observable<Product> {
    return ApiService.request("GET", `/products/${id}`);
  }
}

export default ProductService;
