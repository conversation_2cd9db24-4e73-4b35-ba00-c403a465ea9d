import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20
  },
  headerText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    marginBottom: 24
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
    marginBottom: 32
  },
  tagButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 36
  },
  tagButtonSelected: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderColor: stylesConstants.colors.brand.primary
  },
  tagText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  tagTextSelected: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: 600
  },
  descriptionContainer: {
    marginBottom: 32
  },
  descriptionLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 12
  },
  textAreaContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    padding: 16,
    minHeight: 120
  },
  textArea: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    flex: 1,
    textAlignVertical: "top"
  },
  submitButton: {
    marginBottom: 16
  },
  backButton: {
    alignItems: "center",
    paddingVertical: 16
  },
  backButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24
  }
});

export default styles;
