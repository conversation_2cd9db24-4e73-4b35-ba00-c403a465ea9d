import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    pageContainer: {
        gap: 24
    },
    messageItem: {
        flexDirection: "row",
        alignItems: "center",
        gap: 16,
        paddingVertical: 8,
        marginBottom: 16
    },
    avatarContainer: {
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: stylesConstants.colors.gray300,
    },
    avatar: {
        width: "100%",
        height: "100%",
        borderRadius: 24
    },
    avatarBorder: {
        position: "absolute",
        width: "100%",
        height: "100%",
        borderRadius: 24,
        borderWidth: 0.75,
        borderColor: stylesConstants.colors.gray900,
        opacity: 0.08
    },
    onlineIndicator: {
        position: "absolute",
        bottom: 0,
        right: 0,
        width: 12,
        height: 12,
        borderRadius: 6,
        backgroundColor: stylesConstants.colors.brand.primary,
        borderWidth: 1.5,
        borderColor: stylesConstants.colors.fullWhite
    },
    messageContent: {
        flex: 1,
        gap: 8
    },
    messageHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        gap: 8
    },
    nameRow: {
        flexDirection: "row",
        alignItems: "center",
        gap: 6,
        flex: 1
    },
    userName: {
        fontSize: 14,
        fontWeight: "600",
        lineHeight: 20,
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans
    },
    pinnedBadge: {
        flexDirection: "row",
        alignItems: "center",
        gap: 4,
        paddingHorizontal: 8,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
        borderColor: stylesConstants.colors.gray100
    },
    pinnedText: {
        fontSize: 10,
        fontWeight: "600",
        textAlign: "center",
        lineHeight: 18,
        color: stylesConstants.colors.gray25,
        fontFamily: stylesConstants.fonts.openSans
    },
    timeRow: {
        flexDirection: "row",
        alignItems: "center",
        gap: 4
    },
    messageTime: {
        fontSize: 10,
        fontWeight: "400",
        lineHeight: 18,
        color: stylesConstants.colors.gray100,
        fontFamily: stylesConstants.fonts.openSans
    },
    messageBody: {
        flexDirection: "row",
        alignItems: "center",
        gap: 4,
        paddingRight: 2
    },
    messageText: {
        fontSize: 12,
        fontWeight: "600",
        lineHeight: 18,
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        flex: 1
    },
    lightMessageText: {
        fontWeight: "400"
    },
    unreadBadge: {
        width: 16,
        height: 16,
        borderRadius: 16,
        backgroundColor: stylesConstants.colors.error600,
        justifyContent: "center",
        alignItems: "center"
    },
    unreadCount: {
        fontSize: 10,
        fontWeight: "700",
        lineHeight: 10,
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans
    },
    linkButton: {
        height: 20
    },
    linkButtonText: {
        fontSize: 12,
        fontWeight: "700",
        lineHeight: 18,
        color: "#15B79E",
        fontFamily: stylesConstants.fonts.openSans
    },
    divider: {
        height: 1,

        width: "100%",
        backgroundColor: stylesConstants.colors.gray800,
        marginBottom: 16
    },
});

export default styles;
