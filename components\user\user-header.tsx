import {ImageBackground} from "expo-image";
import React from "react";
import {Text, View} from "react-native";
import styles from "../../styles/components/user/user-header.style";
import stylesConstants from "../../styles/styles-constants";
import Button from "../button";
import EditIcon from "../icons/edit-icon";
import GearIcon from "../icons/gear-icon";
import MarkerPinIcon from "../icons/marker-pin-icon";
import Avatar from "./avatar";
import {useTranslation} from "react-i18next";

const UserHeader: React.FC = () => {
    const {t} = useTranslation();

    return (
        <View style={styles.container}>
            <ImageBackground
                imageStyle={styles.backgroundImageStyle}
                style={styles.backgroundImage}
                source={require("../../assets/textures/rotated-pattern.png")}
            >
                <View style={styles.overlay} />
                <View style={styles.content}>
                    <View style={styles.innerContainer}>
                        <Avatar url="https://upload.wikimedia.org/wikipedia/commons/thumb/1/1e/<PERSON>-amadeus-mozart_1.jpg/1200px-<PERSON>-amadeus-mozart_1.jpg" />
                        <View style={styles.userInfo}>
                            <Text style={styles.userName}>Mozart</Text>
                            <View style={styles.locationContainer}>
                                <MarkerPinIcon />
                                <Text style={styles.locationText}>
                                    Plus Ultra - BH
                                </Text>
                            </View>
                        </View>
                        <View style={styles.buttonsContainer}>
                            <Button
                                style={styles.button}
                                text={t("user.editInfo")}
                                icon={<EditIcon />}
                            />
                            <Button
                                style={styles.button}
                                text={t("user.generalSettings")}
                                icon={<GearIcon />}
                                borderColor={stylesConstants.colors.gray25}
                                backgroundColor={"transparent"}
                                underlayColor={"transparent"}
                            />
                        </View>
                    </View>
                </View>
            </ImageBackground>
        </View>
    );
};
export default UserHeader;
