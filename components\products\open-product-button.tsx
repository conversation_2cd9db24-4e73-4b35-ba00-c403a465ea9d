import {TouchableHighlight, View, Text} from "react-native";
import styles from "../../styles/components/products/open-product-button.style";
import ChevronRightIcon from "../icons/chevron-right-icon";
import {useTranslation} from "react-i18next";
import React, {useCallback} from "react";
import {useRouter} from "expo-router";
import {ProductPageParams} from "../../app/(logged-stack)/product-page";

export interface OpenProductButtonProps {
    noBackground?: boolean;
    productId: number;
}

const OpenProductButton: React.FC<OpenProductButtonProps> = (props) => {
    const {t} = useTranslation();
    const router = useRouter();

    const onPress = useCallback(() => {
        router.navigate({
            pathname: "/(logged-stack)/product-page",
            params: {
                id: props.productId.toFixed()
            } as ProductPageParams
        });
    }, []);

    return (
        <TouchableHighlight
            style={[
                styles.container,
                !props.noBackground && styles.containerBackground
            ]}
            onPress={onPress}
        >
            <View style={styles.innerContainer}>
                <Text style={styles.text}>
                    {t("products.openProductButton")}
                </Text>
                <ChevronRightIcon />
            </View>
        </TouchableHighlight>
    );
};

export default OpenProductButton;
