import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#111828"
  },
  customHeader: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 16,
    gap: 63
  },
  backButton: {
    width: 24,
    height: 24
  },
  headerTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center",
    flex: 1,
    marginRight: 24 // Compensate for back button width
  },
  tabContainer: {
    flexDirection: "row",
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#667085",
    gap: 12,
    marginTop: 4
  },
  tab: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 1,
    minHeight: 32,
    justifyContent: "center",
    alignItems: "center"
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#FCFCFD"
  },
  tabText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center"
  },
  activeTabText: {
    fontWeight: "700"
  },
  scrollView: {
    flex: 1
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingTop: 20,
    gap: 16
  },
  opportunityCard: {
    backgroundColor: "#202938",
    borderRadius: 8,
    padding: 12,
    gap: 6
  },
  opportunityHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },
  opportunityIcon: {
    width: 32,
    height: 32,
    backgroundColor: "#EAECF0",
    borderRadius: 16,
    flexShrink: 0
  },
  opportunityTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18,
    flex: 1,
    marginLeft: 4
  },
  opportunityDescription: {
    paddingLeft: 44
  },
  opportunityDescriptionText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    width: 227
  },
  opportunityActions: {
    backgroundColor: "#344054",
    borderRadius: 4,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
    paddingVertical: 8,
    gap: 8,
    marginTop: 6
  },
  editButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    borderRadius: 8,
    height: 20
  },
  editButtonText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18
  },
  statusText: {
    fontSize: 10,
    fontFamily: stylesConstants.fonts.openSans,
    fontWeight: "400",
    lineHeight: 18,
    flex: 1,
    textAlign: "center"
  },
  toggleBase: {
    flexDirection: "row",
    alignItems: "center",
    padding: 2,
    width: 44,
    height: 24,
    borderRadius: 12
  },
  toggleEnabled: {
    backgroundColor: "#1F9464",
    justifyContent: "flex-end"
  },
  toggleInactive: {
    backgroundColor: "#CFD4DC",
    justifyContent: "flex-start"
  },
  toggleDisabledContainer: {
    width: 44,
    height: 24
  },
  toggleDisabled: {
    backgroundColor: "#475467",
    borderRadius: 12,
    width: 44,
    height: 24,
    padding: 2,
    justifyContent: "flex-start"
  },
  toggleButton: {
    width: 20,
    height: 20,
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    shadowColor: "rgba(16, 24, 40, 0.1)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 2
  },
  toggleButtonEnabled: {
    // Position handled by justifyContent in toggleBase
  },
  toggleButtonInactive: {
    // Position handled by justifyContent in toggleBase
  },
  toggleButtonDisabled: {
    backgroundColor: "#EAECF0"
  }
});

export default styles;
