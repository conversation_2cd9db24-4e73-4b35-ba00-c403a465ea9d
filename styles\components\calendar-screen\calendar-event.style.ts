import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  presenceInformation: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    marginBottom: 8
  },
  presenceLabelContainer: {
    display: "flex",
    flexDirection: "row",
    marginBottom: 10,
    alignItems: "center",
    gap: 8
  },
  text: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontStyle: "normal",
    fontWeight: 400,
    lineHeight: 18,
    color: stylesConstants.colors.fullWhite
  },
  touchableArea: {
    borderLeftColor: stylesConstants.colors.alert400,
    borderLeftWidth: 4,
    borderStyle: "solid",
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    paddingLeft: 20,
    paddingRight: 16,
    paddingVertical: 16,
    marginHorizontal: 20
  },
  locationLabelContainer: {
    display: "flex",
    flexDirection: "row",
    gap: 4,
    alignItems: "center",
    marginBottom: 6
  },
  eventName: {
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20,
    marginBottom: 6
  },
  eventDescription: {
    marginBottom: 12
  },
  footer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  presenceContainer: {
    display: "flex",
    flexDirection: "row",
    gap: 4,
    alignItems: "center"
  },
  presenceText: {
    fontWeight: 700,
    color: stylesConstants.colors.green400
  }
});

export default styles;
