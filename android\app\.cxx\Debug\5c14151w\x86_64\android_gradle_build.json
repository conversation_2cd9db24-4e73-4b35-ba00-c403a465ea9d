{"buildFiles": ["C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\.cxx\\Debug\\5c14151w\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\.cxx\\Debug\\5c14151w\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "appmodules", "output": "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\x86_64\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\x86_64\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fc98766b9298386f4aefafd293f4f926\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_pagerview::@7032a8921530ec438d60": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_pagerview"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnscreens", "output": "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\x86_64\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fc98766b9298386f4aefafd293f4f926\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnsvg", "output": "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\x86_64\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fc98766b9298386f4aefafd293f4f926\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_safeareacontext", "output": "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fc98766b9298386f4aefafd293f4f926\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}