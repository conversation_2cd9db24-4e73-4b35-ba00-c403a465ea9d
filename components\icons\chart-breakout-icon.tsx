import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const ChartBreakoutIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={21}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9.25 2.5H6.583c-1.4 0-2.1 0-2.635.273a2.5 2.5 0 0 0-1.092 1.092C2.583 4.4 2.583 5.1 2.583 6.5v7c0 1.4 0 2.1.273 2.635a2.5 2.5 0 0 0 1.092 1.093c.535.272 1.235.272 2.635.272h7c1.4 0 2.1 0 2.635-.272a2.5 2.5 0 0 0 1.093-1.093c.272-.535.272-1.235.272-2.635v-2.666m-7.5-4.167h3.334V10M13 2.917v-1.25m3.283 2.134.884-.884m.008 4.167h1.25M2.583 11.123c.544.084 1.1.127 1.667.127a10.82 10.82 0 0 0 8.85-4.583"></Path>
        </Svg>
    );
};

export default ChartBreakoutIcon;