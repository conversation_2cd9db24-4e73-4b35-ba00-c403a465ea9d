import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const MessageChatSquareIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={25}
            height={24}
            fill="none"
            {...props}
        >
            <Path
                stroke="#D0D5DD"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="m10.3 15-3.075 3.114c-.43.434-.644.651-.828.666a.5.5 0 0 1-.421-.172c-.12-.14-.12-.446-.12-1.056v-1.56c0-.548-.449-.944-.99-1.024v0a3 3 0 0 1-2.534-2.533c-.032-.216-.032-.475-.032-.99V6.8c0-1.68 0-2.52.327-3.162a3 3 0 0 1 1.311-1.311C4.58 2 5.42 2 7.1 2h7.4c1.68 0 2.52 0 3.162.327a3 3 0 0 1 1.311 1.311c.327.642.327 1.482.327 3.162V11m0 11-2.176-1.513c-.306-.213-.46-.32-.626-.395a2.002 2.002 0 0 0-.462-.145c-.18-.033-.366-.033-.739-.033H13.5c-1.12 0-1.68 0-2.108-.218a2 2 0 0 1-.874-.874c-.218-.428-.218-.988-.218-2.108V14.2c0-1.12 0-1.68.218-2.108a2 2 0 0 1 .874-.874C11.82 11 12.38 11 13.5 11h5.6c1.12 0 1.68 0 2.108.218a2 2 0 0 1 .874.874c.218.428.218.988.218 2.108v2.714c0 .932 0 1.398-.152 1.766a2 2 0 0 1-1.083 1.082c-.367.152-.833.152-1.765.152V22Z"
            />
        </Svg>
    );
};

export default MessageChatSquareIcon;