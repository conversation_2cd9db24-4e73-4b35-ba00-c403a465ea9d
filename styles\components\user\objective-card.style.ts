import {StyleSheet} from "react-native";
import stylesConstants from "@/styles/styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    flexDirection: "row",
    gap: 12,
    padding: 16,
    borderRadius: 8
  },
  image: {
    height: 33,
    width: 33
  },
  content: {
    flex: 1
  },
  title: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    color: stylesConstants.colors.fullWhite,
    fontWeight: 600,
    lineHeight: 20,
    marginBottom: 4
  },
  description: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    color: stylesConstants.colors.fullWhite,
    fontWeight: 400,
    lineHeight: 18
  },
  progress: {
    marginTop: 12
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 7
  },
  accomplished: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    color: stylesConstants.colors.fullWhite,
    fontWeight: 400,
    lineHeight: 18
  },
  redeem: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    color: stylesConstants.colors.yellow400,
    fontWeight: 700,
    lineHeight: 18
  }
});

export default styles;
