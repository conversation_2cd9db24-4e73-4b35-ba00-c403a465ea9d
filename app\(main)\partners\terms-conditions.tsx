import React from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity
} from "react-native";
import ScreenWithHeader from "../../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import FullSizeButton from "../../../components/full-size-button";
import styles from "@/styles/partners/terms-conditions.style";

const TermsConditionsScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();

  const handleClose = () => {
    router.back();
  };

  return (
    <ScreenWithHeader
      screenTitle={t("partners.termsConditions", "Termos e condições do parceiro")}
      backButton
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Text style={styles.title}>
            {t("partners.termsTitle", "Termos e condições do parceiro")}
          </Text>
          
          <Text style={styles.description}>
            {t(
              "partners.termsDescription",
              "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Dolor enim eu tortor urna sed duis nulla. Aliquam vestibulum nunc commodo cursus in vestibulum dignissim. Tellus nunc cursus a sed eleifend dolor. Sed cursus nunc cursus a sed eleifend dolor."
            )}
          </Text>

          <Text style={styles.description}>
            {t(
              "partners.termsDescription2", 
              "Elit in eleifend sed et ex proin commodo consectetur. Proin interdum commodo consectetur congue risus, volutpat aliquet. Arcu ut augue ut feugiat lorem. Risus, volutpat aliquet. Arcu ut augue ut feugiat lorem. Risus, volutpat aliquet. Arcu ut augue ut feugiat lorem."
            )}
          </Text>

          <Text style={styles.description}>
            {t(
              "partners.termsDescription3",
              "Viverra purus et erat auctor aliquam. Risus, volutpat vulputate posuere purus sit congue convallis aliquet. Arcu id augue ut feugiat donec porttitor neque. Mauris, neque ultricies eu vestibulum, bibendum quam lorem id. Dolor lacus, eget nunc lectus in tellus, pharetra, porttitor."
            )}
          </Text>

          <Text style={styles.description}>
            {t(
              "partners.termsDescription4",
              "Ao adquirir o benefício você aceita os termos e condições da loja."
            )}
          </Text>
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <FullSizeButton
          text={t("partners.close", "Fechar")}
          onPress={handleClose}
        />
      </View>
    </ScreenWithHeader>
  );
};

export default TermsConditionsScreen;
