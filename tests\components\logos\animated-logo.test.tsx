import React from "react";
import { render, act } from "@testing-library/react-native";
import { Text } from "react-native";

jest.useFakeTimers();

// Mock react-native-reanimated with the jest mock and ensure runOnJS executes immediately
jest.mock("react-native-reanimated", () => {
  const Reanimated = require("react-native-reanimated/mock");
  return {
    ...Reanimated,
    // Override functions we rely on in component
    runOnJS: (fn: any) => fn,
    withTiming: (value: any, _config?: any, callback?: any) => {
      if (callback) callback(true);
      return value;
    },
    withDelay: (_delay: number, value: any) => value,
    Easing: { bezier: () => {}, linear: () => {} },
    ReduceMotion: { System: 0, Never: 0 },
  };
});

// Mock SmallLogo and LogoName
jest.mock("../../../components/logos/small-logo", () => {
  const { View } = require("react-native");
  return {
    __esModule: true,
    default: (props: any) => <View {...props} testID="small-logo" />,
  };
});

jest.mock("../../../components/logos/logo-name", () => {
  const { View } = require("react-native");
  return {
    __esModule: true,
    default: (props: any) => <View {...props} testID="logo-name" />,
  };
});

import AnimatedLogo from "../../../components/logos/animated-logo";

describe("AnimatedLogo component", () => {
  it("shows animated logo first and children after animation completes", async () => {
    const { queryByTestId, queryByText } = render(
      <AnimatedLogo>
        <Text>FINISHED</Text>
      </AnimatedLogo>
    );

    // Initially, children should not be visible, but logos should
    expect(queryByText("FINISHED")).toBeNull();
    expect(queryByTestId("small-logo")).toBeTruthy();

    // Advance timers within React act to ensure state updates are flushed
    await act(async () => {
      jest.runAllTimers();
    });

    // Re-rendering happens internally via state update; assert children now visible
    expect(queryByText("FINISHED")).toBeTruthy();
  });
}); 