import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        padding: 16,
        display: "flex",
        gap: 12,
        marginBottom: 24
    },
    pillsContainer: {
        display: "flex",
        flexDirection: "row",
        gap: 6
    },
    contentContainer: {
        display: "flex",
        flexDirection: "row",
        gap: 10
    },
    image: {
        width: 40,
        height: 40,
        borderRadius: 4,
        justifyContent: "center",
        alignItems: "center",
        padding: 8,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderStrokesLines,
        backgroundColor: stylesConstants.colors.highlightBackground
    },
    titleContainer: {
        flex: 1
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 14,
        fontWeight: "700",
        fontFamily: stylesConstants.fonts.openSans,
        lineHeight: 20
    },
    description: {
        color: stylesConstants.colors.textPrimary,
        fontSize: 12,
        fontWeight: "400",
        lineHeight: 18,
        fontFamily: stylesConstants.fonts.openSans
    },
    footer: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between"
    },
    footerButton: {
        justifyContent: "center"
    }
});

export default styles;
