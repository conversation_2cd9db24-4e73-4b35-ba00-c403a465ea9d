import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import { TouchableHighlight } from "react-native";

// Mock SVG library
jest.mock("react-native-svg", () => {
  const { View } = require("react-native");
  return {
    __esModule: true,
    default: (props: any) => <View {...props} />,
    Path: (props: any) => <View {...props} />,
  };
});

// Mock Chevron icons to simple views with testIDs so we can assert which one was rendered
jest.mock("../../../components/icons/chevron-left-icon", () => {
  const { View } = require("react-native");
  return {
    __esModule: true,
    default: (props: any) => <View {...props} testID="chevron-left" />,
  };
});

jest.mock("../../../components/icons/chevron-right-icon", () => {
  const { View } = require("react-native");
  return {
    __esModule: true,
    default: (props: any) => <View {...props} testID="chevron-right" />,
  };
});

import NextButton from "../../../components/intro/next-button";

describe("NextButton component", () => {
  it("renders ChevronLeft when isPrevious is true", () => {
    const { queryByTestId } = render(<NextButton isPrevious />);

    expect(queryByTestId("chevron-left")).toBeTruthy();
    expect(queryByTestId("chevron-right")).toBeNull();
  });

  it("renders ChevronRight when isPrevious is false", () => {
    const { queryByTestId } = render(<NextButton />);

    expect(queryByTestId("chevron-right")).toBeTruthy();
    expect(queryByTestId("chevron-left")).toBeNull();
  });

  it("invokes onPress callback when pressed", () => {
    const onPress = jest.fn();
    const { UNSAFE_getByType } = render(<NextButton onPress={onPress} />);

    const touchable = UNSAFE_getByType(TouchableHighlight);
    fireEvent.press(touchable);

    expect(onPress).toHaveBeenCalled();
  });
}); 