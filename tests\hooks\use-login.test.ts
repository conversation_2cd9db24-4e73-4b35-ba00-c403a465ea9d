import React from "react";
import {render, act} from "@testing-library/react-native";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({t: (k: string) => k})
}));

const mockServiceLogin = jest.fn();

jest.mock("../../services/login.service", () => ({
  __esModule: true,
  default: {
    login: (...args: any[]) => {
      mockServiceLogin(...args);
      return {subscribe: ({next}: any) => next?.()};
    }
  }
}));

const mockSafeParseLogin = jest.fn();

jest.mock("../../models/login", () => ({
  LoginRequestSchema: {safeParse: (...args: any) => mockSafeParseLogin(...args)}
}));

const mockEmitError = jest.fn();
jest.mock("../../contexts/error-dialog-context", () => ({
  __esModule: true,
  ErrorType: {Warning: 0, Error: 1},
  useErrorMessage: () => ({emitError: mockEmitError})
}));

// Mock the notification hook to prevent Firebase subscription issues
jest.mock("../../hooks/use-notification", () => ({
  __esModule: true,
  default: () => ({
    firebaseToken: "mock-firebase-token"
  })
}));

jest.mock("expo-local-authentication", () => ({
  __esModule: true,
  hasHardwareAsync: jest.fn(() => Promise.resolve(false)),
  isEnrolledAsync: jest.fn(() => Promise.resolve(false)),
  authenticateAsync: jest.fn()
}));

import useLogin from "../../hooks/use-login";

function setup() {
  let hook: ReturnType<typeof useLogin> | undefined;
  function Test() {
    hook = useLogin();
    return null;
  }
  render(React.createElement(Test));
  return hook!;
}

describe("useLogin hook", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("alerts on validation error", () => {
    mockSafeParseLogin.mockReturnValue({
      success: false,
      error: {flatten: () => ({fieldErrors: {}})}
    });
    const hook = setup();
    act(() => {
      hook.login({} as any);
    });
    expect(mockEmitError).toHaveBeenCalled();
    expect(mockServiceLogin).not.toHaveBeenCalled();
  });

  it("calls service and biometrics on success", async () => {
    mockSafeParseLogin.mockReturnValue({success: true, data: {}});
    const hook = setup();
    await act(async () => {
      hook.login({email: "a", password: "b"} as any);
    });
    expect(mockServiceLogin).toHaveBeenCalled();
  });
});
