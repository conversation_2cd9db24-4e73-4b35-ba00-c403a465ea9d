import React from "react";
import Svg, {<PERSON>lip<PERSON><PERSON>, Defs, G, Path, SvgProps} from "react-native-svg";

const ClockIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={16}
            height={16}
            fill="none"
            {...props}
        >
            <G clipPath="url(#a)">
                <Path
                    stroke="#fff"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M8 4v4l2.667 1.333m4-1.333A6.667 6.667 0 1 1 1.333 8a6.667 6.667 0 0 1 13.334 0Z"
                />
            </G>
            <Defs>
                <ClipPath id="a">
                    <Path fill="#fff" d="M0 0h16v16H0z" />
                </ClipPath>
            </Defs>
        </Svg>
    );
};

export default ClockIcon;