import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface WhatsAppIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const WhatsAppIcon: React.FC<WhatsAppIconProps> = (props) => {
  const color = useMemo(() => props.replaceColor ?? "#25D366", [props.replaceColor]);

  return (
    <Svg
      width={20}
      height={20}
      fill="none"
      {...props}
    >
      <Path
        fill={color}
        d="M17.472 2.512A9.91 9.91 0 0010.05 0C4.556 0 .095 4.46.093 9.954c-.001 1.754.458 3.463 1.33 4.967L0 20l5.18-1.36a9.958 9.958 0 004.77 1.216h.004c5.494 0 9.956-4.46 9.958-9.954a9.908 9.908 0 00-2.44-6.39zM10.05 18.207h-.003a8.27 8.27 0 01-4.212-1.155l-.302-.179-3.132.821.836-3.055-.197-.314a8.27 8.27 0 01-1.267-4.41c.002-4.57 3.72-8.287 8.291-8.287a8.248 8.248 0 015.85 2.426 8.25 8.25 0 012.42 5.864c-.002 4.57-3.72 8.289-8.284 8.289zm4.546-6.206c-.249-.125-1.475-.728-1.704-.811-.228-.084-.394-.125-.56.125-.166.249-.644.811-.789.978-.145.166-.29.187-.539.062-.249-.125-1.052-.388-2.004-1.237-.741-.66-1.242-1.477-1.388-1.726-.145-.249-.015-.384.109-.508.112-.112.249-.29.374-.436.125-.145.166-.249.249-.415.083-.166.041-.311-.021-.436-.062-.125-.56-1.35-.768-1.848-.203-.485-.409-.419-.56-.427-.145-.007-.311-.009-.477-.009a.916.916 0 00-.665.311c-.228.249-.871.851-.871 2.075 0 1.224.892 2.407 1.017 2.573.125.166 1.754 2.679 4.25 3.758.594.257 1.058.41 1.42.525.596.189 1.14.162 1.569.098.479-.071 1.475-.603 1.683-1.186.208-.582.208-1.081.145-1.186-.062-.104-.228-.166-.477-.29z"
      />
    </Svg>
  );
};

export default WhatsAppIcon;
