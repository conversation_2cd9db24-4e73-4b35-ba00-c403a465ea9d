import React from "react";
import {useTranslation} from "react-i18next";
import {Text, View} from "react-native";
import styles from "@/styles/components/user/objective-card.style";
import ProgressBar from "@/components/progress-bar";
import SmallSeal from "@/components/seals/small-seal";

export interface ObjectiveCardProps {
  title: string;
  description: string;
  progress: number;
  accomplished: string;
}

const ObjectiveCard: React.FC<ObjectiveCardProps> = (props) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <SmallSeal source={require("@/assets/seals/Diamond.png")} />
      <View style={styles.content}>
        <Text style={styles.title}>{props.title}</Text>
        <Text style={styles.description}>{props.description}</Text>
        <ProgressBar style={styles.progress} progress={props.progress} />
        <View style={styles.footer}>
          <Text style={styles.accomplished}>{props.accomplished}</Text>
          <Text style={styles.redeem}>{t("objectives.redeem")}</Text>
        </View>
      </View>
    </View>
  );
};

export default ObjectiveCard;
