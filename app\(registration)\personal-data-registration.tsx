import React, {useState, useCallback} from "react";
import {Text, View, ColorValue} from "react-native";
import Screen from "../../components/screen";
import BackgroundLogoTexture from "../../components/logos/background-logo-texture";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";
import UserIcon from "../../components/icons/user-icon";

const PersonalDataRegistration: React.FC = () => {
  const {t} = useTranslation();
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    birthDate: ""
  });

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
    };

  const userIcon = useCallback(
    (errorColor?: ColorValue) => <UserIcon replaceColor={errorColor} />,
    []
  );

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={{flex: 1, padding: 20}}>
          <BackButton />
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#fff",
              textAlign: "center",
              marginBottom: 20
            }}
          >
            {t("personalDataRegistration.title", "Dados Pessoais")}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "#fff",
              textAlign: "center",
              marginBottom: 30
            }}
          >
            {t(
              "personalDataRegistration.description",
              "Preencha seus dados pessoais"
            )}
          </Text>

          <View style={{gap: 15, marginBottom: 30}}>
            <InputField
              label={t("personalDataRegistration.fullName", "Nome Completo")}
              value={formData.fullName}
              onChangeText={handleInputChange("fullName")}
              placeholder={t(
                "personalDataRegistration.fullNamePlaceholder",
                "Digite seu nome completo"
              )}
              icon={userIcon}
            />
            <InputField
              label={t("personalDataRegistration.email", "E-mail")}
              value={formData.email}
              onChangeText={handleInputChange("email")}
              placeholder={t(
                "personalDataRegistration.emailPlaceholder",
                "Digite seu e-mail"
              )}
              inputMode="email"
            />
            <InputField
              label={t("personalDataRegistration.phone", "Telefone")}
              value={formData.phone}
              onChangeText={handleInputChange("phone")}
              placeholder={t(
                "personalDataRegistration.phonePlaceholder",
                "Digite seu telefone"
              )}
              inputMode="tel"
            />
            <InputField
              label={t(
                "personalDataRegistration.birthDate",
                "Data de Nascimento"
              )}
              value={formData.birthDate}
              onChangeText={handleInputChange("birthDate")}
              placeholder={t(
                "personalDataRegistration.birthDatePlaceholder",
                "DD/MM/AAAA"
              )}
            />
          </View>

          <FullSizeButton
            text={t("personalDataRegistration.next", "Próximo")}
            onPress={() => {}}
          />
        </View>
      </Screen>
    </>
  );
};

export default PersonalDataRegistration;
