import React, {useState, useRef} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Dimensions
} from "react-native";
import {Image} from "expo-image";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useBottomModal} from "../../contexts/bottom-modal-context";
import SearchIcon from "../../components/icons/search-icon";
import BottomModal from "../../components/bottom-modal";
import FullSizeButton from "../../components/full-size-button";
import InvisibleFullSizeButton from "../../components/invisible-full-size-button";
import styles from "@/styles/magazine/magazine-list.style";

interface Magazine {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  publishDate: string;
  category: string;
  readTime: string;
  isNew: boolean;
  isPremium: boolean;
  author?: string;
  authorTitle?: string;
}

const MagazineList: React.FC = () => {
  const {t} = useTranslation();
  const {openModal} = useBottomModal();
  const [searchTerm, setSearchTerm] = useState("");

  const [magazines] = useState<Magazine[]>([
    {
      id: "1",
      title:
        "Confira a nova edição da revista digital Club M com João Appolinário.",
      description: "Lorem ipsum quisque lobortis in eu rhoncus...",
      coverImage: "https://picsum.photos/400/600?random=1",
      publishDate: "2024-01-15",
      category: "Novidade no ar",
      readTime: "15 min",
      isNew: true,
      isPremium: false,
      author: "João Appolinário",
      authorTitle: "CEO Club M"
    },
    {
      id: "featured-2",
      title: "Liderança Transformadora no Século XXI",
      description:
        "Descubra as estratégias de liderança que estão moldando o futuro dos negócios...",
      coverImage: "https://picsum.photos/400/600?random=2",
      publishDate: "2024-01-12",
      category: "Novidade no ar",
      readTime: "18 min",
      isNew: true,
      isPremium: false,
      author: "Maria Silva",
      authorTitle: "Especialista em Liderança"
    },
    {
      id: "featured-3",
      title: "Inovação e Tecnologia: O Futuro dos Negócios",
      description:
        "Uma análise profunda sobre as tecnologias emergentes que revolucionarão o mercado...",
      coverImage: "https://picsum.photos/400/600?random=3",
      publishDate: "2024-01-08",
      category: "Novidade no ar",
      readTime: "22 min",
      isNew: true,
      isPremium: false,
      author: "Carlos Tech",
      authorTitle: "CTO Innovation Labs"
    },
    {
      id: "2",
      title: "João Kepler, O Maior Investidor Anjo do Brasil",
      description:
        "O homem das mil startups recebeu a equipe da Club M The Magazine para um bate papo sobre negócios, empreendedorismo e muitas lições para a vida.",
      coverImage: "https://picsum.photos/400/600?random=4",
      publishDate: "2024-01-10",
      category: "Edição 01",
      readTime: "12 min",
      isNew: false,
      isPremium: false,
      author: "João Kepler",
      authorTitle: "Maior Investidor Anjo do Brasil"
    },
    {
      id: "3",
      title: "Jorge Bischoff",
      description: "Lorem ipsum quisque lobortis in eu rhoncus...",
      coverImage: "https://picsum.photos/400/600?random=5",
      publishDate: "2024-01-05",
      category: "Edição 02",
      readTime: "20 min",
      isNew: false,
      isPremium: false,
      author: "Jorge Bischoff",
      authorTitle: "Empresário"
    },
    {
      id: "4",
      title: "Líder é quem inspira",
      description: "Lorem ipsum quisque lobortis in eu rhoncus...",
      coverImage: "https://picsum.photos/400/600?random=6",
      publishDate: "2023-12-28",
      category: "Edição 03",
      readTime: "18 min",
      isNew: false,
      isPremium: false
    },
    {
      id: "5",
      title: "Jorge Bischoff",
      description: "Lorem ipsum quisque lobortis in eu rhoncus...",
      coverImage: "https://picsum.photos/400/600?random=7",
      publishDate: "2023-12-20",
      category: "Edição 04",
      readTime: "15 min",
      isNew: false,
      isPremium: false,
      author: "Jorge Bischoff",
      authorTitle: "Empresário"
    },
    {
      id: "6",
      title: "João Kepler, O Maior Investidor Anjo do Brasil",
      description: "Lorem ipsum quisque lobortis in eu rhoncus...",
      coverImage: "https://picsum.photos/400/600?random=8",
      publishDate: "2023-12-15",
      category: "Edição 05",
      readTime: "22 min",
      isNew: false,
      isPremium: false,
      author: "João Kepler",
      authorTitle: "Maior Investidor Anjo do Brasil"
    }
  ]);

  const featuredMagazines = magazines.filter((mag) => mag.isNew);
  const recentMagazines = magazines.filter((mag) => !mag.isNew);

  // Carousel state
  const [currentCarouselIndex, setCurrentCarouselIndex] = useState(0);
  const carouselRef = useRef<ScrollView>(null);
  const mainScrollRef = useRef<ScrollView>(null);
  const screenWidth = Dimensions.get("window").width;

  const handleMagazinePress = (magazine: Magazine) => {
    openModal({
      title: "Sobre a revista",
      children: <MagazineDetailModal magazine={magazine} />
    });
  };

  const handleCarouselScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / screenWidth);
    setCurrentCarouselIndex(index);
  };

  const scrollToIndex = (index: number) => {
    carouselRef.current?.scrollTo({
      x: index * screenWidth,
      animated: true
    });
    setCurrentCarouselIndex(index);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "short",
      year: "numeric"
    });
  };

  const scrollToTop = () => {
    mainScrollRef.current?.scrollTo({y: 0, animated: true});
  };

  return (
    <ScreenWithHeader
      screenTitle={t("magazine.listTitle", "Revista Digital Club M")}
      backButton
      disablePadding
    >
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <SearchIcon width={20} height={20} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar edições, temas, etc..."
            placeholderTextColor="#666"
            value={searchTerm}
            onChangeText={setSearchTerm}
          />
        </View>
      </View>

      {/* Featured Magazines Carousel */}
      {featuredMagazines.length > 0 && (
        <View style={styles.carouselContainer}>
          <ScrollView
            ref={carouselRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={handleCarouselScroll}
            style={styles.carousel}
          >
            {featuredMagazines.map((magazine, index) => (
              <View
                key={magazine.id}
                style={[styles.carouselItemWrapper, {width: screenWidth}]}
              >
                <TouchableOpacity
                  style={styles.carouselItem}
                  onPress={() => handleMagazinePress(magazine)}
                >
                  <View style={styles.carouselImagePlaceholder}>
                    <Image
                      source={{uri: magazine.coverImage}}
                      style={styles.carouselImage}
                      contentFit="cover"
                      placeholder="https://via.placeholder.com/400x600/0F7C4D/FFFFFF?text=Loading"
                      transition={200}
                    />
                  </View>
                  <View style={styles.carouselBadge}>
                    <Text style={styles.carouselBadgeText}>
                      {magazine.category}
                    </Text>
                  </View>
                  <View style={styles.carouselTextContent}>
                    <Text style={styles.carouselTitle}>{magazine.title}</Text>
                    <Text style={styles.carouselDescription}>
                      {magazine.description}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>

          {/* Navigation Arrows */}
          <TouchableOpacity
            style={[styles.carouselArrow, styles.carouselArrowLeft]}
            onPress={() => scrollToIndex(Math.max(0, currentCarouselIndex - 1))}
            disabled={currentCarouselIndex === 0}
          >
            <Text style={styles.carouselArrowText}>‹</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.carouselArrow, styles.carouselArrowRight]}
            onPress={() =>
              scrollToIndex(
                Math.min(featuredMagazines.length - 1, currentCarouselIndex + 1)
              )
            }
            disabled={currentCarouselIndex === featuredMagazines.length - 1}
          >
            <Text style={styles.carouselArrowText}>›</Text>
          </TouchableOpacity>

          {/* Pagination Dots */}
          <View style={styles.paginationContainer}>
            {featuredMagazines.map((magazine, index) => (
              <TouchableOpacity
                key={`pagination-${magazine.id}`}
                style={[
                  styles.paginationDot,
                  index === currentCarouselIndex && styles.paginationDotActive
                ]}
                onPress={() => scrollToIndex(index)}
              />
            ))}
          </View>
        </View>
      )}

      <ScrollView ref={mainScrollRef}>
        {/* Recent Editions Carousel */}
        <View style={styles.categoryContainer}>
          <View style={styles.categoryHeader}>
            <Text style={styles.categoryTitle}>Edições recentes</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>Ver todos</Text>
            </TouchableOpacity>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalScrollContent}
            style={styles.horizontalScroll}
          >
            {recentMagazines.slice(0, 5).map((magazine) => (
              <TouchableOpacity
                key={magazine.id}
                style={styles.horizontalCard}
                onPress={() => handleMagazinePress(magazine)}
              >
                <View style={styles.horizontalImageContainer}>
                  <Image
                    source={{uri: magazine.coverImage}}
                    style={styles.horizontalImage}
                    contentFit="cover"
                  />
                </View>
                <View style={styles.horizontalContent}>
                  <Text style={styles.horizontalTitle}>
                    {magazine.category}
                  </Text>
                  <Text style={styles.horizontalDate}>
                    {formatDate(magazine.publishDate)}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Business Topics Carousel */}
        <View style={styles.categoryContainer}>
          <View style={styles.categoryHeader}>
            <Text style={styles.categoryTitle}>Negócios & Liderança</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>Ver todos</Text>
            </TouchableOpacity>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalScrollContent}
            style={styles.horizontalScroll}
          >
            {recentMagazines.slice(2, 7).map((magazine) => (
              <TouchableOpacity
                key={`business-${magazine.id}`}
                style={styles.horizontalCard}
                onPress={() => handleMagazinePress(magazine)}
              >
                <View style={styles.horizontalImageContainer}>
                  <Image
                    source={{uri: magazine.coverImage}}
                    style={styles.horizontalImage}
                    contentFit="cover"
                  />
                </View>
                <View style={styles.horizontalContent}>
                  <Text style={styles.horizontalTitle}>
                    {magazine.category}
                  </Text>
                  <Text style={styles.horizontalDate}>
                    {formatDate(magazine.publishDate)}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Back to Top Button */}
        <TouchableOpacity style={styles.backToTopButton} onPress={scrollToTop}>
          <Text style={styles.backToTopText}>↑ Voltar ao topo</Text>
        </TouchableOpacity>
      </ScrollView>
    </ScreenWithHeader>
  );
};

// Magazine Detail Modal Component
interface MagazineDetailModalProps {
  magazine: Magazine;
}

const MagazineDetailModal: React.FC<MagazineDetailModalProps> = ({
  magazine
}) => {
  const {t} = useTranslation();
  const {closeModal} = useBottomModal();

  return (
    <BottomModal.Container>
      <View style={styles.modalContainer}>
        <Image
          source={{uri: magazine.coverImage}}
          style={styles.modalImage}
          contentFit="cover"
        />
        <View style={styles.modalContent}>
          <Text style={styles.modalBadge}>Club M Apresenta</Text>
          <Text style={styles.modalTitle}>
            {magazine.author
              ? `${magazine.author}, ${magazine.title}`
              : magazine.title}
          </Text>
          <Text style={styles.modalDescription}>{magazine.description}</Text>
        </View>
      </View>
      <BottomModal.ButtonsContainer>
        <FullSizeButton
          text="Ler revista"
          onPress={() => {
            closeModal();
            // Navigate to magazine details
          }}
        />
        <InvisibleFullSizeButton text="Fechar" onPress={closeModal} />
      </BottomModal.ButtonsContainer>
    </BottomModal.Container>
  );
};

export default MagazineList;
