import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface ShoppingCartIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const ShoppingCartIcon: React.FC<ShoppingCartIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#FCFCFD", [props.replaceColor]);

    return (
        <Svg
            width={48}
            height={48}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 8h6l4 20h20l4-16H14M8 8 6 4H2m6 4v0m12 28a2 2 0 1 0 4 0 2 2 0 0 0-4 0Zm16 0a2 2 0 1 0 4 0 2 2 0 0 0-4 0Z"
            />
        </Svg>
    );
};

export default ShoppingCartIcon;
