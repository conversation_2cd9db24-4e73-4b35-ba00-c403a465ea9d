import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  monthScroll: {
    marginBottom: 20
  },
  monthList: {
    gap: 5
  },
  rightHeaderYearButton: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "center",
    height: "100%",
    gap: 8
  },
  rightHeaderYearText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontStyle: "normal",
    fontWeight: 600,
    lineHeight: 18,
    color: stylesConstants.colors.gray25
  },
  dayScroll: {
    marginBottom: 22
  },
  dayList: {
    gap: 10
  },
  eventsTodayText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontStyle: "normal",
    fontWeight: 700,
    lineHeight: 24,
    marginBottom: 16
  },
  search: {
    marginBottom: 20
  }
});

export default styles;
