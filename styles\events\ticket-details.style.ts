import {StyleSheet, Dimensions} from "react-native";
import stylesConstants from "../styles-constants";

const {width, height} = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground,
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  contentContainer: {
    paddingVertical: 16
  },
  ticketContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 20,
    marginBottom: 24
  },
  ticketHeader: {
    alignItems: "center",
    marginBottom: 20
  },
  eventTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 8
  },
  organizerContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4
  },
  organizerLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18
  },
  organizerName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "700",
    lineHeight: 18
  },
  ticketDetailsContainer: {
    gap: 16,
    marginBottom: 24
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start"
  },
  detailLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    flex: 1
  },
  detailValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18,
    flex: 1,
    textAlign: "right"
  },
  qrCodeContainer: {
    alignItems: "center",
    paddingTop: 16
  },
  qrCode: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16
  },
  qrCodePlaceholder: {
    width: 120,
    height: 120,
    backgroundColor: stylesConstants.colors.fullBlack,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center"
  },
  qrCodeText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    textAlign: "center"
  },
  fullScreenButton: {
    paddingVertical: 8
  },
  fullScreenButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    textDecorationLine: "underline"
  },
  buttonContainer: {
    gap: 16,
    paddingBottom: 32
  },
  unmarkPresenceButton: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: "center"
  },
  unmarkPresenceButtonText: {
    color: stylesConstants.colors.error600,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  },
  closeButton: {
    paddingVertical: 12,
    alignItems: "center"
  },
  closeButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24
  },
  // QR Modal Styles
  qrModalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: stylesConstants.colors.mainBackground,
    zIndex: 1000
  },
  qrModalContainer: {
    flex: 1,
    paddingTop: 60 // Account for status bar
  },
  qrModalHeader: {
    alignItems: "center",
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.borderDefault
  },
  qrModalTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 24
  },
  qrModalContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40
  },
  qrModalQRCode: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 12,
    padding: 24,
    marginBottom: 32
  },
  qrModalQRCodePlaceholder: {
    width: 250,
    height: 250,
    backgroundColor: stylesConstants.colors.fullBlack,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center"
  },
  qrModalQRCodeText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center"
  },
  qrModalCodeContainer: {
    alignItems: "center"
  },
  qrModalCodeLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 8
  },
  qrModalCodeRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  qrModalCodeValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  },
  copyButton: {
    padding: 8
  },
  copyButtonText: {
    fontSize: 20
  },
  qrModalCloseButton: {
    paddingVertical: 20,
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault
  },
  qrModalCloseButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24
  }
});

export default styles;
