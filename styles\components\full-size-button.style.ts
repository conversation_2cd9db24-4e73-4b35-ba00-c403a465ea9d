import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    display: "flex",
    flexDirection: "row",
    columnGap: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
    alignSelf: "stretch",
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: "solid",
    borderColor: stylesConstants.colors.brand.primary,
    backgroundColor: stylesConstants.colors.brand.primary
  },
  text: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontStyle: "normal",
    fontWeight: 600,
    lineHeight: 24
  },
  innerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8
  },
  disable: {
    backgroundColor: stylesConstants.colors.gray900
  },
  secondary: {
    backgroundColor: "transparent",
    borderColor: stylesConstants.colors.fullWhite
  },
  secondaryText: {
    color: stylesConstants.colors.fullWhite
  }
});

export default styles;
