import React from "react";
import {Text, View, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import BottomModal from "@/components/bottom-modal";
import styles from "@/styles/business/overview-drawer.style";

interface StatData {
  label: string;
  value: string;
  icon: string;
  action: string;
  status: string;
}

interface OverviewDrawerProps {
  stats: StatData[];
}

const OverviewDrawer: React.FC<OverviewDrawerProps> = ({stats}) => {
  const {t} = useTranslation();

  return (
    <BottomModal.Container>
      <View style={styles.container}>
        {stats.map((stat) => (
          <View key={stat.label} style={styles.statCard}>
            {stat.status === "active" && <View style={styles.activeIndicator} />}
            <View style={styles.statHeader}>
              <Text style={styles.statIcon}>{stat.icon}</Text>
              <Text style={styles.statDescription}>
                {stat.label === "conversas" 
                  ? t("businessCenter.conversationsToRespond", "Conversas para responder")
                  : t("businessCenter.activeOpportunities", "Oportunidades ativas")
                }
              </Text>
            </View>
            <View style={styles.statContent}>
              <View style={styles.statLeft}>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </View>
              <TouchableOpacity style={styles.statAction}>
                <Text style={styles.statActionText}>{stat.action}</Text>
                <Text style={styles.statActionArrow}>→</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>
    </BottomModal.Container>
  );
};

export default OverviewDrawer;
