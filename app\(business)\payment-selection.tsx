import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import <PERSON><PERSON>ithHeader from "../../components/screen-with-header";
import FullSizeButton from "../../components/full-size-button";
import {router, useLocalSearchParams} from "expo-router";
import styles from "../../styles/business/payment-selection.style";
import PixIcon from "../../components/icons/payment-methods/pix-icon";
import VisaIcon from "../../components/icons/payment-methods/visa-icon";
import MastercardIcon from "../../components/icons/payment-methods/mastercard-icon";

// Boleto icon component
const BoletoIcon: React.FC<{width?: number; height?: number}> = ({
  width = 16,
  height = 16
}) => <Text style={{fontSize: width, color: "#FFA500"}}>📄</Text>;

interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  type: "pix" | "boleto" | "mastercard" | "visa";
}

const PaymentSelection: React.FC = () => {
  const params = useLocalSearchParams();

  let opportunityData = null;
  try {
    if (params.opportunityData && typeof params.opportunityData === "string") {
      opportunityData = JSON.parse(decodeURIComponent(params.opportunityData));
    }
  } catch (error) {
    console.error("Error parsing opportunity data:", error);
    opportunityData = {};
  }

  const [paymentMethods] = useState<PaymentMethod[]>([
    {
      id: "1",
      name: "PIX (À Vista)",
      description: "Pagamento imediato",
      icon: PixIcon,
      type: "pix"
    },
    {
      id: "2",
      name: "Via Boleto (À Vista / parcelado)",
      description: "Aprovação em até 3 horas",
      icon: BoletoIcon,
      type: "boleto"
    },
    {
      id: "3",
      name: "Cartão Mastercard (À vista / parcelado)",
      description: "Crédito (com final 9876) - Pagamento imediato",
      icon: MastercardIcon,
      type: "mastercard"
    },
    {
      id: "4",
      name: "Cartão Visa (À vista / parcelado)",
      description: "Crédito (com final 1234) - Pagamento imediato",
      icon: VisaIcon,
      type: "visa"
    }
  ]);

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    string | null
  >(null);

  // Mock data for the opportunity value
  const opportunityValue = 150.0;
  const subtotal = opportunityValue;
  const total = subtotal;

  const handleContinue = () => {
    if (!selectedPaymentMethod) return;

    const selectedMethod = paymentMethods.find(
      (method) => method.id === selectedPaymentMethod
    );
    if (!selectedMethod) return;

    // Create a serializable version of the payment method (without the icon function)
    const serializablePaymentMethod = {
      id: selectedMethod.id,
      name: selectedMethod.name,
      description: selectedMethod.description,
      type: selectedMethod.type
    };

    const paymentData = {
      paymentMethod: serializablePaymentMethod,
      opportunityData: opportunityData ?? {},
      subtotal,
      total,
      opportunityValue
    };

    try {
      const jsonString = JSON.stringify(paymentData);
      console.log("JSON to encode:", jsonString);
      const encodedData = encodeURIComponent(jsonString);
      console.log("Encoded data:", encodedData);
      router.push(`/(business)/payment-review?paymentData=${encodedData}`);
    } catch (error) {
      console.error("Error encoding payment data:", error);
    }
  };

  return (
    <ScreenWithHeader screenTitle="Métodos de pagamento" backButton>
      <ScrollView style={styles.contentContainer}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>Selecione o método</Text>
        </View>

        {/* Order Summary */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Resumo da compra</Text>
          <View style={styles.orderSummaryContainer}>
            <View style={styles.orderItem}>
              <Text style={styles.orderItemName}>
                Publicação de oportunidade
              </Text>
              <Text style={styles.orderItemPrice}>
                R$ {opportunityValue.toFixed(2).replace(".", ",")}
              </Text>
            </View>

            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>
                R$ {total.toFixed(2).replace(".", ",")}
              </Text>
            </View>
          </View>
        </View>

        {/* Payment Methods */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Métodos de pagamento</Text>

          {paymentMethods.map((method) => (
            <TouchableOpacity
              key={method.id}
              style={[
                styles.paymentMethodCard,
                selectedPaymentMethod === method.id &&
                  styles.paymentMethodCardSelected
              ]}
              onPress={() => setSelectedPaymentMethod(method.id)}
            >
              <View style={styles.paymentMethodIcon}>
                <method.icon width={24} height={24} />
              </View>

              <View style={styles.paymentMethodInfo}>
                <Text style={styles.paymentMethodName}>{method.name}</Text>
                <Text style={styles.paymentMethodDescription}>
                  {method.description}
                </Text>
              </View>

              <View
                style={[
                  styles.radioButton,
                  selectedPaymentMethod === method.id &&
                    styles.radioButtonSelected
                ]}
              >
                {selectedPaymentMethod === method.id && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.buttonContainer}>
          <FullSizeButton
            text="Selecionar pagamento"
            onPress={handleContinue}
            disabled={!selectedPaymentMethod}
          />
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PaymentSelection;
