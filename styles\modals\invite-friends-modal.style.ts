import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24,
  },
  headerTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 600,
    lineHeight: 24,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    alignItems: "center",
    justifyContent: "center",
  },
  closeButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 20,
    fontWeight: 400,
  },
  successContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.success50,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    gap: 12,
  },
  successText: {
    color: stylesConstants.colors.success700,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    flex: 1,
  },
  content: {
    flex: 1,
  },
  description: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginBottom: 32,
  },
  formContainer: {
    gap: 20,
  },
  buttonContainer: {
    gap: 16,
    marginTop: 32,
  },
});

export default styles;
