import React from "react";
import {View, Text} from "react-native";
import {useTranslation} from "react-i18next";
import styles from "../../styles/components/product-page/details-card.style";

interface ProductDetail {
    label: string;
    value: string;
}

const DetailsCard: React.FC = () => {
    const {t} = useTranslation();

    const productDetails: ProductDetail[] = [
        {label: "Autor", value: "Arbitralis"},
        {label: "Categoria", value: "Arquitetura"},
        {label: "Formato do arquivo", value: "PDF"},
        {label: "Tamanho do arquivo", value: "25 mb"},
        {label: "Data de publicação", value: "15/05/2025"},
        {label: "Tamanho do arquivo", value: "25 mb"},
        {label: "Informações adicionais", value: "Sem informações adicionais"}
    ];

    return (
        <View style={styles.container}>
            <Text style={styles.title}>{t("products.detailsTitle")}</Text>
            <View style={styles.table}>
                {productDetails.map((detail, index) => (
                    <View
                        key={index + 1}
                        style={[
                            styles.row,
                            index % 2 !== 1 && styles.alternateRow
                        ]}
                    >
                        <Text style={styles.label}>{detail.label}</Text>
                        <Text style={styles.value}>{detail.value}</Text>
                    </View>
                ))}
            </View>
        </View>
    );
};

export default DetailsCard;
