import React from "react";
import { render } from "@testing-library/react-native";

jest.doMock("react-native-svg", () => {
  const { View } = require("react-native");
  const Mock = (p: any) => <View {...p} />;
  return { __esModule: true, default: Mock, Path: Mock };
});

import Small<PERSON>ogo from "../../../components/logos/small-logo";

describe("SmallLogo", () => {
  it("renders", () => {
    const { toJSON, getByTestId } = render(<SmallLogo testID="logo" />);
    expect(getByTestId("logo")).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it("forwards props", () => {
    const { getByTestId } = render(<SmallLogo width={50} height={70} testID="logo" />);
    const svg = getByTestId("logo");
    expect(svg.props.width).toBe(50);
    expect(svg.props.height).toBe(70);
  });
}); 