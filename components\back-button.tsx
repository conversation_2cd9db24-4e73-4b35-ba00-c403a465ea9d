import React, {useCallback} from "react";
import {StyleProp, Text, TouchableOpacity, ViewStyle} from "react-native";
import styles from "../styles/components/back-button.style";
import ChevronLeftIcon from "./icons/chevron-left-icon";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";

export interface BackButtonProps {
    style?: StyleProp<ViewStyle>;
    onPress?: () => void;
}

const BackButton: React.FC<BackButtonProps> = (props) => {
    const {t} = useTranslation();
    const router = useRouter();

    const onBackButtonPress = useCallback(() => {
        if(props.onPress) {
            props.onPress();
        } else {
            router.back();
        }
    }, [router, props.onPress]);

    return (
        <TouchableOpacity style={[styles.container, props.style]} onPress={onBackButtonPress}>
            <ChevronLeftIcon/>
            <Text style={styles.text}>{t("components.backButton")}</Text>
        </TouchableOpacity>
    );
};

export default BackButton;