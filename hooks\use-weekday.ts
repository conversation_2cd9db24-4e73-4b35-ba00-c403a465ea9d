import {useEffect, useMemo, useState} from "react";
import {getCurrentLocalization} from "../utils/i18n";
import {capitalize} from "../utils/string";

function useWeekday(date: Date) {
    const [locale, setLocale] = useState<string>();

    useEffect(() => {
        getCurrentLocalization().then((locale) => setLocale(locale));
    }, []);

    return useMemo(() => {
        const formatedWeekday = new Intl.DateTimeFormat(locale, {weekday: "long"})
            .format(date);
        return capitalize(formatedWeekday.slice(0, 3));
    }, [date, locale]);
}

export default useWeekday;