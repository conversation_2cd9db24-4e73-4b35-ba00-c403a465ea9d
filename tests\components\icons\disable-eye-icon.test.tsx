import React from "react";
import { render } from "@testing-library/react-native";

jest.mock("react-native-svg", () => {
  const Svg = ({ children, ...rest }: any) => <svg {...rest}>{children}</svg>;
  const Path = (props: any) => <path {...props} />;
  return {
    __esModule: true,
    default: Svg,
    Svg,
    Path
  };
});

import DisableEyeIcon from "../../../components/icons/disable-eye-icon";

describe("DisableEyeIcon", () => {
  it("renders without crashing", () => {
    const { toJSON } = render(<DisableEyeIcon />);
    expect(toJSON()).not.toBeUndefined();
  });

  it("accepts custom props", () => {
    const { getByTestId } = render(<DisableEyeIcon width={24} height={24} testID="icon" />);
    const icon = getByTestId("icon");
    expect(icon.props.width).toBe(24);
    expect(icon.props.height).toBe(24);
  });
});
