import React from "react";
process.env.EXPO_PUBLIC_REGISTER_URL = "https://example.com";
import {render, fireEvent} from "@testing-library/react-native";
let Login: any;

const mockLogin = jest.fn();
jest.mock("../hooks/use-login", () => () => ({
  login: (...args: any[]) => mockLogin(...args),
  errors: {}
}));

const mockSendRecoveryEmail = jest.fn();
jest.mock("../hooks/use-forget-password", () => () => ({
  sendRecoveryEmail: (...args: any[]) => mockSendRecoveryEmail(...args),
  resetPassword: jest.fn(),
  errors: {}
}));

const mockExists = jest.fn();
jest.mock("../services/user.service", () => ({
  __esModule: true,
  default: {
    exists: (...args: any[]) => mockExists(...args)
  }
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

jest.mock("react-native-reanimated", () =>
  require("react-native-reanimated/mock")
);

jest.mock("expo-web-browser", () => ({
  openBrowserAsync: jest.fn()
}));

jest.mock("expo-router", () => ({
  useRouter: () => ({
    navigate: jest.fn(),
    replace: jest.fn()
  })
}));

jest.mock("../components/logos/background-logo-texture", () => () => null);
jest.mock("../components/screen", () => {
  return ({children}: any) => <>{children}</>;
});

describe("Login screen", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.EXPO_PUBLIC_REGISTER_URL = "https://example.com";
    Login = require("../app/(auth)/login").default;
  });

  it("calls login action with form values", () => {
    mockExists.mockReturnValue({
      subscribe: ({next}: any) => next({exists: true})
    });

    const {getByPlaceholderText, getByText} = render(<Login />);

    fireEvent.changeText(
      getByPlaceholderText("login.fields.documentPlaceholder"),
      "12345678901"
    );
    fireEvent.press(getByText("login.nextButton"));

    fireEvent.changeText(
      getByPlaceholderText("login.fields.passwordPlaceholder"),
      "password123"
    );
    fireEvent.press(getByText("login.nextButton"));

    expect(mockLogin).toHaveBeenCalledWith(
      expect.objectContaining({
        document: "123.456.789-01",
        password: "password123"
      })
    );
  });

  it("sends recovery email", () => {
    mockExists.mockReturnValue({
      subscribe: ({next}: any) => next({exists: true})
    });

    const {getByPlaceholderText, getByText} = render(<Login />);

    fireEvent.changeText(
      getByPlaceholderText("login.fields.documentPlaceholder"),
      "12345678901"
    );
    fireEvent.press(getByText("login.nextButton"));

    fireEvent.press(getByText("login.forgotPassword"));

    expect(mockSendRecoveryEmail).toHaveBeenCalledWith({
      document: "123.456.789-01"
    });
  });

  it("renders register button text", () => {
    const {getByText} = render(<Login />);
    expect(getByText("login.register.button")).toBeTruthy();
  });

  it("shows upsell stage when user doesn't exist", () => {
    mockExists.mockReturnValue({
      subscribe: ({next}: any) => next({exists: false})
    });

    const {getByPlaceholderText, getByText} = render(<Login />);

    fireEvent.changeText(
      getByPlaceholderText("login.fields.documentPlaceholder"),
      "12345678901"
    );
    fireEvent.press(getByText("login.nextButton"));

    expect(
      getByPlaceholderText("login.fields.fullNamePlaceholder")
    ).toBeTruthy();
    expect(getByPlaceholderText("login.fields.phonePlaceholder")).toBeTruthy();
    expect(getByText("login.access")).toBeTruthy();
  });

  it("handles user service error", () => {
    mockExists.mockReturnValue({
      subscribe: ({error}: any) => error(new Error("Service error"))
    });

    const {getByPlaceholderText, getByText} = render(<Login />);

    fireEvent.changeText(
      getByPlaceholderText("login.fields.documentPlaceholder"),
      "12345678901"
    );
    fireEvent.press(getByText("login.nextButton"));

    expect(
      getByPlaceholderText("login.fields.documentPlaceholder")
    ).toBeTruthy();
  });

  it("shows validation error for invalid document", () => {
    const {getByPlaceholderText, getByText} = render(<Login />);

    fireEvent.changeText(
      getByPlaceholderText("login.fields.documentPlaceholder"),
      "123"
    );
    fireEvent.press(getByText("login.nextButton"));

    expect(mockExists).not.toHaveBeenCalled();
    expect(
      getByPlaceholderText("login.fields.documentPlaceholder")
    ).toBeTruthy();
  });

  it("can go back to initial stage from upsell", () => {
    mockExists.mockReturnValue({
      subscribe: ({next}: any) => next({exists: false})
    });

    const {getByPlaceholderText, getByText} = render(<Login />);

    fireEvent.changeText(
      getByPlaceholderText("login.fields.documentPlaceholder"),
      "12345678901"
    );
    fireEvent.press(getByText("login.nextButton"));

    expect(
      getByPlaceholderText("login.fields.fullNamePlaceholder")
    ).toBeTruthy();

    fireEvent.press(getByText("login.goToStart"));

    expect(
      getByPlaceholderText("login.fields.documentPlaceholder")
    ).toBeTruthy();
  });
});
