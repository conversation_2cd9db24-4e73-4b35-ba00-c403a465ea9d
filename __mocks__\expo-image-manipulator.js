// Mock for expo-image-manipulator
export const useImageManipulator = jest.fn(() => ({
  rotate: jest.fn().mockReturnThis(),
  resize: jest.fn().mockReturnThis(),
  renderAsync: jest.fn().mockResolvedValue({
    saveAsync: jest.fn().mockResolvedValue({
      uri: "mocked-manipulated-image-uri",
      width: 100,
      height: 100,
    })
  }),
  reset: jest.fn()
}));

export const ImageResult = {};

export const SaveFormat = {
  JPEG: "jpeg",
  PNG: "png",
  WEBP: "webp"
};

export const FlipType = {
  Horizontal: "horizontal",
  Vertical: "vertical"
};

export const RotateType = {
  Rotate90: 90,
  Rotate180: 180,
  Rotate270: 270
}; 