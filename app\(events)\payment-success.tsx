import React from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import {router, useLocalSearchParams} from "expo-router";
// Usando estilos inline para manter compatibilidade

interface PurchasedProduct {
  id: string;
  name: string;
  seller: string;
  category: string;
  date: string;
  time: string;
  ticketNumber: string;
  value: string;
}

const PaymentSuccess: React.FC = () => {
  const {t} = useTranslation();
  const params = useLocalSearchParams();
  const paymentType = (params.paymentType as string) || "pix";

  // Dados do cartão de crédito se aplicável
  const cardData = params.cardData
    ? JSON.parse(decodeURIComponent(params.cardData as string))
    : null;

  // Dados simulados baseados na imagem de referência
  const purchasedProducts: PurchasedProduct[] = [
    {
      id: "1",
      name: "E-book - Tendências de arquitetura em 2025",
      seller: "Amplify",
      category: "Arquitetura",
      date: "Disponível imediatamente",
      time: "Acesso vitalício",
      ticketNumber: "5565166616914",
      value: "R$ 150,00"
    }
  ];

  const getPaymentInfo = () => {
    switch (paymentType) {
      case "boleto":
        return {
          method: "Boleto",
          status: "À Vista",
          totalValue: "R$ 150,00"
        };
      case "credit-card":
        if (cardData) {
          const installmentText =
            cardData.installments === 1
              ? "À Vista"
              : `${cardData.installments}x de R$ ${cardData.installmentValue
                  .toFixed(2)
                  .replace(".", ",")}`;

          return {
            method: `Cartão de crédito`,
            status: installmentText,
            totalValue: `R$ ${cardData.totalValue.toFixed(2).replace(".", ",")}`
          };
        }
        return {
          method: "Cartão de crédito",
          status: "À Vista",
          totalValue: "R$ 150,00"
        };
      case "pix":
      default:
        return {
          method: "PIX",
          status: "À Vista",
          totalValue: "R$ 150,00"
        };
    }
  };

  const paymentInfo = getPaymentInfo();

  const handleViewEvents = () => {
    router.push("/(tabs)/schedule");
  };

  const handleBackToProducts = () => {
    router.push("/(tabs)/products");
  };

  return (
    <ScreenWithHeader screenTitle="Pagamento efetuado com sucesso!" backButton>
      <ScrollView
        style={{flex: 1, paddingHorizontal: 24}}
        showsVerticalScrollIndicator={false}
      >
        {/* Success Icon and Message */}
        <View style={{alignItems: "center", marginBottom: 32}}>
          <View
            style={{
              width: 80,
              height: 80,
              backgroundColor: "#0F7C4D",
              borderRadius: 40,
              justifyContent: "center",
              alignItems: "center",
              marginBottom: 16
            }}
          >
            <Text style={{fontSize: 40, color: "#fff"}}>✓</Text>
          </View>

          <Text
            style={{
              color: "#fff",
              fontSize: 18,
              fontWeight: "600",
              lineHeight: 24,
              textAlign: "center",
              marginBottom: 8
            }}
          >
            Pagamento efetuado com sucesso!
          </Text>
          <Text
            style={{
              color: "#ccc",
              fontSize: 14,
              fontWeight: "400",
              lineHeight: 20,
              textAlign: "center"
            }}
          >
            {paymentType === "boleto"
              ? "O e-book está disponível para você baixar."
              : paymentType === "credit-card"
              ? "Enviaremos um e-mail para você com as informações do evento."
              : "Enviaremos um e-mail para você com as informações dos produtos."}
          </Text>
        </View>

        {/* Produtos adquiridos */}
        <View
          style={{
            backgroundColor: "#202938",
            borderRadius: 8,
            padding: 16,
            marginBottom: 24,
            borderWidth: 1,
            borderColor: "#44445A"
          }}
        >
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 16
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: 16,
                fontWeight: "600",
                lineHeight: 24,
                marginBottom: 16
              }}
            >
              Produtos adquiridos
            </Text>
            <Text style={{color: "#ccc", fontSize: 14}}>1 / 2 &gt;</Text>
          </View>

          {purchasedProducts.map((product) => (
            <View key={product.id} style={{marginBottom: 16}}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginBottom: 12
                }}
              >
                <View
                  style={{
                    width: 40,
                    height: 40,
                    backgroundColor: "#1F2238",
                    borderRadius: 8,
                    justifyContent: "center",
                    alignItems: "center",
                    marginRight: 12
                  }}
                >
                  <Text style={{fontSize: 16}}>📄</Text>
                </View>

                <View style={{flex: 1}}>
                  <Text
                    style={{
                      color: "#fff",
                      fontSize: 14,
                      fontWeight: "600",
                      lineHeight: 20,
                      marginBottom: 2
                    }}
                  >
                    {product.name}
                  </Text>
                  <Text
                    style={{
                      color: "#ccc",
                      fontSize: 12,
                      fontWeight: "400",
                      lineHeight: 16
                    }}
                  >
                    Vendido por: {product.seller}
                  </Text>
                </View>
              </View>

              <View style={{paddingLeft: 52}}>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    marginBottom: 4
                  }}
                >
                  <Text
                    style={{
                      color: "#ccc",
                      fontSize: 12,
                      fontWeight: "400",
                      lineHeight: 16
                    }}
                  >
                    Data / hora do evento
                  </Text>
                  <Text
                    style={{
                      color: "#fff",
                      fontSize: 12,
                      fontWeight: "400",
                      lineHeight: 16
                    }}
                  >
                    {product.date}
                  </Text>
                </View>

                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    marginBottom: 4
                  }}
                >
                  <Text
                    style={{
                      color: "#ccc",
                      fontSize: 12,
                      fontWeight: "400",
                      lineHeight: 16
                    }}
                  >
                    Duração
                  </Text>
                  <Text
                    style={{
                      color: "#fff",
                      fontSize: 12,
                      fontWeight: "400",
                      lineHeight: 16
                    }}
                  >
                    {product.time}
                  </Text>
                </View>

                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    marginBottom: 4
                  }}
                >
                  <Text
                    style={{
                      color: "#ccc",
                      fontSize: 12,
                      fontWeight: "400",
                      lineHeight: 16
                    }}
                  >
                    Qtd. Ingressos
                  </Text>
                  <Text
                    style={{
                      color: "#fff",
                      fontSize: 12,
                      fontWeight: "400",
                      lineHeight: 16
                    }}
                  >
                    1 (um) ingresso(s)
                  </Text>
                </View>

                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between"
                  }}
                >
                  <Text
                    style={{
                      color: "#ccc",
                      fontSize: 12,
                      fontWeight: "400",
                      lineHeight: 16
                    }}
                  >
                    Valor do item
                  </Text>
                  <Text
                    style={{
                      color: "#fff",
                      fontSize: 12,
                      fontWeight: "400",
                      lineHeight: 16
                    }}
                  >
                    {product.value}
                  </Text>
                </View>
              </View>

              <TouchableOpacity
                style={{
                  backgroundColor: "#1F2238",
                  borderRadius: 6,
                  padding: 8,
                  alignItems: "center",
                  marginTop: 12,
                  flexDirection: "row",
                  justifyContent: "center"
                }}
              >
                <Text style={{color: "#fff", fontSize: 12, marginRight: 8}}>
                  {paymentType === "boleto" ? "📥" : "📧"}
                </Text>
                <Text
                  style={{
                    color: "#fff",
                    fontSize: 12,
                    fontWeight: "500",
                    lineHeight: 16
                  }}
                >
                  {paymentType === "boleto"
                    ? "Baixar e-book"
                    : paymentType === "credit-card"
                    ? "Ver ingresso"
                    : "Ver ingresso"}
                </Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>

        {/* Informações de pagamento */}
        <View
          style={{
            backgroundColor: "#202938",
            borderRadius: 8,
            padding: 16,
            marginBottom: 24,
            borderWidth: 1,
            borderColor: "#44445A"
          }}
        >
          <Text
            style={{
              color: "#fff",
              fontSize: 16,
              fontWeight: "600",
              lineHeight: 24,
              marginBottom: 16
            }}
          >
            Informações de pagamento
          </Text>

          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 8
            }}
          >
            <Text
              style={{
                color: "#ccc",
                fontSize: 14,
                fontWeight: "400",
                lineHeight: 20
              }}
            >
              Método de pagamento
            </Text>
            <Text
              style={{
                color: "#fff",
                fontSize: 14,
                fontWeight: "400",
                lineHeight: 20
              }}
            >
              {paymentType === "credit-card" && cardData
                ? `${cardData.brand.toUpperCase()} (com final ${cardData.number.slice(
                    -4
                  )})`
                : paymentInfo.method}
            </Text>
          </View>

          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 8
            }}
          >
            <Text
              style={{
                color: "#ccc",
                fontSize: 14,
                fontWeight: "400",
                lineHeight: 20
              }}
            >
              {paymentType === "credit-card"
                ? "Qtd. parcelas"
                : "Status do pago"}
            </Text>
            <Text
              style={{
                color: "#fff",
                fontSize: 14,
                fontWeight: "400",
                lineHeight: 20
              }}
            >
              {paymentInfo.status}
            </Text>
          </View>

          {paymentType === "credit-card" && cardData && (
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 8
              }}
            >
              <Text
                style={{
                  color: "#ccc",
                  fontSize: 14,
                  fontWeight: "400",
                  lineHeight: 20
                }}
              >
                Subtotal (2 itens)
              </Text>
              <Text
                style={{
                  color: "#fff",
                  fontSize: 14,
                  fontWeight: "400",
                  lineHeight: 20
                }}
              >
                R${" "}
                {(
                  (cardData.totalValue / cardData.installments) *
                  cardData.installments
                )
                  .toFixed(2)
                  .replace(".", ",")}
              </Text>
            </View>
          )}

          <View
            style={{
              borderTopWidth: 1,
              borderTopColor: "#44445A",
              paddingTop: 12,
              marginTop: 12,
              flexDirection: "row",
              justifyContent: "space-between"
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: 16,
                fontWeight: "600",
                lineHeight: 24
              }}
            >
              Soma total
            </Text>
            <Text
              style={{
                color: "#0F7C4D",
                fontSize: 18,
                fontWeight: "700",
                lineHeight: 26
              }}
            >
              {paymentInfo.totalValue}
            </Text>
          </View>
        </View>

        {/* Buttons */}
        <View style={{gap: 16, marginBottom: 24}}>
          <FullSizeButton
            text="Ver eventos agendados"
            onPress={handleViewEvents}
          />

          <TouchableOpacity
            style={{
              padding: 16,
              alignItems: "center",
              borderRadius: 8,
              borderWidth: 1,
              borderColor: "#44445A"
            }}
            onPress={handleBackToProducts}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: 16,
                fontWeight: "600",
                lineHeight: 24
              }}
            >
              Voltar aos produtos
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PaymentSuccess;
