import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface CameraIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const CameraIcon: React.FC<CameraIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#FCFCFD", [props.replaceColor]);

    return (
        <Svg
            width={48}
            height={48}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M38 34V18a2 2 0 0 0-2-2h-6l-2-4h-8l-2 4h-6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h24a2 2 0 0 0 2-2Z"
            />
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M24 30a6 6 0 1 0 0-12 6 6 0 0 0 0 12Z"
            />
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M32 20h2"
            />
        </Svg>
    );
};

export default CameraIcon;
