import React from "react";
import { render } from "@testing-library/react-native";

jest.mock("react-native-svg", () => {
  const Svg = ({ children, ...rest }: any) => <svg {...rest}>{children}</svg>;
  const Path = (props: any) => <path {...props} />;
  return {
    __esModule: true,
    default: Svg,
    Svg,
    Path
  };
});

import CheckCircleIcon from "../../../components/icons/check-circle-icon";

describe("CheckCircleIcon", () => {
  it("renders with default size", () => {
    const { toJSON } = render(<CheckCircleIcon />);
    expect(toJSON()).not.toBeUndefined();
  });

  it("accepts custom width and height", () => {
    const { getByTestId } = render(<CheckCircleIcon width={40} height={40} testID="icon" />);
    const icon = getByTestId("icon");
    expect(icon.props.width).toBe(40);
    expect(icon.props.height).toBe(40);
  });
}); 