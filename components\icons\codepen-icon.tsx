import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const CodepenIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={21}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M10.083 7.5 3.47 11.799c-.573.372-.86.559-.96.795a.833.833 0 0 0 0 .645m7.573-5.74 6.614 4.3c.573.372.86.559.959.795a.834.834 0 0 1 0 .645M10.083 7.5V2.084m0 10.417L3.47 8.2c-.573-.372-.86-.559-.96-.795a.833.833 0 0 1 0-.646m7.573 5.74L16.697 8.2c.573-.372.86-.559.959-.795a.834.834 0 0 0 0-.646m-7.573 5.74v5.417m7.727-4.606-7 4.55c-.263.17-.395.256-.536.29a.833.833 0 0 1-.381 0c-.142-.034-.273-.12-.536-.29l-7-4.55c-.222-.144-.333-.216-.413-.312a.834.834 0 0 1-.157-.29c-.037-.12-.037-.252-.037-.516V7.807c0-.265 0-.397.037-.516A.833.833 0 0 1 1.944 7c.08-.096.191-.168.413-.312l7-4.55c.263-.171.394-.257.536-.29a.833.833 0 0 1 .38 0c.143.033.274.119.537.29l7 4.55c.222.144.332.216.413.312a.833.833 0 0 1 .157.29c.037.12.037.251.037.516v4.386c0 .264 0 .397-.037.516a.834.834 0 0 1-.157.29c-.08.096-.191.168-.413.312Z"
            />
        </Svg>
    );
};

export default CodepenIcon;