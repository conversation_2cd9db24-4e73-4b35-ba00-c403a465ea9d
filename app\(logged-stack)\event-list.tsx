import React, {useCallback, useState} from "react";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import Search from "../../components/search";
import styles from "../../styles/logged-stack/event-list.style";
import {ScrollView, Text} from "react-native";
import CategoryItem from "../../components/events-screen/category-item";
import HandshakeIcon from "../../components/icons/handshake-icon";
import BuildingIcon from "../../components/icons/building-icon";
import CodepenIcon from "../../components/icons/codepen-icon";
import TargetIcon from "../../components/icons/target-icon";
import ChartBreakoutIcon from "../../components/icons/chart-breakout-icon";
import EventItem from "../../components/events-screen/event-item";

const EventList: React.FC = () => {
    const {t} = useTranslation();
    const [searchTerm, setSearchTerm] = useState<string>("");

    const handleSearchChange = useCallback((value: string) => setSearchTerm(value), []);

    return (
        <ScreenWithHeader screenTitle={t("eventList.title")} backButton>
            <Search searchBarValue={searchTerm} onSearchBarChange={handleSearchChange} style={styles.search}/>
            <Text style={styles.categoriesText}>{t("eventList.category")}</Text>
            <ScrollView nestedScrollEnabled
                        scrollEnabled
                        style={styles.categoryList}
                        horizontal
                        contentContainerStyle={styles.categoriesListInternalContainer}>
                <CategoryItem icon={<HandshakeIcon/>} name={t("eventList.categories.business")}/>
                <CategoryItem icon={<BuildingIcon/>} name={t("eventList.categories.startups")}/>
                <CategoryItem icon={<CodepenIcon/>} name={t("eventList.categories.technology")}/>
                <CategoryItem icon={<TargetIcon/>} name={t("eventList.categories.marketing")}/>
                <CategoryItem icon={<ChartBreakoutIcon/>} name={t("eventList.categories.sustainability")}/>
            </ScrollView>
            <Text style={styles.sponsoredTitle}>{t("eventList.sponsoredEvents")}</Text>
            <EventItem/>
        </ScreenWithHeader>
    );
};

export default EventList;