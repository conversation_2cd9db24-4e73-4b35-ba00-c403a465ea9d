import React, {useCallback, useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import {ScrollView, View} from "react-native";
import OpportunityCard from "../../components/user/opportunity-card";
import UserHeader from "../../components/user/user-header";
import UserTabs from "../../components/user/user-tabs";
import styles from "../../styles/tabs/user.style";
import Seals from "../../components/user/seals";
import Objectives from "../../components/user/objectives";

enum UserTabsEnum {
    Opportunities = 1,
    ClaimedBadges = 2,
    AboutMe = 3
}

const User: React.FC = () => {
    const {t} = useTranslation();
    const [activeTab, setActiveTab] = useState(UserTabsEnum.Opportunities);
    const tabs = [
        {title: t("user.opportunities"), id: UserTabsEnum.Opportunities},
        {title: t("user.claimedBadges"), id: UserTabsEnum.ClaimedBadges},
        {title: t("user.aboutMe"), id: UserTabsEnum.AboutMe}
    ];

    const onTabChange = useCallback(
        (id: number) => {
            setActiveTab(id);
        },
        [activeTab]
    );

    const opportunities = useMemo(() => {
        const list = [];
        for (let i = 0; i < 10; i++) {
            list.push(
                <OpportunityCard
                    key={i + 1}
                    avatarUrl={
                        "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1e/Wolfgang-amadeus-mozart_1.jpg/1200px-Wolfgang-amadeus-mozart_1.jpg"
                    }
                    userName={"Mozart"}
                    createdAt={new Date("2023-10-01T12:00:00Z")}
                    description={
                        "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus volutpat imperdiet facilisis. Donec rhoncus lorem lacus, sed ornare nisi imperdiet vel. Nunc vel varius lectus, nec vehicula quam. Praesent vel diam vehicula urna mattis vestibulum eu sit amet urna. Cras sit amet urna risus. Cras nec ornare nisi, facilisis molestie metus. Nulla at pulvinar augue, rutrum ullamcorper tellus. Cras mattis dictum erat, et vestibulum libero lacinia a. Phasellus fringilla auctor nisi, non vehicula quam iaculis sed. Donec a quam sit amet ante dapibus bibendum. Mauris porttitor diam rutrum tortor elementum pellentesque. Nunc eget maximus lorem, sit amet interdum quam. Maecenas posuere velit sed quam eleifend aliquam. Nulla volutpat in justo vel sodales. "
                    }
                    value={1000}
                    imageUrl="https://upload.wikimedia.org/wikipedia/commons/0/01/Steinway_Vienna_002.JPG"
                />
            );
        }
        return list;
    }, []);

    const seals = useMemo(() => {
        const list = [];
        for (let i = 0; i < 8; i++) {
            list.push({
                title: `Ouro`
            });
        }
        return list;
    }, []);

    const content = useMemo(() => {
        switch (activeTab) {
            case UserTabsEnum.Opportunities:
                return (
                    <View style={styles.opportunitiesList}>
                        {opportunities}
                    </View>
                );
            case UserTabsEnum.ClaimedBadges:
                return (
                    <View style={styles.objectivesContainer}>
                        <Seals seals={seals} />
                        <Objectives />
                    </View>
                );
            case UserTabsEnum.AboutMe:
                return <></>;
        }
    }, [activeTab]);

    return (
        <ScrollView>
            <UserHeader />
            <View style={styles.mainContainer}>
                <UserTabs
                    tabs={tabs}
                    style={styles.tabsContainer}
                    onTabChange={onTabChange}
                />
                <View style={styles.contentContainer}>{content}</View>
            </View>
        </ScrollView>
    );
};
export default User;
