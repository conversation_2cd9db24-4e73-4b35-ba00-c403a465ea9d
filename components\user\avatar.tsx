import {Image} from "expo-image";
import React from "react";
import {View} from "react-native";
import styles from "../../styles/components/user/avatar.style";

export interface AvatarProps {
    url: string;
    size?: number;
    borderSize?: number;
}

const Avatar: React.FC<AvatarProps> = (props) => {
    const size = props.size ?? 56;
    const border = size + (props.borderSize ?? 8);

    return (
        <View
            style={[
                styles.container,
                {
                    width: border,
                    height: border
                }
            ]}
        >
            <Image
                style={[
                    styles.image,
                    {
                        width: size,
                        height: size
                    }
                ]}
                source={props.url}
            />
        </View>
    );
};

export default Avatar;
