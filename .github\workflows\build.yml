name: Build

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  test:
    name: Run tests
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v4
        
      - uses: actions/setup-node@v4
        with:
          node-version: 22

      - run: corepack enable

      - name: Install dependencies
        run: yarn install --frozen-lockfile --ignore-scripts

      - name: Run tests
        run: yarn test --coverage
          
  sonar:
    name: SonarQube
    runs-on: self-hosted

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - uses: sonarsource/sonarqube-scan-action@v4
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      # If you wish to fail your job when the Quality Gate is red, uncomment the
      # following lines. This would typically be used to fail a deployment.
      - uses: sonarsource/sonarqube-quality-gate-action@v1
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}