import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface AppRatingIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const AppRatingIcon: React.FC<AppRatingIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={18} height={18} viewBox="0 0 18 18" fill="none" {...props}>
      <Path
        d="M1.5 11.5C1.5 11.942 1.6756 12.366 1.98816 12.6785C2.30072 12.9911 2.72464 13.1667 3.16667 13.1667H13.1667L16.5 16.5V3.16667C16.5 2.72464 16.3244 2.30072 16.0118 1.98816C15.6993 1.67559 15.2754 1.5 14.8333 1.5H3.16667C2.72464 1.5 2.30072 1.67559 1.98816 1.98816C1.6756 2.30072 1.5 2.72464 1.5 3.16667V11.5Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M6.66667 5.25C6.95415 4.96299 7.34377 4.80179 7.75 4.80179C8.15623 4.80179 8.54585 4.96299 8.83333 5.25L9 5.5L9.25 5.25C9.40321 5.11868 9.58078 5.01882 9.77258 4.95612C9.96439 4.89343 10.1667 4.86913 10.3679 4.8846C10.569 4.90008 10.7652 4.95503 10.9452 5.04632C11.1251 5.13761 11.2853 5.26346 11.4167 5.41667C11.548 5.56988 11.6478 5.74745 11.7105 5.93925C11.7732 6.13105 11.7975 6.33332 11.7821 6.53452C11.7666 6.73571 11.7116 6.93189 11.6203 7.11184C11.5291 7.2918 11.4032 7.45201 11.25 7.58333L9 9.83333L6.75 7.58333C6 6.83333 6.08333 5.83333 6.66667 5.25Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default AppRatingIcon;
