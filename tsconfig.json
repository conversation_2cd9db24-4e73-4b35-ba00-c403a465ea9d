{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["./*"], "@/app/*": ["./app/*"], "@/components/*": ["./components/*"], "@/contexts/*": ["./contexts/*"], "@/hooks/*": ["./hooks/*"], "@/services/*": ["./services/*"], "@/models/*": ["./models/*"], "@/styles/*": ["./styles/*"], "@/utils/*": ["./utils/*"], "@/assets/*": ["./assets/*"], "@/locales/*": ["./locales/*"], "@/tests/*": ["./tests/*"]}}}