import React, {useCallback, useMemo, useState} from "react";
import {
  ColorValue,
  DimensionValue,
  StyleProp,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle
} from "react-native";
import styles from "../styles/components/input-field.style";
import stylesConstants from "../styles/styles-constants";
import {NativeSyntheticEvent} from "react-native/Libraries/Types/CoreEventTypes";
import {
  InputModeOptions,
  TextInputChangeEventData
} from "react-native/Libraries/Components/TextInput/TextInput";
import EyeIcon from "./icons/eye-icon";
import DisableEyeIcon from "./icons/disable-eye-icon";
import {useTranslation} from "react-i18next";

export interface InputFieldProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (value: string) => void;
  style?: StyleProp<ViewStyle>;
  icon?: (errorColor?: ColorValue) => React.ReactNode | React.JSX.Element;
  isPassword?: boolean;
  error?: string;
  maxLength?: number;
  inputMode?: InputModeOptions;
  backgroundColor?: ColorValue;
  multiline?: boolean;
  numberOfLines?: number;
  showCharacterCount?: boolean;
  height?: DimensionValue;
}

const InputField: React.FC<InputFieldProps> = (props) => {
  const {t} = useTranslation();
  const [hidePassword, setHidePassword] = useState<boolean>(
    props.isPassword ?? false
  );

  const characterCount = useMemo(() => {
    if (props.showCharacterCount && props.maxLength) {
      return `${props.value.length}/${props.maxLength} ${t(
        "components.inputField.characters"
      )}`;
    }
    return null;
  }, [props.showCharacterCount, props.maxLength, props.value.length, t]);

  const onChange = useCallback(
    (e: NativeSyntheticEvent<TextInputChangeEventData>) => {
      props.onChangeText(e.nativeEvent.text);
    },
    []
  );

  const onEyeIconPress = useCallback(() => {
    setHidePassword((x) => !x);
  }, []);

  const errorColor = useMemo(
    () => (props.error ? stylesConstants.colors.error300 : undefined),
    [props.error]
  );

  return (
    <View style={[styles.container, props.style]}>
      {props.label && (
        <Text style={[styles.label, props.error && styles.error]}>
          {props.label}
        </Text>
      )}
      <View
        style={[
          styles.field,
          props.error && styles.errorBorder,
          props.backgroundColor && {
            backgroundColor: props.backgroundColor
          },
          props.multiline && styles.multilineField,
          ...(props.height ? [{height: props.height}] : [])
        ]}
      >
        {props.icon?.(errorColor)}
        <TextInput
          placeholder={props.placeholder}
          style={[
            styles.inputText,
            props.error && styles.error,
            props.multiline && styles.multilineInput
          ]}
          value={props.value}
          onChange={onChange}
          autoCapitalize="none"
          autoCorrect={false}
          maxLength={props.maxLength}
          secureTextEntry={hidePassword}
          multiline={props.multiline}
          numberOfLines={props.numberOfLines}
          textAlignVertical={props.multiline ? "top" : "center"}
          inputMode={props.inputMode}
          autoComplete={props.isPassword ? "password" : "off"}
          textContentType={props.isPassword ? "password" : "none"}
          placeholderTextColor={
            props.error
              ? stylesConstants.colors.error300
              : stylesConstants.colors.gray100
          }
        />
        {props.isPassword && (
          <TouchableOpacity onPress={onEyeIconPress}>
            {hidePassword ? (
              <EyeIcon replaceColor={errorColor} />
            ) : (
              <DisableEyeIcon replaceColor={errorColor} />
            )}
          </TouchableOpacity>
        )}
      </View>
      {characterCount && (
        <Text style={[styles.characterCount]}>{characterCount}</Text>
      )}
      {props.error && (
        <Text style={[styles.error, styles.errorText]}>{props.error}</Text>
      )}
    </View>
  );
};

export default InputField;
