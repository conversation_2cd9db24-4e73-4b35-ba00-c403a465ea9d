import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    ticketContainer: {
        paddingVertical: 24,
        paddingHorizontal: 24,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        flexGrow: 1
    },
    ticketContent: {
        gap: 8,
        marginBottom: 16,
        zIndex: 1
    },
    ticketTitle: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 600,
        lineHeight: 20,
        textAlign: "center"
    },
    organizerContainer: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "center",
        gap: 4
    },
    organizerLabel: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18
    },
    organizerName: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 700,
        lineHeight: 18
    },
    ticketFooter: {
        flexDirection: "row",
        display: "flex",
        justifyContent: "space-between",
        marginBottom: 16,
        width: "100%",
        zIndex: 1
    },
    ticketInfo: {
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: 8
    },
    infoLabel: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18,
        textAlign: "center"
    },
    infoValue: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 600,
        lineHeight: 18,
        textAlign: "center"
    },
    detailsButton: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1
    },
    detailsButtonText: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: "700"
    }
});

export default styles;
