import React, {useEffect, useMemo} from "react";
import {Tabs, useRouter} from "expo-router";
import {useSession} from "@/contexts/session.context";
import {BottomTabNavigationOptions} from "@react-navigation/bottom-tabs";
import styles from "@/styles/tabs/layout.style";
import CalendarIcon from "@/components/icons/calendar-icon";
import {useTranslation} from "react-i18next";
import HomeIcon from "@/components/icons/home-icon";
import {Text} from "react-native";
import TabIcon from "@/components/tab-icon";
import ProductIcon from "@/components/icons/product-icon";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import UserIcon from "@/components/icons/user-icon";
import MessageChatSquareIcon from "@/components/icons/message-chat-square-icon";

// Extract tab label component to avoid inline definition
const TabLabel: React.FC<{focused: boolean; children: React.ReactNode}> = ({
  focused,
  children
}) => (
  <Text style={[styles.topBarLabel, focused && styles.tabBarLabelActive]}>
    {children}
  </Text>
);

// Extract tab icon components to avoid inline definitions
const HomeTabIcon: React.FC<{focused: boolean}> = ({focused}) => (
  <TabIcon active={focused}>
    <HomeIcon />
  </TabIcon>
);

const ScheduleTabIcon: React.FC<{focused: boolean}> = ({focused}) => (
  <TabIcon active={focused}>
    <CalendarIcon />
  </TabIcon>
);

const ProductsTabIcon: React.FC<{focused: boolean}> = ({focused}) => (
  <TabIcon active={focused}>
    <ProductIcon />
  </TabIcon>
);

const UserTabIcon: React.FC<{focused: boolean}> = ({focused}) => (
  <TabIcon active={focused}>
    <UserIcon />
  </TabIcon>
);

const MessagesTabIcon: React.FC<{focused: boolean}> = ({focused}) => (
  <TabIcon active={focused}>
    <MessageChatSquareIcon />
  </TabIcon>
);

// Memoized icon render functions to avoid recreating on every render
const renderHomeIcon = (props: {focused: boolean}) => (
  <HomeTabIcon focused={props.focused} />
);

const renderScheduleIcon = (props: {focused: boolean}) => (
  <ScheduleTabIcon focused={props.focused} />
);

const renderProductsIcon = (props: {focused: boolean}) => (
  <ProductsTabIcon focused={props.focused} />
);

const renderUserIcon = (props: {focused: boolean}) => (
  <UserTabIcon focused={props.focused} />
);

const renderMessagesIcon = (props: {focused: boolean}) => (
  <MessagesTabIcon focused={props.focused} />
);

const Layout: React.FC = () => {
  const session = useSession();
  const router = useRouter();
  const {t} = useTranslation();
  const insets = useSafeAreaInsets();

  useEffect(() => {
    if (!session) {
      router.replace("/(auth)/login");
    }
  }, [session, router]);

  const screenOptions: BottomTabNavigationOptions = useMemo(
    () => ({
      headerShown: false,
      tabBarStyle: [styles.tabBar, {marginBottom: insets.bottom}],
      tabBarLabelStyle: styles.topBarLabel,
      tabBarIconStyle: styles.topBarIcon,
      // eslint-disable-next-line react/no-unstable-nested-components
      tabBarLabel: (props) => (
        <TabLabel focused={props.focused}>{props.children}</TabLabel>
      ),
      sceneStyle: styles.contentStyle
    }),
    [insets.bottom]
  );

  return (
    <Tabs screenOptions={screenOptions}>
      <Tabs.Screen
        options={{
          title: t("tabBar.home"),
          tabBarIcon: renderHomeIcon
        }}
        name="home"
      />
      <Tabs.Screen
        options={{
          title: t("tabBar.schedule"),
          tabBarIcon: renderScheduleIcon
        }}
        name="schedule"
      />
      <Tabs.Screen
        options={{
          title: t("tabBar.products"),
          tabBarIcon: renderProductsIcon
        }}
        name="products"
      />
      <Tabs.Screen
        options={{
          title: t("tabBar.profile"),
          tabBarIcon: renderUserIcon
        }}
        name="user"
      />
      <Tabs.Screen
        options={{
          title: t("tabBar.messages"),
          tabBarIcon: renderMessagesIcon
        }}
        name="messages"
      />
    </Tabs>
  );
};

export default Layout;
