import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const CalendarPlusIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={24}
            height={24}
            fill="none"
            {...props}
        >
            <Path
                stroke="#36A67A"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 11.5V8.8c0-1.68 0-2.52-.327-3.162a3 3 0 0 0-1.311-1.311C18.72 4 17.88 4 16.2 4H7.8c-1.68 0-2.52 0-3.162.327a3 3 0 0 0-1.311 1.311C3 6.28 3 7.12 3 8.8v8.4c0 1.68 0 2.52.327 3.162a3 3 0 0 0 1.311 1.311C5.28 22 6.12 22 7.8 22h4.7M21 10H3m13-8v4M8 2v4m10 15v-6m-3 3h6"
            />
        </Svg>
    );
};

export default CalendarPlusIcon;
