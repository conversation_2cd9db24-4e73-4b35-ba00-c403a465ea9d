import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        display: "flex",
        flexDirection: "row",
        columnGap: 8,
        paddingVertical: 8,
        paddingHorizontal: 24,
        justifyContent: "center",
        alignItems: "center",
        gap: 8,
        alignSelf: "stretch",
        borderRadius: 8,
        borderWidth: 1,
        borderStyle: "solid",
        borderColor: stylesConstants.colors.brand.primary,
        backgroundColor: stylesConstants.colors.brand.primary
    },
    text: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontStyle: "normal",
        fontWeight: 700,
        lineHeight: 20
    },
    innerContainer: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        gap: 6
    },
    disable: {
        backgroundColor: stylesConstants.colors.gray900
    },
    disableBorder: {
        borderColor: "transparent",
    }
});

export default styles;
