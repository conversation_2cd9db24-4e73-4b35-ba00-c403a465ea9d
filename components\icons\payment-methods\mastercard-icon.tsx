import React from "react";
import Svg, {SvgProps, Path, Rect, Circle} from "react-native-svg";

const MastercardIcon: React.FC<SvgProps> = (props) => {
    const width = props.width ?? 34;
    const height = props.height ?? 24;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 34 24"
            fill="none"
            {...props}
        >
            <Rect width={33} height={23} x={0.5} y={0.5} fill="#fff" rx={3.5} />
            <Rect
                width={33}
                height={23}
                x={0.5}
                y={0.5}
                stroke="#F2F4F7"
                rx={3.5}
            />
            <Circle fill="#EB001B" cx="13" cy="12" r="5.5" />
            <Circle fill="#F79E1B" cx="21" cy="12" r="5.5" />
            <Path
                fill="#FF5F00"
                d="M20 12c0-1.8-0.9-3.4-2.3-4.3-1.4 1-2.3 2.6-2.3 4.3s0.9 3.4 2.3 4.3c1.4-0.9 2.3-2.5 2.3-4.3z"
            />
        </Svg>
    );
};

export default MastercardIcon;
