import {useCallback, useState} from "react";
import {
  ForgetPasswordRequest,
  ForgetPasswordRequestSchema,
  ResetPasswordRequest,
  ResetPasswordRequestSchema
} from "@/models/login";
import {formatZodError} from "@/utils/zod-utils";
import {useTranslation} from "react-i18next";
import LoginService from "@/services/login.service";
import {useRouter} from "expo-router";
import {RecoveryPasswordParams} from "@/app/(auth)/password-recovery/recovery-password";
import {ErrorType, useErrorMessage} from "@/contexts/error-dialog-context";
import {useLoading} from "@/contexts/loading-context";

function useForgetPassword() {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const errorAction = useErrorMessage();
  const loadingAction = useLoading();
  const router = useRouter();
  const {t} = useTranslation();

  const sendRecoveryEmail = useCallback(
    (request: ForgetPasswordRequest) => {
      const validation = ForgetPasswordRequestSchema.safeParse(request);
      if (!validation.success) {
        setErrors(formatZodError(validation.error));
        errorAction.emitError({
          errorType: ErrorType.Warning,
          title: t("errors.requiredField"),
          description: t("errors.forgetPasswordDescription")
        });
        return;
      }

      setErrors({});

      loadingAction.setCurrentLoading?.(true);
      LoginService.forgetPassword(request).subscribe({
        next: () => {
          router.navigate({
            pathname: "/recovery-password",
            params: {
              document: request.document
            } as RecoveryPasswordParams
          });
        },
        error: () => {
          errorAction.emitError({
            errorType: ErrorType.Error,
            title: t("errors.emptyFields"),
            description: t("errors.tryLater")
          });
          loadingAction.setCurrentLoading?.(false);
        },
        complete: () => {
          loadingAction.setCurrentLoading?.(false);
        }
      });
    },
    [errorAction, t, loadingAction]
  );

  const resetPassword = useCallback(
    (request: ResetPasswordRequest) => {
      const validation = ResetPasswordRequestSchema.safeParse(request);
      if (!validation.success) {
        errorAction.emitError({
          errorType: ErrorType.Warning,
          title: t("errors.passwordNotSecure"),
          description: t("errors.passwordNotSecureDescription")
        });
        setErrors({
          password: t("errors.passwordMinimumLength")
        });
        return;
      }

      setErrors({});

      loadingAction.setCurrentLoading?.(true);
      LoginService.resetPassword(request).subscribe({
        next: () => {
          router.replace("/(auth)/password-recovery/recovery-password-success");
        },
        error: () => {
          errorAction.emitError({
            errorType: ErrorType.Error,
            title: t("errors.emptyFields"),
            description: t("errors.tryLater")
          });
          loadingAction.setCurrentLoading?.(false);
        },
        complete: () => {
          loadingAction.setCurrentLoading?.(false);
        }
      });
    },
    [errorAction, t, loadingAction]
  );

  return {
    sendRecoveryEmail,
    resetPassword,
    errors
  };
}

export default useForgetPassword;
