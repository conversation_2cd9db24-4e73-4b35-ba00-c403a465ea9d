import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    display: "flex",
    position: "absolute",
    zIndex: 9999,
    margin: 24,
    flexDirection: "row",
    alignItems: "flex-start",
    alignSelf: "stretch",
    backgroundColor: stylesConstants.colors.error.background,
    borderRadius: 12
  },
  linear: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    borderColor: stylesConstants.colors.error.border,
    borderStyle: "solid",
    borderWidth: 1
  },
  iconsContainer: {
    flexDirection: "row",
    display: "flex",
    justifyContent: "space-between"
  },
  icon: {
    marginLeft: -10,
    marginBottom: 7
  },
  closeButton: {
    marginTop: 10,
    marginRight: 8
  },
  text: {
    color: stylesConstants.colors.secondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontStyle: "normal",
    fontWeight: 400,
    lineHeight: 20
  },
  title: {
    fontWeight: 700,
    color: stylesConstants.colors.fullWhite,
    marginBottom: 4
  }
});

export default styles;
