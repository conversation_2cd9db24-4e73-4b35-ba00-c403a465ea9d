import React from "react";
import {render, act} from "@testing-library/react-native";
import {View} from "react-native";

const mockSetPage = jest.fn();

jest.doMock("react-native-pager-view", () => {
  interface MockPagerViewProps {
    children: React.ReactNode;
    initialPage?: number;
    onPageScroll?: (event: {
      nativeEvent: {position: number; offset: number};
    }) => void;
    style?: object;
    testID?: string;
  }
  const MockPagerView = React.forwardRef(
    (
      {children, initialPage, onPageScroll, style, testID}: MockPagerViewProps,
      ref
    ) => {
      React.useImperativeHandle(ref, () => ({setPage: mockSetPage}));
      return (
        <View
          testID={testID ?? "pager-view"}
          data-initialPage={initialPage}
          data-onPageScroll={onPageScroll}
        >
          {children}
        </View>
      );
    }
  );
  return {
    __esModule: true,
    default: MockPagerView
  };
});

jest.doMock("../styles/index.style", () => ({
  screenView: {flex: 1, backgroundColor: "mock-screen"}
}));

jest.doMock("../components/intro/slide", () => {
  interface MockSlideProps {
    id: number;
    imageLeftPosition?: number;
    image?: string;
  }
  const MockSlide = ({id, imageLeftPosition, image}: MockSlideProps) => (
    <View
      testID={`slide-${id}`}
      data-imageLeftPosition={imageLeftPosition}
      data-image={image}
    />
  );
  return {
    __esModule: true,
    default: MockSlide
  };
});

jest.doMock("../components/intro/overlay", () => {
  interface MockOverlayProps {
    currentSlide: number;
    texts: Array<Record<string, unknown>>;
    slideState: number;
    onChangeSlide: (slide: number) => void;
  }
  const MockOverlay = ({
    currentSlide,
    texts,
    slideState,
    onChangeSlide
  }: MockOverlayProps) => (
    <View
      testID="overlay"
      data-currentSlide={currentSlide}
      data-texts={JSON.stringify(texts)}
      data-slideState={slideState}
      data-onChangeSlide={onChangeSlide}
    />
  );
  return {
    __esModule: true,
    default: MockOverlay
  };
});

jest.doMock("../components/intro/overlay-top", () => {
  const MockOverlayTop = () => <View testID="overlay-top" />;
  return {
    __esModule: true,
    default: MockOverlayTop
  };
});

let IndexPage: React.FC;
beforeAll(() => {
  IndexPage = require("../app/indexo").default;
});

describe("<IndexPage />", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render initial components correctly", () => {
    const {getByTestId, getAllByTestId} = render(<IndexPage />);

    expect(getByTestId("pager-view")).toBeTruthy();
    expect(getAllByTestId(/slide-\d+/)).toHaveLength(3);
    expect(getByTestId("overlay-top")).toBeTruthy();
    expect(getByTestId("overlay")).toBeTruthy();

    const overlay = getByTestId("overlay");
    expect(overlay.props["data-currentSlide"]).toBe(0);
    expect(overlay.props["data-slideState"]).toBe(0);
    const texts = JSON.parse(overlay.props["data-texts"]);
    expect(texts).toHaveLength(3);
    expect(texts[0].title).toBe("intro.slide1.title");
  });

  it("should update overlay props on page scroll", async () => {
    const {getByTestId} = render(<IndexPage />);
    const pagerView = getByTestId("pager-view");
    const overlay = getByTestId("overlay");

    const scrollEventData = {nativeEvent: {position: 1, offset: 0.5}};

    await act(async () => {
      pagerView.props["data-onPageScroll"](scrollEventData);
    });

    expect(overlay.props["data-currentSlide"]).toBe(1);
    expect(overlay.props["data-slideState"]).toBe(0.5);
  });

  it("should call PagerView.setPage when onChangeSlide is triggered from Overlay", async () => {
    const {getByTestId} = render(<IndexPage />);
    const overlay = getByTestId("overlay");

    const targetSlide = 2;
    await act(async () => {
      overlay.props["data-onChangeSlide"](targetSlide);
    });

    expect(mockSetPage).toHaveBeenCalledWith(targetSlide);
    expect(mockSetPage).toHaveBeenCalledTimes(1);
  });
});
