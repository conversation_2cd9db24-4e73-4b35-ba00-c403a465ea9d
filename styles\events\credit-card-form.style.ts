import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 600,
    marginBottom: 8,
    textAlign: "center"
  },
  subtitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    textAlign: "center"
  },
  formContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 16
  },
  inputGroup: {
    marginBottom: 16
  },
  inputLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    marginBottom: 8
  },
  textInput: {
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderRadius: 8,
    padding: 12,
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    borderWidth: 1,
    borderColor: "transparent"
  },
  textInputFocused: {
    borderColor: stylesConstants.colors.brand.primary
  },
  textInputError: {
    borderColor: "#FF6B6B"
  },
  inputRow: {
    flexDirection: "row",
    gap: 12
  },
  inputHalf: {
    flex: 1
  },
  inputThird: {
    flex: 0.4
  },
  inputTwoThirds: {
    flex: 0.6
  },
  errorText: {
    color: "#FF6B6B",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    marginTop: 4
  },
  cardBrandContainer: {
    position: "absolute",
    right: 12,
    top: 12,
    bottom: 12,
    justifyContent: "center",
    alignItems: "center",
    width: 32
  },
  cardBrandText: {
    fontSize: 16
  },
  installmentsContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  installmentOption: {
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "transparent"
  },
  installmentOptionSelected: {
    borderColor: stylesConstants.colors.brand.primary,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  installmentOptionText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    flex: 1,
    marginLeft: 12
  },
  installmentOptionValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: stylesConstants.colors.borderDefault,
    justifyContent: "center",
    alignItems: "center"
  },
  radioButtonSelected: {
    borderColor: stylesConstants.colors.brand.primary
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: stylesConstants.colors.brand.primary
  },
  summaryContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8
  },
  summaryLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400
  },
  summaryValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    paddingTop: 12,
    marginTop: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  totalLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600
  },
  totalValue: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 700
  },
  buttonContainer: {
    gap: 16
  },
  securityInfo: {
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    flexDirection: "row",
    alignItems: "center"
  },
  securityIcon: {
    marginRight: 8,
    fontSize: 16
  },
  securityText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    flex: 1,
    lineHeight: 16
  },
  cardPreview: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    minHeight: 100,
    justifyContent: "space-between"
  },
  cardNumber: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: "monospace",
    fontSize: 18,
    fontWeight: 600,
    letterSpacing: 2,
    marginBottom: 8
  },
  cardInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end"
  },
  cardHolder: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    textTransform: "uppercase"
  },
  cardExpiry: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400
  },
  cardBrand: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    textTransform: "uppercase"
  }
});

export default styles;
