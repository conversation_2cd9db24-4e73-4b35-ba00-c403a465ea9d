{"buildFiles": ["C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\.cxx\\Debug\\5c14151w\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\.cxx\\Debug\\5c14151w\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fc98766b9298386f4aefafd293f4f926\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_pagerview::@7032a8921530ec438d60": {"artifactName": "react_codegen_pagerview", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fc98766b9298386f4aefafd293f4f926\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fc98766b9298386f4aefafd293f4f926\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"artifactName": "react_codegen_rnsvg", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\Debug\\5c14151w\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fc98766b9298386f4aefafd293f4f926\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "arm64-v8a", "runtimeFiles": []}}}