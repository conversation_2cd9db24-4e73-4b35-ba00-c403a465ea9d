import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface PinIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const PinIcon: React.FC<PinIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#F2F4F7", [props.replaceColor]);

    return (
        <Svg
            width={12}
            height={12}
            viewBox="0 0 12 12"
            fill="none"
            {...props}
        >
            <Path
                d="M6.00021 7.5L6.00021 11M4.00021 3.65407V4.71938C4.00021 4.82338 4.00021 4.87539 3.99001 4.92513C3.98095 4.96926 3.96597 5.01197 3.94547 5.05209C3.92237 5.0973 3.88988 5.13791 3.82491 5.21913L3.04002 6.20024C2.70715 6.61633 2.54072 6.82437 2.54053 6.99946C2.54036 7.15173 2.6096 7.29578 2.7286 7.39077C2.86544 7.5 3.13186 7.5 3.66471 7.5H8.33571C8.86856 7.5 9.13499 7.5 9.27183 7.39077C9.39083 7.29578 9.46007 7.15173 9.4599 6.99946C9.45971 6.82437 9.29328 6.61633 8.96041 6.20024L8.17552 5.21913C8.11055 5.13791 8.07806 5.0973 8.05496 5.05209C8.03446 5.01197 8.01948 4.96926 8.01042 4.92513C8.00021 4.87539 8.00021 4.82338 8.00021 4.71938V3.65407C8.00021 3.5965 8.00021 3.56772 8.00347 3.53934C8.00636 3.51412 8.01117 3.48917 8.01785 3.46468C8.02537 3.43712 8.03606 3.4104 8.05743 3.35695L8.56137 2.09711C8.70839 1.72957 8.7819 1.5458 8.75124 1.39827C8.72443 1.26927 8.64778 1.15605 8.53796 1.08325C8.41237 1 8.21444 1 7.81859 1H4.18184C3.78598 1 3.58806 1 3.46247 1.08325C3.35264 1.15605 3.276 1.26927 3.24919 1.39827C3.21853 1.5458 3.29204 1.72957 3.43906 2.09711L3.943 3.35695C3.96437 3.4104 3.97506 3.43712 3.98258 3.46468C3.98926 3.48917 3.99407 3.51412 3.99696 3.53934C4.00021 3.56772 4.00021 3.5965 4.00021 3.65407Z"
                stroke={color} stroke-linecap="round" stroke-linejoin="round"
            />
        </Svg>
    );
};

export default PinIcon; 