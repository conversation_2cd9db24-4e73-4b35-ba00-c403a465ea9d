import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput
} from "react-native";
import ScreenWithHeader from "@/components/screen-with-header";
import {useTranslation} from "react-i18next";
import styles from "@/styles/main/search.style";
import SearchIcon from "@/components/icons/search-icon";
import CloseIcon from "@/components/icons/close-icon";

interface SearchSuggestion {
  id: string;
  name: string;
  subtitle: string;
  avatar?: string;
}

const SearchScreen: React.FC = () => {
  const {t} = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [suggestions] = useState<SearchSuggestion[]>([
    {
      id: "1",
      name: "<PERSON>",
      subtitle: "Diretora de vendas"
    },
    {
      id: "2",
      name: "<PERSON>",
      subtitle: "Diretora de vendas"
    },
    {
      id: "3",
      name: "<PERSON>",
      subtitle: "Di<PERSON>or de vendas"
    },
    {
      id: "4",
      name: "5 anos - Tendências de arquitetura em 2024",
      subtitle: "Produtos"
    },
    {
      id: "5",
      name: "<PERSON> sobre Revolução Jurídica",
      subtitle: "Produtos"
    },
    {
      id: "6",
      name: "Terceiro anual de Empreendedores em Brasília e...",
      subtitle: "Produtos"
    }
  ]);

  const filteredSuggestions = suggestions.filter(
    (suggestion) =>
      suggestion.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      suggestion.subtitle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const handleRemoveSuggestion = (suggestionId: string) => {
    // In a real app, this would remove the suggestion from the list
    console.log("Remove suggestion:", suggestionId);
  };

  return (
    <ScreenWithHeader screenTitle={t("search.title", "Busca")} backButton>
      <View style={styles.contentContainer}>
        {/* Custom Search Input */}
        <View style={styles.searchInputContainer}>
          <SearchIcon width={20} height={20} />
          <TextInput
            style={styles.searchInput}
            value={searchTerm}
            onChangeText={setSearchTerm}
            placeholder={t(
              "search.placeholder",
              "Busca membros, eventos, etc..."
            )}
            placeholderTextColor="#8B8B8B"
            autoCapitalize="none"
            autoCorrect={false}
          />
          {searchTerm.length > 0 && (
            <TouchableOpacity
              onPress={handleClearSearch}
              style={styles.cancelButton}
            >
              <Text style={styles.cancelButtonText}>
                {t("search.cancel", "Cancelar")}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <ScrollView
          style={styles.resultsContainer}
          showsVerticalScrollIndicator={false}
        >
          {/* Show suggestions when there's search term and matches */}
          {searchTerm.length > 0 && filteredSuggestions.length > 0 && (
            <>
              <Text style={styles.recentSearchesTitle}>
                {t("search.recentSearches", "Pesquisas recentes")}
              </Text>
              {filteredSuggestions.map((suggestion) => (
                <View key={suggestion.id} style={styles.suggestionItem}>
                  <View style={styles.suggestionAvatar}>
                    <Text style={styles.suggestionAvatarText}>
                      {getInitials(suggestion.name)}
                    </Text>
                  </View>
                  <View style={styles.suggestionContent}>
                    <Text style={styles.suggestionTitle}>
                      {suggestion.name}
                    </Text>
                    <Text style={styles.suggestionSubtitle}>
                      {suggestion.subtitle}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => handleRemoveSuggestion(suggestion.id)}
                    style={styles.suggestionCloseButton}
                  >
                    <CloseIcon width={16} height={16} />
                  </TouchableOpacity>
                </View>
              ))}
            </>
          )}

          {/* Show no results when search term exists but no suggestions match */}
          {searchTerm.length > 0 && filteredSuggestions.length === 0 && (
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateTitle}>
                {t("search.noResults", "Nenhum resultado encontrado")}
              </Text>
              <Text style={styles.emptyStateText}>
                {t(
                  "search.noResultsDesc",
                  `Não encontramos nenhum resultado para "${searchTerm}" no aplicativo.`
                )}
              </Text>
            </View>
          )}

          {/* Show default state when no search term */}
          {searchTerm.length === 0 && (
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateTitle}>
                {t("search.placeholder", "O que você está procurando?")}
              </Text>
              <Text style={styles.emptyStateText}>
                {t(
                  "search.placeholderDesc",
                  "Busque por membros, eventos, benefícios e oportunidades"
                )}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>
    </ScreenWithHeader>
  );
};

export default SearchScreen;
