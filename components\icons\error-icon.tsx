import Svg, {Path, Rect, SvgProps} from "react-native-svg";
import React from "react";

const ErrorIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={38}
            height={38}
            fill="none"
            {...props}
        >
            <Rect
                width={26}
                height={26}
                x={6}
                y={6}
                stroke="#FC5556"
                strokeWidth={2}
                opacity={0.3}
                rx={13}
            />
            <Rect
                width={36}
                height={36}
                x={1}
                y={1}
                stroke="#FC5556"
                strokeWidth={2}
                opacity={0.1}
                rx={18}
            />
            <Path
                stroke="#FC5556"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.667}
                d="m16.34 11.664 2.192-.822c.173-.064.26-.097.349-.11a.835.835 0 0 1 .238 0c.09.013.176.046.35.11l4.467 1.676c.624.234.936.351 1.166.554.203.179.36.404.456.657.109.287.109.62.109 1.286V19c0 .6-.096 1.177-.266 1.726m-1.716 2.962c-1.405 1.634-3.195 2.795-4.102 3.324-.185.108-.278.162-.408.19a.95.95 0 0 1-.35 0c-.13-.028-.222-.082-.407-.19-1.623-.946-6.085-3.921-6.085-8.012v-5.186c0-.39.242-.738.606-.874M11.5 11.5l15 15"
            />
        </Svg>
    );
};

export default ErrorIcon;