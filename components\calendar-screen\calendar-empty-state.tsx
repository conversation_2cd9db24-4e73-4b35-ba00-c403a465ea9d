import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import CalendarIcon from "../icons/calendar-icon";
import styles from "../../styles/components/calendar-screen/calendar-empty-state.style";

export interface CalendarEmptyStateProps {
  onExploreEvents?: () => void;
}

const CalendarEmptyState: React.FC<CalendarEmptyStateProps> = ({
  onExploreEvents
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <CalendarIcon width={48} height={48} />
      </View>
      <Text style={styles.title}>Nenhum evento por aqui</Text>
      <Text style={styles.description}>
        Você ainda não reservou nenhum evento, confira o que há de melhor na
        cidade para você!
      </Text>
      <TouchableOpacity style={styles.button} onPress={onExploreEvents}>
        <Text style={styles.buttonText}>Explorar eventos</Text>
      </TouchableOpacity>
    </View>
  );
};

export default CalendarEmptyState;
