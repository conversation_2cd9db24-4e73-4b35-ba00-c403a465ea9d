import React from "react";
import { render } from "@testing-library/react-native";
import ChevronRightIcon from "../../../components/icons/chevron-right-icon";

jest.doMock("react-native-svg", () => {
    const { View } = require("react-native");

    type MockProps = { [key: string]: unknown; children?: unknown };

    const MockSvg = ({ children, ...rest }: MockProps) => (
        <View {...rest}>{children}</View>
    );

    return {
        __esModule: true,
        default: MockSvg,
        Path: (props: MockProps) => <View {...props} />,
    };
});

describe("ChevronRight component", () => {
    it("renders correctly and matches snapshot", () => {
        const { toJSON, getByTestId } = render(<ChevronRightIcon testID="icon" />);

        expect(getByTestId("icon")).toBeTruthy();
        expect(toJSON()).toMatchSnapshot();
    });

    it("forwards props to underlying Svg element", () => {
        const { getByTestId } = render(
            <ChevronRightIcon width={30} height={30} testID="icon" />
        );

        const svg = getByTestId("icon");
        expect(svg.props.width).toBe(30);
        expect(svg.props.height).toBe(30);
    });
}); 