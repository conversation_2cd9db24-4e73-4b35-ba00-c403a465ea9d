import React, {useEffect, useMemo, useState} from "react";
import {Image} from "expo-image";
import styles from "../../styles/components/logos/background-logo-texture.style";
import {View} from "react-native";
import {Asset} from "expo-asset";
import {
    ImageResult,
    SaveFormat,
    useImageManipulator
} from "expo-image-manipulator";

export interface BackgroundLogoTextureProps {
    gray?: boolean;
}

const BackgroundLogoTexture: React.FC<BackgroundLogoTextureProps> = (props) => {
    const initialImage = useMemo(
        () =>
            Asset.fromModule(
                props.gray
                    ? require("../../assets/textures/pattern-gray.png")
                    : require("../../assets/textures/pattern-gold.png")
            ),
        []
    );

    const [image, setImage] = useState<ImageResult | null>(null);

    const context = useImageManipulator(initialImage.uri);

    useEffect(() => {
        (async () => {
            context.rotate(13).resize({
                height: 1000,
                width: 1000
            });
            const image = await context.renderAsync();
            const result = await image.saveAsync({
                format: SaveFormat.PNG
            });

            setImage(result);
            context.reset();
        })();
    }, [initialImage, context]);

    return (
        <View style={[styles.imageContainer, styles.sizePosition]}>
            <Image
                style={[styles.image, styles.sizePosition]}
                source={image?.uri}
                contentFit="none"
            />
            <View style={styles.backFilter} />
        </View>
    );
};

export default BackgroundLogoTexture;
