import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const Type2Icon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={20}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke="#F2F4F7"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.667}
                d="M3.333 5.833c0-.777 0-1.165.127-1.471.17-.409.494-.733.902-.902.307-.127.695-.127 1.471-.127h8.334c.776 0 1.165 0 1.471.127.408.169.733.493.902.902.127.306.127.694.127 1.471m-10 10.833h6.667M8.542 3.333v13.333m2.917-13.333v13.333"
            />
        </Svg>
    );
};

export default Type2Icon;