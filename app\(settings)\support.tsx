import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
  TextInput,
  FlatList,
  Dimensions
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import InputField from "../../components/input-field";
import FullSizeButton from "../../components/full-size-button";
import SearchIcon from "../../components/icons/search-icon";
import styles from "../../styles/settings/support.style";

const {width: screenWidth} = Dimensions.get("window");

interface SupportTicket {
  fullName: string;
  email: string;
  subject: string;
  message: string;
  category: string;
}

const Support: React.FC = () => {
  const {t} = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState(0); // 0 = Tirar dúvidas, 1 = Entre em contato
  const [ticket, setTicket] = useState<SupportTicket>({
    fullName: "",
    email: "",
    subject: "",
    message: "",
    category: "general"
  });

  const supportOptions = [
    {
      id: "faq",
      title: t("support.faq", "Perguntas Frequentes"),
      description: t(
        "support.faqDesc",
        "Encontre respostas para as dúvidas mais comuns"
      ),
      icon: "❓",
      action: () => console.log("Open FAQ")
    },
    {
      id: "chat",
      title: t("support.liveChat", "Chat ao Vivo"),
      description: t(
        "support.liveChatDesc",
        "Fale com nossa equipe de suporte em tempo real"
      ),
      icon: "💬",
      action: () => console.log("Open live chat")
    },
    {
      id: "email",
      title: t("support.email", "Email"),
      description: t(
        "support.emailDesc",
        "Envie um <NAME_EMAIL>"
      ),
      icon: "📧",
      action: () => Linking.openURL("mailto:<EMAIL>")
    },
    {
      id: "phone",
      title: t("support.phone", "Telefone"),
      description: t("support.phoneDesc", "Ligue para (11) 3000-0000"),
      icon: "📞",
      action: () => Linking.openURL("tel:+551130000000")
    }
  ];

  const contactInfo = [
    {
      id: "sp",
      name: "Club M",
      location: "Unidade São Paulo - SP",
      email: "<EMAIL>",
      phone: "(11) 98765-4321"
    },
    {
      id: "rj",
      name: "Club M",
      location: "Unidade Rio de Janeiro - RJ",
      email: "<EMAIL>",
      phone: "(21) 91234-5678"
    },
    {
      id: "bh",
      name: "Club M",
      location: "Unidade Belo Horizonte - BH",
      email: "<EMAIL>",
      phone: "(31) 98765-5432"
    }
  ];

  const categories = [
    {id: "general", name: t("support.general", "Geral")},
    {id: "account", name: t("support.account", "Conta")},
    {id: "payment", name: t("support.payment", "Pagamento")},
    {id: "technical", name: t("support.technical", "Técnico")},
    {id: "events", name: t("support.events", "Eventos")},
    {id: "networking", name: t("support.networking", "Networking")}
  ];

  const handleInputChange = (field: keyof SupportTicket) => (value: string) => {
    setTicket((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAttachFile = () => {
    Alert.alert(
      t("support.attachFile", "Anexar Arquivo"),
      t("support.attachFileDesc", "Selecione um arquivo para anexar"),
      [
        {
          text: t("support.camera", "Câmera"),
          onPress: () => console.log("Open camera")
        },
        {
          text: t("support.gallery", "Galeria"),
          onPress: () => console.log("Open gallery")
        },
        {
          text: t("support.cancel", "Cancelar"),
          style: "cancel"
        }
      ]
    );
  };

  const selectCategory = (categoryId: string) => {
    setTicket((prev) => ({...prev, category: categoryId}));
  };

  const handleCategorySelect = () => {
    Alert.alert(
      t("support.selectCategory", "Selecionar Categoria"),
      t(
        "support.chooseCategoryDesc",
        "Escolha a categoria que melhor descreve seu problema"
      ),
      [
        ...categories.map((category) => ({
          text: category.name,
          onPress: () => selectCategory(category.id)
        })),
        {
          text: t("support.cancel", "Cancelar"),
          style: "cancel" as const
        }
      ]
    );
  };

  const handleSubmitTicket = () => {
    if (!ticket.fullName.trim()) {
      Alert.alert(
        t("support.error", "Erro"),
        t("support.nameRequired", "Por favor, informe seu nome completo")
      );
      return;
    }

    if (!ticket.email.trim()) {
      Alert.alert(
        t("support.error", "Erro"),
        t("support.emailRequired", "Por favor, informe seu e-mail")
      );
      return;
    }

    if (!ticket.message.trim()) {
      Alert.alert(
        t("support.error", "Erro"),
        t("support.messageRequired", "Por favor, escreva sua mensagem")
      );
      return;
    }

    Alert.alert(
      t("support.success", "Sucesso"),
      t(
        "support.ticketSubmitted",
        "Sua mensagem foi enviada para nossa equipe de suporte! Entraremos em contato em breve."
      ),
      [
        {
          text: t("support.ok", "OK"),
          onPress: () => {
            setTicket({
              fullName: "",
              email: "",
              subject: "",
              message: "",
              category: "general"
            });
          }
        }
      ]
    );
  };

  const getCategoryName = (categoryId: string) => {
    const category = categories.find((cat) => cat.id === categoryId);
    return category ? category.name : t("support.general", "Geral");
  };

  const renderSupportOptionCard = ({item}: {item: any}) => (
    <TouchableOpacity style={styles.carouselCard} onPress={item.action}>
      <Text style={styles.carouselIcon}>{item.icon}</Text>
      <Text style={styles.carouselTitle}>{item.title}</Text>
      <Text style={styles.carouselDescription}>{item.description}</Text>
    </TouchableOpacity>
  );

  const renderTabContent = () => {
    if (activeTab === 0) {
      // Aba "Tirar dúvidas"
      return (
        <ScrollView style={styles.tabContent}>
          {/* Formulário de contato */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>
              {t("support.contactForm", "Tire suas dúvidas")}
            </Text>
            <Text style={styles.sectionDescription}>
              {t(
                "support.contactFormDesc",
                "Envie uma mensagem para nossa equipe de suporte e em breve entraremos em contato com você!"
              )}
            </Text>

            <View style={styles.formContainer}>
              <TouchableOpacity
                style={styles.categorySelector}
                onPress={handleCategorySelect}
              >
                <View style={styles.categoryContent}>
                  <Text style={styles.categoryTitle}>
                    {t("support.category", "Selecione um assunto")}
                  </Text>
                  <Text style={styles.categoryValue}>
                    {getCategoryName(ticket.category)}
                  </Text>
                </View>
                <Text style={styles.optionChevron}>›</Text>
              </TouchableOpacity>

              <InputField
                label={t("support.fullName", "Nome completo")}
                value={ticket.fullName}
                onChangeText={handleInputChange("fullName")}
                placeholder={t(
                  "support.fullNamePlaceholder",
                  "Insira seu nome completo"
                )}
              />

              <InputField
                label={t("support.email", "E-mail")}
                value={ticket.email}
                onChangeText={handleInputChange("email")}
                placeholder={t("support.emailPlaceholder", "Insira seu e-mail")}
              />

              <View style={styles.messageContainer}>
                <Text style={styles.messageLabel}>
                  {t("support.message", "Mensagem")}
                </Text>
                <View style={styles.messageInputContainer}>
                  <InputField
                    label=""
                    value={ticket.message}
                    onChangeText={handleInputChange("message")}
                    placeholder={t(
                      "support.messagePlaceholder",
                      "Insira sua mensagem..."
                    )}
                    multiline
                    numberOfLines={6}
                    style={{
                      backgroundColor: "transparent",
                      borderWidth: 0,
                      paddingHorizontal: 0
                    }}
                  />
                </View>
                <Text style={styles.characterCount}>
                  {ticket.message.length}/1000 caracteres
                </Text>
              </View>

              <TouchableOpacity
                style={styles.attachFileButton}
                onPress={handleAttachFile}
              >
                <Text style={styles.attachFileText}>
                  📎 {t("support.attachFile", "Anexar arquivo (opcional)")}
                </Text>
              </TouchableOpacity>

              <FullSizeButton
                text={t("support.submit", "Enviar feedback")}
                onPress={handleSubmitTicket}
              />
            </View>
          </View>
        </ScrollView>
      );
    } else {
      // Aba "Entre em contato"
      return (
        <ScrollView style={styles.tabContent}>
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>
              {t("support.contactInfo", "Entre em contato")}
            </Text>
            <Text style={styles.infoDescription}>
              {t(
                "support.contactDesc",
                "Confira abaixo as informações de contato das nossas unidades ao redor do Brasil"
              )}
            </Text>

            <View style={styles.contactList}>
              {contactInfo.map((contact) => (
                <View key={contact.id} style={styles.contactItem}>
                  <View style={styles.contactHeader}>
                    <View style={styles.contactIcon}>
                      <Text style={styles.contactIconText}>🏢</Text>
                    </View>
                    <View style={styles.contactInfo}>
                      <Text style={styles.contactName}>{contact.name}</Text>
                      <Text style={styles.contactLocation}>
                        {contact.location}
                      </Text>
                    </View>
                  </View>

                  <TouchableOpacity
                    style={styles.contactAction}
                    onPress={() => Linking.openURL(`mailto:${contact.email}`)}
                  >
                    <Text style={styles.contactActionIcon}>📧</Text>
                    <Text style={styles.contactActionText}>
                      {contact.email}
                    </Text>
                    <Text style={styles.optionChevron}>›</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.contactAction}
                    onPress={() =>
                      Linking.openURL(
                        `tel:${contact.phone.replace(/[^\d]/g, "")}`
                      )
                    }
                  >
                    <Text style={styles.contactActionIcon}>📞</Text>
                    <Text style={styles.contactActionText}>
                      {contact.phone}
                    </Text>
                    <Text style={styles.optionChevron}>›</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      );
    }
  };

  return (
    <ScreenWithHeader
      screenTitle={t("support.title", "Suporte e ajuda")}
      backButton
    >
      <View style={styles.container}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <SearchIcon width={20} height={20} />
            <TextInput
              style={styles.searchInput}
              value={searchTerm}
              onChangeText={setSearchTerm}
              placeholder={t(
                "support.searchPlaceholder",
                "Busque perguntas ou palavras-chave"
              )}
              placeholderTextColor="#8B8B8B"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
        </View>

        {/* Quick Support Options Carousel */}
        <View style={styles.carouselContainer}>
          <Text style={styles.sectionTitle}>
            {t("support.quickHelp", "Ajuda Rápida")}
          </Text>
          <FlatList
            data={supportOptions}
            renderItem={renderSupportOptionCard}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.carouselContent}
            snapToInterval={screenWidth * 0.8}
            decelerationRate="fast"
          />
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 0 && styles.activeTab]}
            onPress={() => setActiveTab(0)}
          >
            <Text
              style={[styles.tabText, activeTab === 0 && styles.activeTabText]}
            >
              {t("support.askQuestions", "Tirar dúvidas")}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 1 && styles.activeTab]}
            onPress={() => setActiveTab(1)}
          >
            <Text
              style={[styles.tabText, activeTab === 1 && styles.activeTabText]}
            >
              {t("support.getInTouch", "Entre em contato")}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        {renderTabContent()}
      </View>
    </ScreenWithHeader>
  );
};

export default Support;
