import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface TrashIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const TrashIcon: React.FC<TrashIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#FCFCFD", [props.replaceColor]);

    return (
        <Svg
            width={20}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.667}
                d="M2.5 5h15M8.333 5V3.333a1.667 1.667 0 0 1 1.667-1.666h0a1.667 1.667 0 0 1 1.667 1.666V5m2.5 0v10a1.667 1.667 0 0 1-1.667 1.667H7.5a1.667 1.667 0 0 1-1.667-1.667V5h8.334ZM8.333 9.167v5M11.667 9.167v5"
            />
        </Svg>
    );
};

export default TrashIcon;
