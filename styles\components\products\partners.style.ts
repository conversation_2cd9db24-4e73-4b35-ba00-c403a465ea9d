import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        display: "flex",
        gap: 16
    },
    header: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center"
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 14,
        fontWeight: "700",
        fontFamily: stylesConstants.fonts.openSans,
        lineHeight: 20
    },
    viewAllText: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 12,
        fontWeight: "600",
        fontFamily: stylesConstants.fonts.openSans,
        lineHeight: 18
    },
    content: {
        flex: 1,
        display: "flex",
        gap: 12
    },
    flatListContainer: {
        display: "flex",
        flexDirection: "row",
        gap: 12
    },
    paginationContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center"
    },
    dotsContainer: {
        flexDirection: "row",
        gap: 8
    },
    dot: {
        height: 10,
        borderRadius: 24,
        borderWidth: 1,
        borderColor: stylesConstants.colors.fullWhite
    },
    activeDot: {
        width: 40,
        backgroundColor: stylesConstants.colors.fullWhite
    },
    inactiveDot: {
        width: 10,
        backgroundColor: "transparent"
    }
});

export default styles; 