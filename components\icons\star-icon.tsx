import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface StarIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const StarIcon: React.FC<StarIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#FCFCFD", [props.replaceColor]);

    return (
        <Svg
            width={24}
            height={24}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2Z"
            />
        </Svg>
    );
};

export default StarIcon;
