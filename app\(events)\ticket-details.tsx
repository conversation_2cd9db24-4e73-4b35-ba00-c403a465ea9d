import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity, Alert} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useLocalSearchParams} from "expo-router";
import styles from "@/styles/events/ticket-details.style";

interface Ticket {
  id: string;
  eventTitle: string;
  organizer: string;
  date: string;
  time: string;
  location: string;
  additionalInfo: string;
  ticketCode: string;
}

const TicketDetails: React.FC = () => {
  const {id} = useLocalSearchParams();
  const [showQRModal, setShowQRModal] = useState(false);

  const [ticket] = useState<Ticket>({
    id: (id as string) || "1",
    eventTitle: "Mentoria Coletiva sobre Resoluções Jurídicas",
    organizer: "Arbitralis",
    date: "29/05/2025",
    time: "17:30 PM",
    location: "Expocentro Balneário Camboriú - SC",
    additionalInfo: "Nenhuma informação adicional",
    ticketCode: "X9TR-72QK-P5DM"
  });

  const handleCopyTicketCode = () => {
    // Using a modern clipboard API alternative
    Alert.alert(
      "Código copiado",
      "O código do ingresso foi copiado para a área de transferência."
    );
  };

  const handleUnmarkPresence = () => {
    Alert.alert(
      "Desmarcar presença",
      "Tem certeza que deseja desmarcar sua presença neste evento?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Desmarcar",
          style: "destructive",
          onPress: () => {
            Alert.alert(
              "Presença desmarcada",
              "Sua presença foi desmarcada com sucesso."
            );
          }
        }
      ]
    );
  };

  return (
    <>
      <ScreenWithHeader
        screenTitle="Detalhes do Ingresso"
        backButton
        disablePadding
      >
        <ScrollView style={styles.container}>
          {/* Main Ticket Card */}
          <View style={styles.ticketContainer}>
            {/* Event Title and Organizer */}
            <View style={styles.ticketHeader}>
              <Text style={styles.eventTitle}>{ticket.eventTitle}</Text>
              <View style={styles.organizerContainer}>
                <Text style={styles.organizerLabel}>Sediado por:</Text>
                <Text style={styles.organizerName}>{ticket.organizer}</Text>
              </View>
            </View>

            {/* Event Details */}
            <View style={styles.ticketDetailsContainer}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Data/horário do evento</Text>
                <Text style={styles.detailValue}>
                  {ticket.date} - {ticket.time}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Código do ingresso</Text>
                <Text style={styles.detailValue}>{ticket.ticketCode}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Local do evento</Text>
                <Text style={styles.detailValue}>{ticket.location}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Informações adicionais</Text>
                <Text style={styles.detailValue}>{ticket.additionalInfo}</Text>
              </View>
            </View>

            {/* QR Code Section */}
            <View style={styles.qrCodeContainer}>
              <View style={styles.qrCode}>
                {/* QR Code placeholder - in a real app, you'd use a QR code library */}
                <View style={styles.qrCodePlaceholder}>
                  <Text style={styles.qrCodeText}>QR CODE</Text>
                </View>
              </View>

              <TouchableOpacity
                style={styles.fullScreenButton}
                onPress={() => setShowQRModal(true)}
              >
                <Text style={styles.fullScreenButtonText}>
                  Visualizar em tela cheia
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.unmarkPresenceButton}
              onPress={handleUnmarkPresence}
            >
              <Text style={styles.unmarkPresenceButtonText}>
                ✕ Desmarcar presença
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.closeButton}>
              <Text style={styles.closeButtonText}>Fechar</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ScreenWithHeader>

      {/* QR Code Full Screen Modal */}
      {showQRModal && (
        <View style={styles.qrModalOverlay}>
          <View style={styles.qrModalContainer}>
            <View style={styles.qrModalHeader}>
              <Text style={styles.qrModalTitle}>QR Code</Text>
            </View>

            <View style={styles.qrModalContent}>
              <View style={styles.qrModalQRCode}>
                <View style={styles.qrModalQRCodePlaceholder}>
                  <Text style={styles.qrModalQRCodeText}>QR CODE</Text>
                </View>
              </View>

              <View style={styles.qrModalCodeContainer}>
                <Text style={styles.qrModalCodeLabel}>Código do ingresso</Text>
                <View style={styles.qrModalCodeRow}>
                  <Text style={styles.qrModalCodeValue}>
                    {ticket.ticketCode}
                  </Text>
                  <TouchableOpacity
                    style={styles.copyButton}
                    onPress={handleCopyTicketCode}
                  >
                    <Text style={styles.copyButtonText}>📋</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <TouchableOpacity
              style={styles.qrModalCloseButton}
              onPress={() => setShowQRModal(false)}
            >
              <Text style={styles.qrModalCloseButtonText}>Fechar</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </>
  );
};

export default TicketDetails;
