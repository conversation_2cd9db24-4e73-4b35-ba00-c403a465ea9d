import React, {useState, useEffect} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import {router, useLocalSearchParams} from "expo-router";
import styles from "../../styles/events/credit-card-form.style";
import {
  formatCardNumberForDisplay,
  generateInstallmentOptions,
  getCardBrand
} from "../../utils/credit-card";

interface CardConfirmationData {
  number: string;
  expiryDate: string;
  cvv: string;
  holderName: string;
  installments: number;
  brand: string;
  totalValue: number;
  installmentValue: number;
}

const CreditCardConfirmation: React.FC = () => {
  const {t} = useTranslation();
  const params = useLocalSearchParams();
  const paymentType = (params.paymentType as string) || "mastercard";
  
  const [cardData, setCardData] = useState<CardConfirmationData | null>(null);
  const [selectedInstallment, setSelectedInstallment] = useState(1);
  
  useEffect(() => {
    if (params.cardData) {
      try {
        const data = JSON.parse(decodeURIComponent(params.cardData as string));
        setCardData(data);
        setSelectedInstallment(data.installments);
      } catch (error) {
        console.error("Erro ao parsear dados do cartão:", error);
        router.back();
      }
    }
  }, [params.cardData]);
  
  if (!cardData) {
    return null;
  }
  
  const installmentOptions = generateInstallmentOptions(cardData.totalValue, 12);
  const selectedOption = installmentOptions.find(opt => opt.installments === selectedInstallment);
  
  const handleInstallmentSelect = (installments: number) => {
    setSelectedInstallment(installments);
  };
  
  const handleEditCard = () => {
    router.back();
  };
  
  const handleConfirmPayment = () => {
    // Simular processamento do pagamento
    const finalCardData = {
      ...cardData,
      installments: selectedInstallment,
      installmentValue: selectedOption?.value || cardData.totalValue,
      totalValue: selectedOption?.totalValue || cardData.totalValue
    };
    
    router.push(`/(events)/payment-success?paymentType=credit-card&cardData=${encodeURIComponent(JSON.stringify(finalCardData))}`);
  };
  
  const getCardBrandIcon = (brand: string) => {
    switch (brand) {
      case 'visa':
        return '💳';
      case 'mastercard':
        return '💳';
      case 'amex':
        return '💳';
      default:
        return '💳';
    }
  };
  
  return (
    <ScreenWithHeader screenTitle="Confirmar pagamento" backButton>
      <ScrollView
        style={{flex: 1, paddingHorizontal: 24}}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>Revisar dados do cartão</Text>
          <Text style={styles.subtitle}>
            Confirme as informações antes de finalizar o pagamento
          </Text>
        </View>
        
        {/* Dados do cartão */}
        <View style={styles.formContainer}>
          <View style={{flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginBottom: 16}}>
            <Text style={styles.sectionTitle}>Cartão de crédito</Text>
            <TouchableOpacity onPress={handleEditCard}>
              <Text style={{color: "#0F7C4D", fontSize: 14, fontWeight: 600}}>
                ✏️ Editar
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={{
            backgroundColor: "#1F2238",
            borderRadius: 8,
            padding: 16,
            marginBottom: 16
          }}>
            <View style={{flexDirection: "row", alignItems: "center", marginBottom: 12}}>
              <View style={{
                width: 32,
                height: 32,
                backgroundColor: "#0F7C4D",
                borderRadius: 6,
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12
              }}>
                <Text style={{fontSize: 14, color: "#fff"}}>
                  {getCardBrandIcon(cardData.brand)}
                </Text>
              </View>
              
              <View style={{flex: 1}}>
                <Text style={{color: "#fff", fontSize: 14, fontWeight: 600}}>
                  {cardData.brand.toUpperCase()} {formatCardNumberForDisplay(cardData.number)}
                </Text>
                <Text style={{color: "#ccc", fontSize: 12}}>
                  {cardData.holderName}
                </Text>
              </View>
            </View>
            
            <View style={{flexDirection: "row", justifyContent: "space-between"}}>
              <Text style={{color: "#ccc", fontSize: 12}}>
                Validade: {cardData.expiryDate}
              </Text>
              <Text style={{color: "#ccc", fontSize: 12}}>
                CVV: ***
              </Text>
            </View>
          </View>
        </View>
        
        {/* Opções de parcelamento */}
        <View style={styles.installmentsContainer}>
          <Text style={styles.sectionTitle}>Escolha o parcelamento</Text>
          
          {installmentOptions.slice(0, 6).map((option) => (
            <TouchableOpacity
              key={option.installments}
              style={[
                styles.installmentOption,
                selectedInstallment === option.installments && styles.installmentOptionSelected
              ]}
              onPress={() => handleInstallmentSelect(option.installments)}
            >
              <View style={[
                styles.radioButton,
                selectedInstallment === option.installments && styles.radioButtonSelected
              ]}>
                {selectedInstallment === option.installments && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>
              
              <Text style={styles.installmentOptionText}>
                {option.label}
              </Text>
              
              {option.hasInterest && (
                <Text style={{color: "#FF6B6B", fontSize: 12, fontWeight: 400}}>
                  Total: R$ {option.totalValue.toFixed(2).replace(".", ",")}
                </Text>
              )}
            </TouchableOpacity>
          ))}
          
          {installmentOptions.length > 6 && (
            <TouchableOpacity style={{alignItems: "center", marginTop: 8}}>
              <Text style={{color: "#0F7C4D", fontSize: 14, fontWeight: 600}}>
                Ver mais opções de parcelamento
              </Text>
            </TouchableOpacity>
          )}
        </View>
        
        {/* Resumo do pagamento */}
        <View style={styles.summaryContainer}>
          <Text style={styles.sectionTitle}>Resumo do pagamento</Text>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Subtotal (2 itens)</Text>
            <Text style={styles.summaryValue}>
              R$ {cardData.totalValue.toFixed(2).replace(".", ",")}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Método de pagamento</Text>
            <Text style={styles.summaryValue}>
              {cardData.brand.toUpperCase()} {formatCardNumberForDisplay(cardData.number)}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Parcelamento</Text>
            <Text style={styles.summaryValue}>
              {selectedOption?.label}
            </Text>
          </View>
          
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Valor total</Text>
            <Text style={styles.totalValue}>
              R$ {(selectedOption?.totalValue || cardData.totalValue).toFixed(2).replace(".", ",")}
            </Text>
          </View>
        </View>
        
        {/* Informação de segurança */}
        <View style={styles.securityInfo}>
          <Text style={styles.securityIcon}>🔒</Text>
          <Text style={styles.securityText}>
            Transação segura processada com criptografia SSL
          </Text>
        </View>
        
        <FullSizeButton
          text="Confirmar pagamento"
          onPress={handleConfirmPayment}
        />
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default CreditCardConfirmation;
