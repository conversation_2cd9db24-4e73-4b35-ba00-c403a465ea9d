import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  StatusBar
} from "react-native";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import ChevronLeftIcon from "../../../components/icons/chevron-left-icon";
import ChevronRightIcon from "../../../components/icons/chevron-right-icon";
import EditIcon from "../../../components/icons/edit-icon";
import styles from "@/styles/partners/partner-benefits.style";

interface OpportunityStatus {
  type: "active" | "inactive" | "closed";
  label: string;
  color: string;
}

interface Opportunity {
  id: string;
  title: string;
  description: string;
  status: OpportunityStatus;
  isToggleEnabled: boolean;
}

const PartnerBenefitsScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"all" | "active" | "inactive">(
    "all"
  );

  // Mock data for opportunities
  const opportunities: Opportunity[] = [
    {
      id: "1",
      title: "Oportunidade Exclusiva na Praia Brava – Itajaí/SC",
      description:
        "Estamos abrindo uma oportunidade única para investidores visionários que desejam participar de um empreendimento com alto potencial de valorização e retorno financeiro. Seja parte do crescimento de um dos metros quadrados mais disputados do sul do país.",
      status: {
        type: "inactive",
        label: "Oportunidade Inativa",
        color: "#FEC84B"
      },
      isToggleEnabled: false
    },
    {
      id: "2",
      title: "Praia Brava, Itajaí/SC — onde grandes investimentos acontecem.",
      description:
        "Agora é o momento de investir com visão e propósito. Faça parte desse movimento. Entre em contato e saiba mais.",
      status: {
        type: "active",
        label: "Oportunidade Ativa",
        color: "#47CD89"
      },
      isToggleEnabled: true
    },
    {
      id: "3",
      title: "Praia Brava, Itajaí/SC — O Endereço do Futuro Está Chamando",
      description:
        "Estamos apresentando uma oportunidade única para investidores que querem estar à frente do mercado. Um projeto moderno, com alto padrão de execução e retorno garantido, inserido em um dos metros quadrados mais valorizados do Brasil.",
      status: {
        type: "closed",
        label: "Oportunidade Encerrada",
        color: "#EAECF0"
      },
      isToggleEnabled: false
    },
    {
      id: "4",
      title:
        "Invista Onde o Desejo Encontra a Valorização — Praia Brava, Itajaí/SC",
      description:
        "Visão, prestígio e retorno — essa é a sua chance de investir no lugar certo, na hora certa. Fale conosco e descubra os detalhes.",
      status: {
        type: "inactive",
        label: "Oportunidade Inativa",
        color: "#FEC84B"
      },
      isToggleEnabled: false
    }
  ];

  const handleBack = () => {
    router.back();
  };

  const handleEditOpportunity = (opportunityId: string) => {
    console.log("Edit opportunity:", opportunityId);
  };

  const handleToggleOpportunity = (opportunityId: string) => {
    console.log("Toggle opportunity:", opportunityId);
  };

  const handleOpportunityPress = (opportunityId: string) => {
    console.log("Open opportunity:", opportunityId);
  };

  const getTabCounts = () => {
    const activeCount = opportunities.filter(
      (op) => op.status.type === "active"
    ).length;
    const inactiveCount = opportunities.filter(
      (op) => op.status.type === "inactive"
    ).length;
    return {
      all: opportunities.length,
      active: activeCount,
      inactive: inactiveCount
    };
  };

  const filteredOpportunities = opportunities.filter((opportunity) => {
    if (activeTab === "all") return true;
    if (activeTab === "active") return opportunity.status.type === "active";
    if (activeTab === "inactive") return opportunity.status.type === "inactive";
    return true;
  });

  const tabCounts = getTabCounts();

  const renderToggle = (opportunity: Opportunity) => {
    const isEnabled = opportunity.isToggleEnabled;
    const isDisabled = opportunity.status.type === "closed";

    if (isDisabled) {
      return (
        <View style={styles.toggleDisabledContainer}>
          <View style={styles.toggleDisabled}>
            <View style={styles.toggleButtonDisabled} />
          </View>
        </View>
      );
    }

    return (
      <TouchableOpacity
        style={[
          styles.toggleBase,
          isEnabled ? styles.toggleEnabled : styles.toggleInactive
        ]}
        onPress={() => handleToggleOpportunity(opportunity.id)}
        activeOpacity={0.8}
      >
        <View
          style={[
            styles.toggleButton,
            isEnabled ? styles.toggleButtonEnabled : styles.toggleButtonInactive
          ]}
        />
      </TouchableOpacity>
    );
  };

  const renderOpportunityCard = (opportunity: Opportunity) => (
    <View key={opportunity.id} style={styles.opportunityCard}>
      <TouchableOpacity
        style={styles.opportunityHeader}
        onPress={() => handleOpportunityPress(opportunity.id)}
      >
        <View style={styles.opportunityIcon} />
        <Text style={styles.opportunityTitle} numberOfLines={2}>
          {opportunity.title}
        </Text>
        <ChevronRightIcon width={24} height={24} />
      </TouchableOpacity>

      <View style={styles.opportunityDescription}>
        <Text style={styles.opportunityDescriptionText} numberOfLines={3}>
          {opportunity.description}
        </Text>
      </View>

      <View style={styles.opportunityActions}>
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => handleEditOpportunity(opportunity.id)}
        >
          <EditIcon width={20} height={20} />
          <Text style={styles.editButtonText}>Editar</Text>
        </TouchableOpacity>

        <Text style={[styles.statusText, {color: opportunity.status.color}]}>
          {opportunity.status.label}
        </Text>

        {renderToggle(opportunity)}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#111828" />

      {/* Custom Header */}
      <View style={styles.customHeader}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ChevronLeftIcon width={24} height={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Minhas oportunidades</Text>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "all" && styles.activeTab]}
          onPress={() => setActiveTab("all")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "all" && styles.activeTabText
            ]}
          >
            Todas ({tabCounts.all})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === "active" && styles.activeTab]}
          onPress={() => setActiveTab("active")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "active" && styles.activeTabText
            ]}
          >
            Ativas ({tabCounts.active})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === "inactive" && styles.activeTab]}
          onPress={() => setActiveTab("inactive")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "inactive" && styles.activeTabText
            ]}
          >
            Inativas ({tabCounts.inactive})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Opportunities List */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {filteredOpportunities.map(renderOpportunityCard)}
      </ScrollView>
    </View>
  );
};

export default PartnerBenefitsScreen;
