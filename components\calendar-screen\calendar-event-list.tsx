import React from "react";
import {ScrollView, View} from "react-native";
import CalendarEvent from "./calendar-event";
import CalendarEmptyState from "./calendar-empty-state";

export interface CalendarEventData {
    id: string;
    title: string;
    description: string;
    location: string;
    startTime: string;
    endTime: string;
    isPrivate: boolean;
    isFree: boolean;
    isAttending: boolean;
    borderColor?: string;
}

export interface CalendarEventListProps {
    events: CalendarEventData[];
    onExploreEvents?: () => void;
}

const CalendarEventList: React.FC<CalendarEventListProps> = ({events, onExploreEvents}) => {
    if (events.length === 0) {
        return <CalendarEmptyState onExploreEvents={onExploreEvents} />;
    }

    return (
        <ScrollView showsVerticalScrollIndicator={false}>
            {events.map((event, index) => (
                <View key={event.id} style={{marginBottom: index < events.length - 1 ? 16 : 0}}>
                    <CalendarEvent event={event} />
                </View>
            ))}
        </ScrollView>
    );
};

export default CalendarEventList;
