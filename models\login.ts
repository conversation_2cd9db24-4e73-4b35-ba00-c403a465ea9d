import {z} from "zod";

export interface Login {
    document?: string;
    password: string;
}

export const ForgetPasswordRequestSchema = z.object({
    document: z
        .string()
        .optional()
        .refine((value) => value === undefined || value.length >= 14, {
            message: "errors.invalidCpf"
        })
        .refine((value) => value === undefined || value.length <= 14, {
            message: "errors.invalidCpf"
        })
});

export type ForgetPasswordRequest = Partial<
    z.infer<typeof ForgetPasswordRequestSchema>
>;

export const ResetPasswordRequestSchema = z.object({
    code: z
        .string()
        .min(6, {
            message: "errors.invalidCodeSize"
        })
        .max(6, {
            message: "errors.invalidCodeSize"
        }),
    password: z.string().min(8, {
        message: "errors.invalidPasswordLength"
    })
});

export type ResetPasswordRequest = Partial<
    z.infer<typeof ResetPasswordRequestSchema>
>;

export const LoginRequestSchema = z
    .object({
        document: z.string(),
        password: z.string().min(1, "errors.requiredField"),
        activatedBiometrics: z.boolean().optional(),
        firebaseToken: z.string().optional()
    })
    .refine((data) => Boolean(data.document), {
        message: "errors.requiredField",
        path: ["document"]
    });

export type LoginRequest = Partial<z.infer<typeof LoginRequestSchema>>;

export interface ForgetPasswordResponse {
    message: string;
    type: string;
    property: string;
    key: string;
}

export interface RecoveryPassword {
    password: string;
    repeatPassword: string;
}

export interface ApiErrorResponse {
    message: string;
}

export interface LoginResponse {
    tokenType: string;
    accessToken: string;
    expiresIn: number;
}

export const UpsellRequestSchema = z.object({
    name: z.string().min(1, "errors.requiredField"),
    phoneNumber: z.string().min(1, "errors.requiredField"),
    document: z.string().min(1, "errors.requiredField")
});

export type UpsellRequest = z.infer<typeof UpsellRequestSchema>;
