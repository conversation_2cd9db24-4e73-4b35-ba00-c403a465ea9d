import React, { createRef } from "react";
import { render, fireEvent } from "@testing-library/react-native";

jest.mock("../../../styles/components/recovery-password/mega-digit.style", () => ({
  container: {},
  onInputFillBorder: {},
  input: {}
}));

jest.mock("../../../styles/styles-constants", () => ({ colors: { gray300: "#aaa" } }));

import MegaDigit from "../../../components/recovery-password/mega-digit";

describe("MegaDigit", () => {
  it("accepts numeric input", () => {
    const onChange = jest.fn();
    const { getByPlaceholderText } = render(<MegaDigit value="" onChange={onChange} />);
    fireEvent.changeText(getByPlaceholderText("0"), "5");
    expect(onChange).toHaveBeenCalledWith("5");
  });

  it("ignores non numeric input", () => {
    const onChange = jest.fn();
    const { getByPlaceholderText } = render(<MegaDigit value="" onChange={onChange} />);
    fireEvent.changeText(getByPlaceholderText("0"), "a");
    expect(onChange).not.toHaveBeenCalled();
  });

  it("calls onBackspace on backspace key press", () => {
    const onBackspace = jest.fn();
    const ref = createRef<any>();
    const { getByPlaceholderText } = render(
      <MegaDigit value="" onChange={jest.fn()} onBackspace={onBackspace} ref={ref} />
    );
    fireEvent(getByPlaceholderText("0"), "keyPress", { nativeEvent: { key: "Backspace" } });
    expect(onBackspace).toHaveBeenCalled();
  });
});
