import React, {useCallback} from "react";
import {StyleProp, View, ViewStyle} from "react-native";
import styles from "../../styles/components/login/select-button.style";
import OptionButton from "./option-button";

export interface SelectButtonProps {
    options: SelectOptions[];
    activeOption: React.Key;
    activeOptionChange?: (activeOption: React.Key) => void;
    style?: StyleProp<ViewStyle>
}

export interface SelectOptions {
    id: React.Key;
    label: string;
}

const SelectButton: React.FC<SelectButtonProps> = (props) => {
    const isActive = useCallback((opts: SelectOptions) => props.activeOption == opts.id,
        [props.activeOption]);

    const onPress = useCallback((opts: SelectOptions) => () => {
        props.activeOptionChange?.(opts.id);
    }, []);

    return (
        <View style={[styles.container, props.style]}>
            {props.options.map((opts) => (
                <OptionButton
                    key={opts.id}
                    option={opts}
                    active={isActive(opts)}
                    onPress={onPress(opts)}
                />
            ))}
        </View>
    );
};



export default SelectButton;