import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        flexDirection: "row",
        justifyContent: "space-around",
        borderBottomWidth: 1,
        borderBottomColor: stylesConstants.colors.gray200
    },
    tab: {
        borderBottomWidth: 2,
        borderBottomColor: "transparent"
    },
    activeTab: {
        borderBottomColor: stylesConstants.colors.textPrimary
    },
    text: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        paddingBottom: 12
    },
    activeText: {
        color: stylesConstants.colors.textPrimary,
        fontWeight: 700
    }
});

export default styles;
