@echo off
"C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  x86_64 ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging12913695022303453609\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\prefab" ^
  "C:\\Users\\<USER>\\eclipse-workspace\\club-m-app\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\n4v6a2j2" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0aa4eabcf5a1bcbf3ed8249013e84f07\\transformed\\hermes-android-0.79.3-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fc98766b9298386f4aefafd293f4f926\\transformed\\fbjni-0.7.0\\prefab"
