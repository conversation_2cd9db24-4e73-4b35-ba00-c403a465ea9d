import React from "react";
import {render} from "@testing-library/react-native";

jest.mock("expo-splash-screen", () => ({
  preventAutoHideAsync: jest.fn(() => Promise.resolve()),
  hideAsync: jest.fn(() => Promise.resolve())
}));

jest.mock("@dev-plugins/react-query", () => ({
  useReactQueryDevTools: jest.fn()
}));

jest.mock("expo-status-bar", () => ({
  StatusBar: () => null
}));

// Mock do QueryClient com métodos necessários
const mockQueryClient = {
  mount: jest.fn(),
  unmount: jest.fn(),
  clear: jest.fn(),
  getQueryCache: jest.fn(),
  getMutationCache: jest.fn(),
  getLogger: jest.fn(),
  getDefaultOptions: jest.fn(),
  setDefaultOptions: jest.fn(),
  isFetching: jest.fn(),
  isMutating: jest.fn(),
  getQueryData: jest.fn(),
  setQueryData: jest.fn(),
  getQueriesData: jest.fn(),
  setQueriesData: jest.fn(),
  invalidateQueries: jest.fn(),
  refetchQueries: jest.fn(),
  cancelQueries: jest.fn(),
  removeQueries: jest.fn(),
  resetQueries: jest.fn(),
  fetchQuery: jest.fn(),
  prefetchQuery: jest.fn(),
  fetchInfiniteQuery: jest.fn(),
  prefetchInfiniteQuery: jest.fn(),
  resumePausedMutations: jest.fn(),
  executeMutation: jest.fn()
};

jest.mock("../services/api.service", () => ({
  __esModule: true,
  default: {queryClient: mockQueryClient}
}));

jest.mock("../components/logos/animated-logo", () => {
  return ({children}: any) => <>{children}</>;
});

jest.mock("../contexts/error-dialog-context", () => {
  return ({children}: any) => <>{children}</>;
});

jest.mock("../components/error-dialog", () => {
  return () => null;
});

jest.mock("../contexts/loading-context", () => {
  return ({children}: any) => <>{children}</>;
});

jest.mock("../contexts/session.context", () => {
  return ({children}: any) => <>{children}</>;
});

jest.mock("../contexts/bottom-modal-context", () => {
  return ({children}: any) => <>{children}</>;
});

jest.mock("../components/bottom-modal", () => {
  return () => null;
});

jest.mock("@react-native-async-storage/async-storage", () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(),
  removeItem: jest.fn()
}));

jest.mock("expo-localization", () => ({
  getLocales: () => [{languageCode: "pt-BR"}]
}));

jest.mock("react-native-safe-area-context", () => ({
  SafeAreaProvider: ({children}: any) => children,
  useSafeAreaInsets: () => ({
    top: 0,
    bottom: 0,
    left: 0,
    right: 0
  })
}));

jest.mock("@tanstack/react-query", () => ({
  QueryClientProvider: ({children}: any) => <>{children}</>,
  onlineManager: {
    setEventListener: jest.fn()
  }
}));

jest.mock("../utils/i18n", () => ({}));

jest.mock("expo-router", () => ({
  Stack: ({children}: any) => <>{children}</>
}));

const SplashScreen = require("expo-splash-screen");
const Layout = require("../app/_layout").default;

describe("Layout component", () => {
  it("calls SplashScreen.preventAutoHideAsync on module load", () => {
    expect(SplashScreen.preventAutoHideAsync).toHaveBeenCalledTimes(1);
  });

  it("calls SplashScreen.hideAsync on mount", () => {
    SplashScreen.hideAsync.mockClear();
    render(<Layout />);
    expect(SplashScreen.hideAsync).toHaveBeenCalledTimes(1);
  });

  it("renders without crashing", () => {
    const {toJSON} = render(<Layout />);
    expect(toJSON()).not.toBeUndefined();
  });
});
