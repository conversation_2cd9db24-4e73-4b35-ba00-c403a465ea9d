import React, { useMemo } from "react";
import { ColorValue } from "react-native";
import Svg, { Path, SvgProps } from "react-native-svg";

export interface SearchIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const SearchIcon: React.FC<SearchIconProps> = (props) => {
    const color = useMemo(
        () => props.replaceColor ?? "#F2F4F7",
        [props.replaceColor],
    );

    return (
        <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.667}
                d="M17.5 17.5L13.875 13.875M9.16667 5C11.4679 5 13.3333 6.86548 13.3333 9.16667M15.8333 9.16667C15.8333 12.8486 12.8486 15.8333 9.16667 15.8333C5.48477 15.8333 2.5 12.8486 2.5 9.16667C2.5 5.48477 5.48477 2.5 9.16667 2.5C12.8486 2.5 15.8333 5.48477 15.8333 9.16667Z"
            />
        </Svg>
    );
};

export default SearchIcon;
