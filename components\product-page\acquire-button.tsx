import React, {useMemo} from "react";
import {TouchableHighlight, View, Text} from "react-native";
import styles from "../../styles/components/product-page/acquire-button.style";
import {useTranslation} from "react-i18next";
import useCurrentLocalization from "../../hooks/use-localization";

export interface AcquireButtonProps {
    price?: number;
    tag?: React.ReactNode;
}

const AcquireButton: React.FC<AcquireButtonProps> = (props) => {
    const {t} = useTranslation();
    const localization = useCurrentLocalization();

    const price = useMemo(() => {
        return props.price
            ? (props.price / 100).toLocaleString(localization, {
                  maximumFractionDigits: 2
              })
            : 0;
    }, [props.price, localization]);

    return (
        <TouchableHighlight style={styles.touchableHighlight}>
            <View>
                {props.tag && (
                    <View style={styles.tagContainer}>{props.tag}</View>
                )}

                <View style={styles.contentContainer}>
                    <Text style={styles.labelText}>
                        {t("acquireButton.acquire")}
                    </Text>
                    <Text style={styles.priceText}>R$ {price}</Text>
                </View>
            </View>
        </TouchableHighlight>
    );
};

export default AcquireButton;
