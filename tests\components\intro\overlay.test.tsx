import React from "react";
import {render, fireEvent} from "@testing-library/react-native";

// Mock expo-linear-gradient
jest.mock("expo-linear-gradient", () => {
  const {View} = require("react-native");
  return {
    LinearGradient: (props: any) => <View {...props} />
  };
});

// Mock i18n translation
jest.mock("react-i18next", () => ({
  useTranslation: () => ({t: (key: string) => key})
}));

// Mock expo-router and expose navigateMock for assertions
jest.mock("expo-router", () => {
  const navigateMock = jest.fn();
  return {
    __esModule: true,
    useRouter: () => ({navigate: navigateMock}),
    navigateMock
  };
});

// Mock Dots component
jest.mock("@/components/intro/dots", () => {
  const {View} = require("react-native");
  return {
    __esModule: true,
    default: (props: any) => <View {...props} testID="dots" />
  };
});

// Mock NextButton to a simple touchable that exposes testIDs and respects disabled prop
jest.mock("@/components/intro/next-button", () => {
  const {TouchableOpacity, View} = require("react-native");

  const NextButton = (props: any) => (
    <TouchableOpacity
      testID={props.isPrevious ? "prev-button" : "next-button"}
      onPress={props.disabled ? undefined : props.onPress}
      disabled={props.disabled}
    >
      <View />
    </TouchableOpacity>
  );

  return {
    __esModule: true,
    default: NextButton
  };
});

import Overlay from "../../../components/intro/overlay";

const texts = [
  {title: "intro.slide1.title", description: "intro.slide1.description"},
  {title: "intro.slide2.title", description: "intro.slide2.description"}
];

describe("Overlay component", () => {
  it("renders title and description for the current slide", () => {
    const {getByText} = render(
      <Overlay currentSlide={0} texts={texts} slideState={0} />
    );

    expect(getByText("intro.slide1.title")).toBeTruthy();
    expect(getByText("intro.slide1.description")).toBeTruthy();
  });

  it("navigates to /login when skip intro is pressed", () => {
    const {getByText} = render(
      <Overlay currentSlide={0} texts={texts} slideState={0} />
    );

    const skipButton = getByText("intro.skipIntroButton");
    fireEvent.press(skipButton);

    const {navigateMock} = require("expo-router");
    expect(navigateMock).toHaveBeenCalledWith("/login");
  });

  it("calls onChangeSlide with next slide index on next button press", () => {
    const onChangeSlide = jest.fn();
    const {getByTestId} = render(
      <Overlay
        currentSlide={0}
        texts={texts}
        slideState={0}
        onChangeSlide={onChangeSlide}
      />
    );

    fireEvent.press(getByTestId("next-button"));
    expect(onChangeSlide).toHaveBeenCalledWith(1);
  });

  it("does not call onChangeSlide when previous is disabled", () => {
    const onChangeSlide = jest.fn();
    const {getByTestId} = render(
      <Overlay
        currentSlide={0}
        texts={texts}
        slideState={0}
        onChangeSlide={onChangeSlide}
      />
    );

    fireEvent.press(getByTestId("prev-button"));
    expect(onChangeSlide).not.toHaveBeenCalled();
  });
});
