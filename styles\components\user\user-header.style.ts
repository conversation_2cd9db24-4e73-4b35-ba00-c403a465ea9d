import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        zIndex: 1
    },
    backgroundImage: {
        overflow: "hidden",
        backgroundColor: "#02343B"
    },
    backgroundImageStyle: {
        backgroundColor: "#02343B",
        objectFit: "none",
        opacity: 0.1,
        transform: [
            {
                scale: 1.6
            }
        ]
    },
    overlay: {
        flex: 1,
        backgroundColor: "rgba(0, 0, 0, 0.3)"
    },
    content: {
        marginTop: 25,
        padding: 24,
        paddingBottom: 50,
        justifyContent: "center",
        alignItems: "center",
        width: "100%"
    },
    innerContainer: {
        gap: 12,
        alignItems: "center",
        width: "100%"
    },
    userInfo: {
        alignItems: "center",
        gap: 5
    },
    userName: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 700
    },
    locationContainer: {
        flexDirection: "row",
        gap: 4
    },
    locationText: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400
    },
    buttonsContainer: {
        flexDirection: "row",
        gap: 16
    },
    button: {
        flex: 1
    }
});

export default styles;
