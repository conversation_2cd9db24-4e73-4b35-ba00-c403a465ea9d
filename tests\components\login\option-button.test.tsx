import React from "react";
import { render, fireEvent } from "@testing-library/react-native";

jest.mock("react-native-reanimated", () => {
  const Reanimated = require("react-native-reanimated/mock");
  Reanimated.withTiming = (value: any) => value;
  return Reanimated;
});

jest.mock("../../../styles/styles-constants", () => ({
  colors: { secondary: "red", gray200: "#ccc", gray900: "#000" }
}));

jest.mock("../../../styles/components/login/select-button.style", () => ({
  button: { padding: 4 },
  buttonText: { fontSize: 12 },
  container: {}
}));

import OptionButton from "../../../components/login/option-button";

describe("OptionButton", () => {
  const option = { id: 1, label: "Login" } as const;
  it("calls onPress when tapped", () => {
    const onPress = jest.fn();
    const { getByText } = render(<OptionButton option={option} active={false} onPress={onPress} />);
    fireEvent.press(getByText("Login"));
    expect(onPress).toHaveBeenCalledTimes(1);
  });

  it("applies active state", () => {
    const { getByText } = render(<OptionButton option={option} active={true} onPress={jest.fn()} />);
    expect(getByText("Login")).toBeTruthy();
  });
});
