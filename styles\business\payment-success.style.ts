import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 32
  },
  successIcon: {
    width: 80,
    height: 80,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16
  },
  successIconText: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 32,
    fontWeight: 700
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    textAlign: "center",
    marginBottom: 8
  },
  subtitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    textAlign: "center"
  },
  sectionContainer: {
    marginBottom: 24
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 16
  },
  productCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  productHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16
  },
  productIcon: {
    width: 48,
    height: 48,
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  productIconText: {
    fontSize: 20
  },
  productInfo: {
    flex: 1,
    marginRight: 12
  },
  productName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 22,
    marginBottom: 4
  },
  productSeller: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16,
    marginBottom: 2
  },
  productCategory: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16
  },
  productValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 700,
    lineHeight: 24
  },
  productDetails: {
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    paddingTop: 16
  },
  productDetailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8
  },
  productDetailLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16
  },
  productDetailValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16
  },
  paymentSummaryContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  paymentMethodRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12
  },
  paymentMethodLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  paymentMethodValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    paddingTop: 12,
    marginTop: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  totalLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24
  },
  totalValue: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 700,
    lineHeight: 26
  },
  buttonContainer: {
    marginTop: 32,
    gap: 16
  },
  secondaryButtonContainer: {
    alignItems: "center"
  },
  secondaryButton: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    textDecorationLine: "underline"
  }
});

export default styles;
