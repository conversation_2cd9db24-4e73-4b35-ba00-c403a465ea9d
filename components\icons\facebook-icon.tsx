import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface FacebookIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const FacebookIcon: React.FC<FacebookIconProps> = (props) => {
  const color = useMemo(() => props.replaceColor ?? "#1877F2", [props.replaceColor]);

  return (
    <Svg
      width={20}
      height={20}
      fill="none"
      {...props}
    >
      <Path
        fill={color}
        d="M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z"
      />
    </Svg>
  );
};

export default FacebookIcon;
