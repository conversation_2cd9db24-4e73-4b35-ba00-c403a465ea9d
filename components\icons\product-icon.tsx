import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";
import React, {useMemo} from "react";

export interface MessageIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const ProductIcon: React.FC<MessageIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#D0D5DD", [props.replaceColor]);

    return (
        <Svg
            width={25}
            height={24}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M6.02 2.64 4.46 4.72c-.309.412-.463.618-.46.79a.5.5 0 0 0 .192.384C4.328 6 4.585 6 5.1 6h14.8c.515 0 .773 0 .908-.106A.5.5 0 0 0 21 5.51c.003-.172-.151-.378-.46-.79l-1.56-2.08m-12.96 0c.176-.235.264-.352.376-.437a1 1 0 0 1 .33-.165C6.86 2 7.005 2 7.3 2h10.4c.293 0 .44 0 .575.038a1 1 0 0 1 .33.165c.111.085.199.202.375.437m-12.96 0L4.14 5.147c-.237.316-.356.475-.44.649a2 2 0 0 0-.163.487c-.037.19-.037.388-.037.784V18.8c0 1.12 0 1.68.218 2.108a2 2 0 0 0 .874.874C5.02 22 5.58 22 6.7 22h11.6c1.12 0 1.68 0 2.108-.218a2 2 0 0 0 .874-.874c.218-.428.218-.988.218-2.108V7.067c0-.396 0-.594-.037-.784a1.998 1.998 0 0 0-.163-.487c-.084-.174-.203-.333-.44-.65L18.98 2.64M16.5 10a4 4 0 1 1-8 0"
            />
        </Svg>
    );
};

export default ProductIcon;