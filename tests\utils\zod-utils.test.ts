import { z } from "zod";
import { formatZodError, formatZodErrorToString } from "../../utils/zod-utils";

describe("zod-utils", () => {
  const schema = z.object({ email: z.string().email(), pass: z.string().min(3) });

  it("formats field errors", () => {
    const result = schema.safeParse({ email: "bad", pass: "1" });
    const errors = formatZodError((result as any).error);
    expect(errors).toHaveProperty("email");
    expect(errors).toHaveProperty("pass");
  });

  it("formats error to string", () => {
    const result = schema.safeParse({ email: "bad", pass: "1" });
    const str = formatZodErrorToString((result as any).error);
    expect(typeof str).toBe("string");
    expect(str.length).toBeGreaterThan(0);
  });
});
