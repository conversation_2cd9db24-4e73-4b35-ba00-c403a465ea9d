import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    overlay: {
        position: "absolute",
        bottom: 0,
        width: "100%",
    },
    baseMarginBottom: {
        marginBottom: 16
    },
    internalContainer: {
        flex: 1,
        padding: 24
    },
    text: {
        color: stylesConstants.colors.secondary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontStyle: "normal",
        fontWeight: 400,
        lineHeight: 24
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 30,
        fontWeight: 700,
        lineHeight: 38
    },
    lastContainer: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        alignSelf: "stretch",
        marginTop: 24,
        flexDirection: "row"
    },
    lastContainerText: {
        display: "flex",
        height: 48,
        paddingVertical: 10,
        paddingHorizontal: 0,
        justifyContent: "center",
        alignItems: "center",
        gap: 8
    },
    skipIntroText: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontStyle: "normal",
        fontWeight: 600,
        lineHeight: 20
    },
    buttons: {
        display: "flex",
        flexDirection: "row",
        alignItems: "flex-start",
        gap: 12
    }
});

export default styles;