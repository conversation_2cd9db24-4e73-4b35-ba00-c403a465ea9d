import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface MyOpportunitiesIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const MyOpportunitiesIcon: React.FC<MyOpportunitiesIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
      <Path
        d="M15 8.33366L12.138 11.1956C11.973 11.3606 11.8905 11.4431 11.7954 11.474C11.7117 11.5012 11.6216 11.5012 11.5379 11.474C11.4427 11.4431 11.3602 11.3606 11.1952 11.1956L8.8047 8.80506C8.63969 8.64006 8.55719 8.55755 8.46205 8.52664C8.37837 8.49945 8.28822 8.49945 8.20454 8.52664C8.1094 8.55755 8.0269 8.64006 7.86189 8.80506L4.99996 11.667M18.3333 10.0003C18.3333 14.6027 14.6023 18.3337 9.99996 18.3337C5.39759 18.3337 1.66663 14.6027 1.66663 10.0003C1.66663 5.39795 5.39759 1.66699 9.99996 1.66699C14.6023 1.66699 18.3333 5.39795 18.3333 10.0003Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default MyOpportunitiesIcon;
