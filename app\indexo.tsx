import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {NativeSyntheticEvent, View} from "react-native";
import PagerView from "react-native-pager-view";
import styles from "../styles/auth/index.style";
import Slide from "../components/intro/slide";
import {ImageSource} from "expo-image/src/Image.types";
import Overlay, {SlideTextProps} from "../components/intro/overlay";
import OverlayTop from "../components/intro/overlay-top";
import {Double} from "react-native/Libraries/Types/CodegenTypes";
import useLogin from "../hooks/use-login";

const IndexPage: React.FC = () => {
  const [slideState, setSlideState] = useState({
    offset: 0,
    index: 0
  });
  const pageViewRef = useRef<PagerView>(null);

  const loginAction = useLogin();

  const slidesText: SlideTextProps[] = useMemo(
    () => [
      {
        title: "intro.slide1.title",
        description: "intro.slide1.description"
      },
      {
        title: "intro.slide2.title",
        description: "intro.slide1.description"
      },
      {
        title: "intro.slide3.title",
        description: "intro.slide1.description"
      }
    ],
    []
  );

  const imagesPosition = useMemo(() => [65, 50, 49.5], []);

  const images: ImageSource[] = useMemo(
    () => [
      require("../assets/intro-images/intro-slide-1.jpg"),
      require("../assets/intro-images/intro-slide-2.png"),
      require("../assets/intro-images/intro-slide-3.png")
    ],
    []
  );

  useEffect(() => {
    loginAction.restoreSession();
  }, [loginAction]);

  const onPageScroll = useCallback(
    (
      e: NativeSyntheticEvent<
        Readonly<{
          position: Double;
          offset: Double;
        }>
      >
    ) => {
      setSlideState({
        index: e.nativeEvent.position,
        offset: e.nativeEvent.offset
      });
    },
    []
  );

  const onPageChange = useCallback((slide: number) => {
    pageViewRef.current?.setPage(slide);
  }, []);

  return (
    <View style={styles.screenView}>
      <PagerView
        ref={pageViewRef}
        initialPage={0}
        style={styles.screenView}
        onPageScroll={onPageScroll}
      >
        {images.map((image, index) => (
          <Slide
            key={`slide-${index}-${imagesPosition[index]}`}
            id={index}
            imageLeftPosition={imagesPosition[index]}
            image={image}
          />
        ))}
      </PagerView>
      <OverlayTop />
      <Overlay
        currentSlide={slideState.index}
        texts={slidesText}
        slideState={slideState.offset}
        onChangeSlide={onPageChange}
      />
    </View>
  );
};

export default IndexPage;
