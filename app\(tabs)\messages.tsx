import React, {useCallback, useMemo, useState} from "react";
import {FlatList, Image, ListRenderItemInfo, Text, TouchableOpacity, View} from "react-native";
import {useTranslation} from "react-i18next";
import ScreenWithHeader from "../../components/screen-with-header";
import ChevronRightIcon from "../../components/icons/chevron-right-icon";
import PinIcon from "../../components/icons/pin-icon";
import MessagePlusIcon from "../../components/icons/message-plus-icon";
import styles from "../../styles/tabs/messages.style";
import stylesConstants from "../../styles/styles-constants";
import Search from "../../components/search";
import {useRouter} from "expo-router";

interface MessageData {
    id: string;
    name: string;
    message: string;
    time: string;
    avatar: string;
    isOnline: boolean;
    isPinned?: boolean;
    unreadCount?: number;
    hasButton?: boolean;
    buttonText?: string;
    isOlderMessage?: boolean;
}

const Messages: React.FC = () => {
    const {t} = useTranslation();
    const [searchValue, setSearchValue] = useState("");
    const router = useRouter();

    const messages: MessageData[] = useMemo(() => ([
        {
            id: "1",
            name: "Lana Steiner",
            message: "Estou animada para compartilhar algumas novidades com vocês. Vamos nos conectar!",
            time: "Agora",
            avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
            isOnline: true,
            isPinned: true,
            unreadCount: 9
        },
        {
            id: "2",
            name: "Candice Wu",
            message: "Estou convidando você para o evento \"Encontro anual de empreendedores em Balneário Camboriú - SC\", participe comigo!",
            time: "15:15 PM",
            avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
            isOnline: true,
            hasButton: true,
            buttonText: "Ver evento"
        },
        {
            id: "3",
            name: "Phoenix Baker",
            message: "Oi, tenho algumas atualizações incríveis para dividir com vocês. Vamos trocar ideias e nos conectar!",
            time: "Há 3 dias",
            avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
            isOnline: false,
            isOlderMessage: true
        },
        {
            id: "4",
            name: "Demi Wilkinson",
            message: "Que tal a gente se encontrar pra bater um papo e ficar por dentro?",
            time: "Há 1 dia",
            avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
            isOnline: true,
            isOlderMessage: true
        },
        {
            id: "5",
            name: "Natali Craig",
            message: "Estou ansioso para ouvir o que você têm a dizer!",
            time: "Há 1 semana",
            avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
            isOnline: false,
            isOlderMessage: true
        },
        {
            id: "6",
            name: "Lyle Kauffman",
            message: "Olá, terminei o documento de requisitos!",
            time: "Há 1 semana",
            avatar: "https://images.unsplash.com/photo-1463453091185-61582044d556?w=100&h=100&fit=crop&crop=face",
            isOnline: false,
            isOlderMessage: true
        },
        {
            id: "7",
            name: "Orlando Diggs",
            message: "Que tal a gente se encontrar pra bater um papo e ficar por dentro?",
            time: "Há 1 mês",
            avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=face",
            isOnline: true,
            isOlderMessage: true
        },
        {
            id: "8",
            name: "Noah Pierre",
            message: "Que tal a gente se encontrar pra bater um papo e ficar por dentro?",
            time: "Há 1 ano",
            avatar: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=100&h=100&fit=crop&crop=face",
            isOnline: true,
            isOlderMessage: true
        }
    ]), []);

    const handleSearchChange = useCallback((value: string) => {
        setSearchValue(value);
    }, []);

    const handleMessagePress = useCallback(() => {
        router.push("/(logged-stack)/chat")
    }, [router]);

    const renderMessage = useCallback(({item}: ListRenderItemInfo<MessageData>) => (
        <View>
            <TouchableOpacity style={styles.messageItem} onPress={handleMessagePress}>
                <View style={styles.avatarContainer}>
                    <Image source={{uri: item.avatar}} style={styles.avatar}/>
                    <View style={styles.avatarBorder}/>
                    {item.isOnline && <View style={styles.onlineIndicator}/>}
                </View>

                <View style={styles.messageContent}>
                    <View style={styles.messageHeader}>
                        <View style={styles.nameRow}>
                            <Text style={styles.userName}>{item.name}</Text>
                            {item.isPinned && (
                                <View style={styles.pinnedBadge}>
                                    <PinIcon/>
                                    <Text style={styles.pinnedText}>{t("messages.pinned")}</Text>
                                </View>
                            )}
                        </View>
                        <View style={styles.timeRow}>
                            <Text
                                style={[styles.messageTime, item.id === "8" && {color: stylesConstants.colors.textPrimary}]}>
                                {item.time}
                            </Text>
                            <ChevronRightIcon width={16} height={16}/>
                        </View>
                    </View>

                    <View style={styles.messageBody}>
                        <Text ellipsizeMode="tail" numberOfLines={1} style={[
                            styles.messageText,
                            item.isOlderMessage && styles.lightMessageText
                        ]}>
                            {item.message}
                        </Text>
                        {item.hasButton && (
                            <TouchableOpacity style={styles.linkButton}>
                                <Text style={styles.linkButtonText}>{item.buttonText}</Text>
                            </TouchableOpacity>
                        )}
                        {item.unreadCount && (
                            <View style={styles.unreadBadge}>
                                <Text style={styles.unreadCount}>{item.unreadCount}</Text>
                            </View>
                        )}
                    </View>


                </View>
            </TouchableOpacity>

            {item.id !== "8" && <View style={styles.divider}/>}
        </View>
    ), [handleMessagePress]);

    const rightHeaderChild = useMemo(() => (
        <TouchableOpacity>
            <MessagePlusIcon/>
        </TouchableOpacity>
    ), []);

    return (
        <ScreenWithHeader
            screenTitle={t("messages.title")}
            rightHeaderChild={rightHeaderChild}
            disableScrollView
        >
            <View style={styles.pageContainer}>
                <Search searchBarValue={searchValue} onSearchBarChange={handleSearchChange}/>
                <FlatList
                    data={messages}
                    renderItem={renderMessage}
                    keyExtractor={(item) => item.id}
                    showsVerticalScrollIndicator={false}
                />
            </View>
        </ScreenWithHeader>
    );
};

export default Messages;
