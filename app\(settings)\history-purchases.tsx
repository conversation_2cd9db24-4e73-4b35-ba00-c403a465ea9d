import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity, Modal} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import styles from "@/styles/settings/history-purchases.style";

interface Purchase {
  id: string;
  type: "event" | "subscription" | "product" | "service";
  title: string;
  description: string;
  amount: number;
  date: string;
  status: "completed" | "pending" | "cancelled" | "refunded";
  paymentMethod: string;
  itemType: string;
  orderNumber: string;
}

const HistoryPurchases: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<string>("all");
  const [selectedPurchase, setSelectedPurchase] = useState<Purchase | null>(
    null
  );
  const [purchases] = useState<Purchase[]>([
    {
      id: "1",
      type: "subscription",
      title: "Comemoração de 10 anos da Cafeteira Garibaldi",
      description: "",
      amount: 1500.0,
      date: "2024-05-22",
      status: "pending",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    },
    {
      id: "2",
      type: "event",
      title: "Encontro anual de empreendedores em Balneário Camboriú",
      description: "",
      amount: 1200.0,
      date: "2024-05-21",
      status: "completed",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    },
    {
      id: "3",
      type: "subscription",
      title: "Assinatura mensal de membro Club M",
      description: "",
      amount: 280.0,
      date: "2024-05-20",
      status: "completed",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    },
    {
      id: "4",
      type: "service",
      title: "Mentoria Coletiva sobre Resoluções Jurídicas",
      description: "",
      amount: 1200.0,
      date: "2024-04-15",
      status: "cancelled",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    },
    {
      id: "5",
      type: "subscription",
      title: "Assinatura mensal de membro Club M",
      description: "",
      amount: 280.0,
      date: "2024-04-10",
      status: "completed",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    }
  ]);

  const tabs = [
    {id: "all", name: "Todas as compras"},
    {id: "completed", name: "Compras efetuadas"},
    {id: "pending", name: "Compras"}
  ];

  const filteredPurchases =
    selectedTab === "all"
      ? purchases
      : purchases.filter((purchase) => purchase.status === selectedTab);

  // Group purchases by month
  const groupedPurchases = filteredPurchases.reduce((groups, purchase) => {
    const date = new Date(purchase.date);
    const currentDate = new Date();

    let monthKey;
    if (
      date.getMonth() === currentDate.getMonth() &&
      date.getFullYear() === currentDate.getFullYear()
    ) {
      monthKey = "Mês atual";
    } else {
      monthKey = date.toLocaleDateString("pt-BR", {
        month: "long",
        year: "numeric"
      });
      monthKey = monthKey.charAt(0).toUpperCase() + monthKey.slice(1);
    }

    if (!groups[monthKey]) {
      groups[monthKey] = [];
    }
    groups[monthKey].push(purchase);
    return groups;
  }, {} as Record<string, Purchase[]>);

  const getStatusStyles = (status: string) => {
    switch (status) {
      case "completed":
        return {
          container: styles.completedStatus,
          text: styles.completedText
        };
      case "cancelled":
        return {
          container: styles.cancelledStatus,
          text: styles.cancelledText
        };
      case "pending":
      default:
        return {
          container: styles.pendingStatus,
          text: styles.pendingText
        };
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Efetuada";
      case "pending":
        return "Pendente";
      case "cancelled":
        return "Cancelada";
      case "refunded":
        return "Reembolsado";
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric"
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit"
    });
  };

  return (
    <ScreenWithHeader screenTitle="Histórico de compras" backButton>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[styles.tab, selectedTab === tab.id && styles.activeTab]}
                onPress={() => setSelectedTab(tab.id)}
              >
                <Text
                  style={[
                    styles.tabText,
                    selectedTab === tab.id && styles.activeTabText
                  ]}
                >
                  {tab.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Purchase List */}
          <ScrollView style={{flex: 1}}>
            {Object.entries(groupedPurchases).map(
              ([monthKey, monthPurchases], index) => (
                <View key={monthKey}>
                  {/* Month Label */}
                  <Text
                    style={[styles.monthLabel, index === 0 && {marginTop: 4}]}
                  >
                    {monthKey}
                  </Text>

                  {monthPurchases.map((purchase) => (
                    <TouchableOpacity
                      key={purchase.id}
                      style={styles.purchaseItem}
                      onPress={() => setSelectedPurchase(purchase)}
                    >
                      <View style={styles.purchaseIcon}>
                        <Text style={{fontSize: 16, color: "#FFFFFF"}}>📄</Text>
                      </View>

                      <View style={styles.purchaseContent}>
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "flex-start",
                            marginBottom: 6
                          }}
                        >
                          <Text style={styles.purchaseTitle}>
                            {purchase.title}
                          </Text>
                          <View
                            style={[
                              styles.purchaseStatus,
                              getStatusStyles(purchase.status).container
                            ]}
                          >
                            <Text
                              style={[
                                styles.statusText,
                                getStatusStyles(purchase.status).text
                              ]}
                            >
                              {getStatusText(purchase.status)}
                            </Text>
                          </View>
                        </View>
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "flex-end"
                          }}
                        >
                          <Text style={styles.purchaseDescription}>
                            {purchase.description || ""}
                          </Text>
                          <Text style={styles.purchasePrice}>
                            R$ {purchase.amount.toFixed(2)}
                          </Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )
            )}
          </ScrollView>
        </View>

        {/* Detail Modal */}
        <Modal
          visible={selectedPurchase !== null}
          transparent
          animationType="slide"
          onRequestClose={() => setSelectedPurchase(null)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <View style={styles.modalIndicator} />
                <Text style={styles.modalTitle}>Detalhes da compra</Text>
              </View>

              {selectedPurchase && (
                <ScrollView style={{flex: 1}}>
                  {/* Purchase Title */}
                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>
                      {selectedPurchase.title}
                    </Text>
                  </View>

                  {/* General Information */}
                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>
                      Informações gerais
                    </Text>

                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>Status</Text>
                      <Text
                        style={[
                          styles.detailValue,
                          getStatusStyles(selectedPurchase.status).text
                        ]}
                      >
                        {getStatusText(selectedPurchase.status)}
                      </Text>
                    </View>

                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>Valor do item</Text>
                      <Text style={styles.detailValue}>
                        R$ {selectedPurchase.amount.toFixed(2)}
                      </Text>
                    </View>

                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>Data/Hora</Text>
                      <Text style={styles.detailValue}>
                        {formatDate(selectedPurchase.date)} -{" "}
                        {formatTime(selectedPurchase.date)}
                      </Text>
                    </View>

                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>Tipo de item</Text>
                      <Text style={styles.detailValue}>
                        {selectedPurchase.itemType}
                      </Text>
                    </View>

                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>Tipo de pagamento</Text>
                      <Text style={styles.detailValue}>
                        {selectedPurchase.paymentMethod}
                      </Text>
                    </View>

                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>
                        Quantidade de parcelas
                      </Text>
                      <Text style={styles.detailValue}>2x sem juros</Text>
                    </View>

                    <View style={[styles.detailItem, styles.detailItemLast]}>
                      <Text style={styles.detailLabel}>Nº pedido</Text>
                      <Text style={styles.detailValue}>
                        {selectedPurchase.orderNumber}
                      </Text>
                    </View>
                  </View>

                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setSelectedPurchase(null)}
                  >
                    <Text style={styles.closeButtonText}>Fechar</Text>
                  </TouchableOpacity>
                </ScrollView>
              )}
            </View>
          </View>
        </Modal>
      </View>
    </ScreenWithHeader>
  );
};

export default HistoryPurchases;
