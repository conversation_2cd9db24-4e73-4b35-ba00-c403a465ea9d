import React from "react";
import {render, act} from "@testing-library/react-native";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({t: (k: string) => k})
}));

const mockServiceUpsell = jest.fn();

jest.mock("../../services/login.service", () => ({
  __esModule: true,
  default: {
    upsell: (...args: any[]) => {
      mockServiceUpsell(...args);
      return {
        subscribe: ({next}: any) =>
          next?.({
            accessToken: "test-token",
            tokenType: "Bearer",
            expiresIn: 3600
          })
      };
    }
  }
}));

const mockSafeParseUpsell = jest.fn();

jest.mock("../../models/login", () => ({
  UpsellRequestSchema: {
    safeParse: (...args: any) => mockSafeParseUpsell(...args)
  }
}));

const mockEmitError = jest.fn();
jest.mock("../../contexts/error-dialog-context", () => ({
  __esModule: true,
  ErrorType: {Warning: 0, Error: 1},
  useErrorMessage: () => ({emitError: mockEmitError})
}));

const mockSetCurrentLoading = jest.fn();
jest.mock("../../contexts/loading-context", () => ({
  __esModule: true,
  useLoading: () => ({setCurrentLoading: mockSetCurrentLoading})
}));

const mockSetLoginData = jest.fn();
jest.mock("../../contexts/session.context", () => ({
  __esModule: true,
  useSession: () => ({setLoginData: mockSetLoginData})
}));

const mockNavigate = jest.fn();
jest.mock("expo-router", () => ({
  __esModule: true,
  useRouter: () => ({navigate: mockNavigate})
}));

import useUpsell from "../../hooks/use-upsell";

function setup() {
  let hook: ReturnType<typeof useUpsell> | undefined;
  function Test() {
    hook = useUpsell();
    return null;
  }
  render(React.createElement(Test));
  return hook!;
}

describe("useUpsell hook", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("alerts on validation error", () => {
    mockSafeParseUpsell.mockReturnValue({
      success: false,
      error: {flatten: () => ({fieldErrors: {}})}
    });
    const hook = setup();
    act(() => {
      hook.upsell({} as any);
    });
    expect(mockEmitError).toHaveBeenCalled();
    expect(mockServiceUpsell).not.toHaveBeenCalled();
  });

  it("calls service on success", async () => {
    mockSafeParseUpsell.mockReturnValue({success: true, data: {}});
    const hook = setup();
    await act(async () => {
      hook.upsell({
        name: "John Doe",
        phoneNumber: "123456789",
        document: "12345678901"
      } as any);
    });
    expect(mockServiceUpsell).toHaveBeenCalled();
  });

  it("sets session data and navigates on successful response", async () => {
    mockSafeParseUpsell.mockReturnValue({success: true, data: {}});

    const hook = setup();
    await act(async () => {
      hook.upsell({
        name: "John Doe",
        phoneNumber: "123456789",
        document: "12345678901"
      } as any);
    });

    expect(mockSetLoginData).toHaveBeenCalledWith({
      token: "test-token",
      email: "<EMAIL>",
      name: "John Doe"
    });
    expect(mockNavigate).toHaveBeenCalledWith("(tabs)/home");
  });
});
