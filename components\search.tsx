import React, {useCallback, useMemo} from "react";
import styles from "../styles/components/search.style";
import InputField from "./input-field";
import FilterButton from "./filter-button";
import {ColorValue, StyleProp, View, ViewStyle} from "react-native";
import SearchIcon from "./icons/search-icon";
import {useTranslation} from "react-i18next";

export interface SearchProps {
    searchBarValue: string;
    onSearchBarChange?: (value: string) => void;
    searchBarPlaceholder?: string;
    style?: StyleProp<ViewStyle>
}

const Search: React.FC<SearchProps> = (props) => {
    const {t} = useTranslation();

    const searchFieldIcon = useCallback((errColor?: ColorValue) =>
        (<SearchIcon replaceColor={errColor}/>), []);

    const handleSearchFieldChange = useCallback((value: string) => props.onSearchBarChange?.(value),
        [props.searchBarValue]);

    const placeholder = useMemo(() => props.searchBarPlaceholder
        ?? t("calendar.searchTextFieldPlaceholder"), [t, props.searchBarPlaceholder]);

    return (
        <View style={[styles.searchBarLine, props.style]}>
            <InputField onChangeText={handleSearchFieldChange}
                        value={props.searchBarValue}
                        placeholder={placeholder}
                        icon={searchFieldIcon}
                        style={styles.searchBarField}/>
            <FilterButton/>
        </View>
    );
};

export default Search;