import React, {useCallback, useMemo, useState} from "react";
import {
    FlatList,
    Image,
    ListRenderItemInfo,
    Text,
    TouchableOpacity,
    View
} from "react-native";
import {useTranslation} from "react-i18next";
import ScreenWithHeader from "../../components/screen-with-header";

import DotsVerticalIcon from "../../components/icons/dots-vertical-icon";
import SmileIcon from "../../components/icons/smile-icon";
import SendIcon from "../../components/icons/send-icon";
import styles from "../../styles/logged-stack/chat.style";
import stylesConstants from "../../styles/styles-constants";
import InputField from "../../components/input-field";

interface MessageItem {
    id: string;
    text: string;
    isFromUser: boolean;
    time?: string;
    avatar?: string;
}

interface ChatSection {
    id: string;
    title: string;
    messages: MessageItem[];
}

const Chat: React.FC = () => {
    const {t} = useTranslation();
    const [messageText, setMessageText] = useState("");

    const chatData: ChatSection[] = useMemo(
        () => [
            {
                id: "yesterday",
                title: t("chat.yesterday"),
                messages: [
                    {
                        id: "1",
                        text: "Olá, terminei o documento de requisitos!",
                        isFromUser: false,
                        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
                    },
                    {
                        id: "2",
                        text: "Espetacular! Obrigado.",
                        isFromUser: true,
                        time: "11:41 AM"
                    },
                    {
                        id: "3",
                        text: "Terminei em boa hora — estava olhando isso agora.",
                        isFromUser: false,
                        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
                    }
                ]
            },
            {
                id: "today",
                title: t("chat.today"),
                messages: [
                    {
                        id: "4",
                        text: "Olá Maria, você pode avaliar o último design quando puder?",
                        isFromUser: false,
                        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
                    },
                    {
                        id: "5",
                        text: "Claro, darei uma olhada hoje.",
                        isFromUser: true,
                        time: "14:20 PM"
                    }
                ]
            }
        ],
        [t]
    );

    const renderSectionHeader = useCallback(
        (title: string) => (
            <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>{title}</Text>
            </View>
        ),
        []
    );

    const renderMessage = useCallback(
        (message: MessageItem) => (
            <View
                style={[
                    styles.messageRow,
                    message.isFromUser && styles.messageRowUser
                ]}
            >
                {!message.isFromUser && (
                    <View style={styles.avatarContainer}>
                        <Image
                            source={{uri: message.avatar}}
                            style={styles.avatar}
                        />
                        <View style={styles.avatarBorder} />
                        <View style={styles.onlineIndicator} />
                    </View>
                )}
                <View style={styles.selfMessageContainer}>
                    <View
                        style={[
                            styles.messageContainer,
                            message.isFromUser
                                ? styles.messageFromUser
                                : styles.messageFromOther
                        ]}
                    >
                        <Text style={styles.messageText}>{message.text}</Text>
                    </View>
                    {message.isFromUser && message.time && (
                        <View style={styles.timeContainer}>
                            <Text style={styles.timeText}>{message.time}</Text>
                        </View>
                    )}
                </View>
            </View>
        ),
        []
    );

    const renderSection = useCallback(
        (i: ListRenderItemInfo<ChatSection>) => (
            <View style={styles.section}>
                {renderSectionHeader(i.item.title)}
                <View style={styles.messagesContainer}>
                    {i.item.messages.map((message) => (
                        <React.Fragment key={message.id}>
                            {renderMessage(message)}
                        </React.Fragment>
                    ))}
                </View>
            </View>
        ),
        [renderMessage, renderSectionHeader]
    );

    const rightHeaderChild = useMemo(
        () => (
            <TouchableOpacity>
                <DotsVerticalIcon />
            </TouchableOpacity>
        ),
        []
    );

    const handleSendMessage = useCallback(() => {
        if (messageText.trim()) {
            setMessageText("");
        }
    }, [messageText]);

    const smileIcon = useCallback(() => <SmileIcon />, []);

    return (
        <ScreenWithHeader
            screenTitle="Phoenix Baker"
            backButton={true}
            rightHeaderChild={rightHeaderChild}
            disablePadding
            disableScrollView
        >
            <View style={[styles.container]}>
                <ScreenWithHeader.InternalPadding
                    style={styles.messageListContainer}
                >
                    <FlatList
                        data={chatData}
                        renderItem={renderSection}
                        keyExtractor={(item) => item.id}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={styles.chatContent}
                    />
                </ScreenWithHeader.InternalPadding>
                <View style={styles.inputContainer}>
                    <View style={styles.inputRow}>
                        <InputField
                            style={styles.textInput}
                            value={messageText}
                            onChangeText={setMessageText}
                            placeholder={t("chat.messagePlaceholder")}
                            icon={smileIcon}
                            backgroundColor={
                                stylesConstants.colors.inputBackground
                            }
                            height={"100%"}
                        />
                        <TouchableOpacity
                            style={styles.sendButton}
                            onPress={handleSendMessage}
                        >
                            <SendIcon />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </ScreenWithHeader>
    );
};

export default Chat;
