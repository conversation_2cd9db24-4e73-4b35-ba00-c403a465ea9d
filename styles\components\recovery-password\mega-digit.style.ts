import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        display: "flex",
        width: 45.496,
        paddingVertical: 1.422,
        paddingHorizontal: 5.687,
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        gap: 5.687,
        borderRadius: 5.687,
        borderWidth: 0.711,
        borderStyle: "solid",
        borderColor: stylesConstants.colors.gray300,
        backgroundColor: stylesConstants.colors.fullWhite
    },
    input: {
        fontSize: 48,
        fontStyle: "normal",
        fontWeight: 500,
        lineHeight: 60,
        letterSpacing: -0.96,
        width: 34.1217,
        textAlign: "center",
        fontFamily: stylesConstants.fonts.inter,
        color: stylesConstants.colors.brand.primary
    },
    onInputFillBorder: {
        borderColor: stylesConstants.colors.brand.primary,
        borderWidth: 1.422,
        borderStyle: "solid",
    }
});

export default styles;