import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    yearHeader: {
        flexDirection: "row",
        alignItems: "center",
        gap: 8
    },
    yearText: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: "600",
        lineHeight: 18
    },
    monthsContainer: {
        paddingVertical: 16
    },
    monthsList: {
        gap: 5
    },
    contentContainer: {
        flex: 1,
        gap: 24
    },
    searchSection: {
        gap: 16
    },
    sectionTitle: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: "700",
        lineHeight: 24
    },
    searchInput: {
        backgroundColor: stylesConstants.colors.highlightBackground
    },
    ticketsList: {
        flex: 1
    },
    ticketsScrollInternalContainer: {
        gap: 20
    },
    ticketContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        aspectRatio: 333 / 216
    }
});

export default styles;
