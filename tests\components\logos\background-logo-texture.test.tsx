import React from "react";
import { render } from "@testing-library/react-native";

jest.mock("expo-image", () => ({ Image: ({ ...props }: any) => <img {...props}  alt={"test"}/> }));

jest.mock("../../../styles/components/logos/background-logo-texture.style", () => ({
  imageContainer: {},
  image: {},
  backFilter: {}
}));

jest.mock("react-native", () => {
  const RN = jest.requireActual("react-native");
  RN.View = ({ children, ...rest }: any) => <div {...rest}>{children}</div>;
  return RN;
});

import BackgroundLogoTexture from "../../../components/logos/background-logo-texture";

describe("BackgroundLogoTexture", () => {
  it("renders container and image", () => {
    const { toJSON } = render(<BackgroundLogoTexture />);
    expect(toJSON()).not.toBeUndefined();
  });
});
