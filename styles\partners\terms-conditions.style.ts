import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  content: {
    paddingHorizontal: 24,
    paddingVertical: 24,
    flex: 1
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: "700",
    lineHeight: 28,
    marginBottom: 24,
    textAlign: "center"
  },
  description: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 22,
    marginBottom: 16,
    textAlign: "justify"
  },
  buttonContainer: {
    paddingHorizontal: 24,
    paddingVertical: 24,
    backgroundColor: stylesConstants.colors.mainBackground
  }
});

export default styles;
