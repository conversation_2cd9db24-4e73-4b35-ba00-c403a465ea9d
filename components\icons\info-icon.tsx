import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface InfoIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const InfoIcon: React.FC<InfoIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#FCFCFD", [props.replaceColor]);

    return (
        <Svg
            width={24}
            height={24}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10ZM12 16v-4M12 8h.01"
            />
        </Svg>
    );
};

export default InfoIcon;
