import {StyleSheet} from "react-native";
import stylesConstants from "@/styles/styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 40,
    paddingHorizontal: 24,
    justifyContent: "space-between"
  },
  logoContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24
  },
  textContainer: {
    marginBottom: 32
  },
  text: {
    alignSelf: "stretch",
    color: stylesConstants.colors.secondary,
    textAlign: "center",
    fontSize: 16,
    fontStyle: "normal",
    fontWeight: 400,
    lineHeight: 24,
    fontFamily: stylesConstants.fonts.openSans
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 30,
    fontStyle: "normal",
    fontWeight: 700,
    lineHeight: 38,
    marginBottom: 12
  },
  fieldsMargin: {
    marginBottom: 24
  },
  loginButtonMargin: {
    marginBottom: 32
  },
  forgetPasswordContainer: {
    width: "100%",
    alignItems: "center",
    paddingBottom: 20,
    flex: 0,
    paddingVertical: 10
  },
  clickableText: {
    fontFamily: stylesConstants.fonts.openSans,
    color: stylesConstants.colors.fullWhite,
    fontStyle: "normal",
    fontWeight: 400,
    lineHeight: 24,
    textDecorationLine: "underline",
    textDecorationStyle: "solid",
    fontSize: 16
  },
  createAccountContainer: {
    marginTop: 32
  },
  forgotPassword: {
    marginTop: 16
  },
  gap: {
    gap: 16
  }
});

export default styles;
