import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        backgroundColor: "#303030",
        borderRadius: 8,
        padding: 24,
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        gap: 12,
        alignSelf: "stretch",
        minHeight: 100,
        height: 100,
        maxHeight: 100,
    },
    bigIcon: {
        position: "absolute",
    },
    internalContainer: {
        display: "flex",
        flexDirection: "row",
        flex: 1,
        justifyContent: "space-between",
    },
    textContainer: {
        display: "flex",
        justifyContent: "center",
        gap: 4,
        maxWidth: 215
    },
    text: {
        color: stylesConstants.colors.gray200,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontStyle: "normal",
        fontWeight: 400,
        lineHeight: 18
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 20,
        fontWeight: 600,
        lineHeight: 30
    },
    iconContainer: {
        display: "flex",
        minWidth: 48,
        minHeight: 48,
        height: 48,
        width: 48,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 100
    },
    svgContainer: {
        width: 24,
        height: 24,
        minWidth: 24,
        minHeight: 24,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
    }
});

export default styles;