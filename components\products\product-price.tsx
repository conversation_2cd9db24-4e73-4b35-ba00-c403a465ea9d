import {View, Text} from "react-native";
import styles from "../../styles/components/products/product-price.style";
import {useTranslation} from "react-i18next";

export interface ProductPriceProps {
    price: number;
}

const ProductPrice: React.FC<ProductPriceProps> = (props) => {
    const {t} = useTranslation();

    return (
        <View style={{display: "flex", flexDirection: "column", gap: 4}}>
            <Text style={styles.label}>{t("products.priceLabel")}</Text>
            <Text style={styles.price}>
                R$ {(props.price / 100).toFixed(2).replace(".", ",")}
            </Text>
        </View>
    );
};

export default ProductPrice;
