import React from "react";
import {View} from "react-native";
import {SvgProps} from "react-native-svg";
import styles from "../../styles/components/product-page/payment-method-card.style";

interface PaymentMethodCardProps {
    icon: React.ComponentType<SvgProps>;
}

const PaymentMethodCard: React.FC<PaymentMethodCardProps> = ({icon: Icon}) => {
    return (
        <View style={styles.container}>
            <Icon />
        </View>
    );
};

export default PaymentMethodCard;
