import { of, firstValueFrom } from "rxjs";

const mockRequest = jest.fn();

jest.mock("../../services/api.service", () => ({
  __esModule: true,
  default: {
    request: (...args: any[]) => mockRequest(...args)
  }
}));

import LoginService from "../../services/login.service";

describe("LoginService", () => {
  beforeEach(() => { jest.clearAllMocks(); });

  it("login proxies to /app/auth/login", async () => {
    mockRequest.mockReturnValue(of({ token: "abc" }));
    const data = await firstValueFrom(LoginService.login({} as any));
    expect(data).toEqual({ token: "abc" });
    expect(mockRequest).toHaveBeenCalledWith("POST", "/app/auth/login", {});
  });

  it("forgetPassword proxies to /app/auth/forgot", () => {
    mockRequest.mockReturnValue(of({ ok: true }));
    LoginService.forgetPassword({ email: "a" } as any).subscribe();
    expect(mockRequest).toHaveBeenCalledWith("POST", "/app/auth/forgot", { email: "a" });
  });

  it("resetPassword proxies to /auth/reset", () => {
    mockRequest.mockReturnValue(of(null));
    LoginService.resetPassword({ code: "1" } as any).subscribe();
    expect(mockRequest).toHaveBeenCalledWith("POST", "/auth/reset", { code: "1" });
  });

  it("upsell proxies to /app/auth/upsell", async () => {
    mockRequest.mockReturnValue(of({ accessToken: "xyz", tokenType: "Bearer", expiresIn: 3600 }));
    const data = await firstValueFrom(LoginService.upsell({ 
      name: "John Doe", 
      phoneNumber: "123456789", 
      document: "12345678901" 
    } as any));
    expect(data).toEqual({ accessToken: "xyz", tokenType: "Bearer", expiresIn: 3600 });
    expect(mockRequest).toHaveBeenCalledWith("POST", "/app/auth/upsell", { 
      name: "John Doe", 
      phoneNumber: "123456789", 
      document: "12345678901" 
    });
  });
});
