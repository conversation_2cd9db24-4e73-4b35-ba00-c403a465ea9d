// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FullSizeButton component renders correctly and matches snapshot 1`] = `
<View
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    [
      {
        "alignItems": "center",
        "alignSelf": "stretch",
        "backgroundColor": "#0F7C4D",
        "borderColor": "#0F7C4D",
        "borderRadius": 8,
        "borderStyle": "solid",
        "borderWidth": 1,
        "columnGap": 8,
        "display": "flex",
        "flexDirection": "row",
        "gap": 8,
        "justifyContent": "center",
        "paddingHorizontal": 20,
        "paddingVertical": 12,
      },
      undefined,
      false,
    ]
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "flexDirection": "row",
        "gap": 8,
        "justifyContent": "center",
      }
    }
  >
    <Text
      style={
        [
          {
            "color": "#FFFFFF",
            "fontFamily": "Open Sans",
            "fontSize": 16,
            "fontStyle": "normal",
            "fontWeight": 600,
            "lineHeight": 24,
          },
          false,
        ]
      }
    >
      button.text
    </Text>
    <Text>
      Icon
    </Text>
  </View>
</View>
`;
