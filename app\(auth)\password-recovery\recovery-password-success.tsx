import React, {useCallback} from "react";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import Screen from "@/components/screen";
import {Text, View} from "react-native";
import FullSizeButton from "@/components/full-size-button";
import CheckCircleIcon from "@/components/icons/check-circle-icon";
import {useTranslation} from "react-i18next";
import styles from "@/styles/auth/password-recovery/recovery-password-success.style";
import {useRouter} from "expo-router";

const RecoveryPasswordSuccess: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();

  const onButtonPress = useCallback(
    () => router.replace("/(auth)/login"),
    [router]
  );

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={styles.container}>
          <View style={styles.icon}>
            <CheckCircleIcon />
          </View>
          <Text style={[styles.text, styles.title]}>
            {t("recoveryPasswordSuccess.successTitle")}
          </Text>
          <Text style={styles.text}>
            {t("recoveryPasswordSuccess.successDescription")}
          </Text>
        </View>
        <View style={styles.footerButton}>
          <FullSizeButton
            text="recoveryPasswordSuccess.successButton"
            onPress={onButtonPress}
          />
        </View>
      </Screen>
    </>
  );
};

export default RecoveryPasswordSuccess;
