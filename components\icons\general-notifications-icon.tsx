import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface GeneralNotificationsIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const GeneralNotificationsIcon: React.FC<GeneralNotificationsIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={16} height={20} viewBox="0 0 16 20" fill="none" {...props}>
      <Path
        d="M10.4998 15.8337C10.4998 17.2144 9.38048 18.3337 7.99977 18.3337C6.61906 18.3337 5.49977 17.2144 5.49977 15.8337M9.49685 5.19912C9.85977 4.82419 10.0831 4.31334 10.0831 3.75033C10.0831 2.59973 9.15036 1.66699 7.99977 1.66699C6.84917 1.66699 5.91643 2.59973 5.91643 3.75033C5.91643 4.31334 6.13977 4.82419 6.50268 5.19912M12.9998 9.33366C12.9998 8.18439 12.473 7.08219 11.5353 6.26953C10.5976 5.45687 9.32585 5.00033 7.99977 5.00033C6.67368 5.00033 5.40192 5.45687 4.46423 6.26953C3.52655 7.08219 2.99977 8.18439 2.99977 9.33366C2.99977 11.2352 2.52821 12.6258 1.93982 13.6209C1.26922 14.755 0.933914 15.3221 0.947155 15.4575C0.962303 15.6125 0.990197 15.6614 1.11588 15.7533C1.22574 15.8337 1.77769 15.8337 2.88159 15.8337H13.1179C14.2218 15.8337 14.7738 15.8337 14.8837 15.7533C15.0093 15.6614 15.0372 15.6125 15.0524 15.4575C15.0656 15.3221 14.7303 14.755 14.0597 13.6209C13.4713 12.6258 12.9998 11.2352 12.9998 9.33366Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default GeneralNotificationsIcon;
