import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 100 // Space for bottom purchase bar
  },
  // Header Styles
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24
  },
  headerTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  priceText: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24
  },
  // Event Card Styles
  eventCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    alignItems: "center"
  },
  eventIconContainer: {
    width: 64,
    height: 64,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16
  },
  eventTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 16
  },
  eventDescriptionContainer: {
    marginBottom: 16
  },
  eventDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    textAlign: "center"
  },
  seeMoreText: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  freeEventBadgeContainer: {
    alignItems: "center",
    marginBottom: 16
  },
  purchaseButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: "center",
    marginBottom: 16,
    width: "100%"
  },
  purchaseButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24
  },
  attendeesContainer: {
    flexDirection: "column",
    alignItems: "center",
    gap: 12
  },
  attendeesText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    textAlign: "center"
  },
  inviteButton: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16
  },
  inviteButtonText: {
    color: stylesConstants.colors.fullBlack,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 18
  },
  // Event Details Styles
  eventDetailsContainer: {
    marginBottom: 24
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 16
  },
  eventDetailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12
  },
  eventDetailLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  eventDetailValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    textAlign: "right",
    flex: 1,
    marginLeft: 16
  },
  eventTypeContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "flex-end"
  },
  eventTypeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: stylesConstants.colors.brand.primary,
    marginRight: 8
  },
  // Payment Methods Styles
  paymentMethodsContainer: {
    marginBottom: 24
  },
  paymentMethodsGrid: {
    flexDirection: "row",
    gap: 8,
    marginBottom: 8
  },
  paymentMethodIcon: {
    width: 40,
    height: 24,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  paymentMethodCard: {
    width: 24,
    height: 16,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 2
  },
  paymentMethodText: {
    fontSize: 16
  },
  installmentText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18
  },
  // Similar Events Styles
  similarEventsContainer: {
    marginBottom: 24
  },
  similarEventCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault,
    padding: 16
  },
  similarEventBadges: {
    flexDirection: "row",
    gap: 6,
    marginBottom: 12
  },
  similarEventTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20,
    marginBottom: 16
  },
  similarEventPricing: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  similarEventOldPrice: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    textDecorationLine: "line-through"
  },
  similarEventPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20
  },
  similarEventButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16
  },
  similarEventButtonText: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 700,
    lineHeight: 18
  },
  // Bottom Purchase Bar Styles
  bottomPurchaseBar: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    flexDirection: "row",
    paddingHorizontal: 24,
    paddingVertical: 16,
    gap: 12
  },
  cartIconButton: {
    width: 48,
    height: 48,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center"
  },
  cartIconText: {
    fontSize: 20
  },
  bottomPurchaseButton: {
    flex: 1,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center"
  },
  bottomPurchaseButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20
  }
});

export default styles;
