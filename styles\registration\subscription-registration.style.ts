import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: stylesConstants.colors.primary,
    },
    contentContainer: {
        flex: 1,
        paddingHorizontal: 24,
        paddingVertical: 40,
    },
    headerContainer: {
        alignItems: "center",
        marginBottom: 32,
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 24,
        fontWeight: 700,
        lineHeight: 32,
        textAlign: "center",
        marginBottom: 8,
    },
    subtitle: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 400,
        lineHeight: 24,
        textAlign: "center",
    },
    plansContainer: {
        gap: 16,
        marginBottom: 32,
    },
    planCard: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        padding: 20,
        borderWidth: 2,
        borderColor: stylesConstants.colors.borderDefault,
    },
    planCardSelected: {
        borderColor: stylesConstants.colors.brand.primary,
        backgroundColor: stylesConstants.colors.highlightBackground,
    },
    planHeader: {
        alignItems: "center",
        marginBottom: 16,
    },
    planName: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 20,
        fontWeight: 700,
        lineHeight: 28,
        marginBottom: 4,
    },
    planPrice: {
        color: stylesConstants.colors.brand.primary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 24,
        fontWeight: 700,
        lineHeight: 32,
        marginBottom: 4,
    },
    planPeriod: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
    },
    planFeatures: {
        gap: 8,
    },
    featureItem: {
        flexDirection: "row",
        alignItems: "center",
        gap: 8,
    },
    featureIcon: {
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: stylesConstants.colors.brand.primary,
        alignItems: "center",
        justifyContent: "center",
    },
    featureText: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
        flex: 1,
    },
    popularBadge: {
        position: "absolute",
        top: -8,
        right: 16,
        backgroundColor: stylesConstants.colors.brand.primary,
        borderRadius: 12,
        paddingHorizontal: 12,
        paddingVertical: 4,
    },
    popularText: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 10,
        fontWeight: 700,
        lineHeight: 14,
    },
    summaryContainer: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        padding: 16,
        marginBottom: 24,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
    },
    summaryTitle: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 600,
        lineHeight: 24,
        marginBottom: 12,
    },
    summaryRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 8,
    },
    summaryLabel: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
    },
    summaryValue: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 600,
        lineHeight: 20,
    },
    totalRow: {
        borderTopWidth: 1,
        borderTopColor: stylesConstants.colors.borderDefault,
        paddingTop: 12,
        marginTop: 12,
    },
    totalValue: {
        color: stylesConstants.colors.brand.primary,
        fontSize: 16,
        fontWeight: 700,
    },
    buttonContainer: {
        gap: 16,
    },
});

export default styles;
