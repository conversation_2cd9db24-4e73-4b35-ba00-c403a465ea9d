import React, {useState, useCallback} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  ColorValue
} from "react-native";
import ScreenWithHeader from "@/components/screen-with-header";
import {useTranslation} from "react-i18next";
import InputField from "@/components/input-field";
import FullSizeButton from "@/components/full-size-button";
import InvisibleFullSizeButton from "@/components/invisible-full-size-button";
import UserIcon from "@/components/icons/user-icon";
import PhoneIcon from "@/components/icons/phone-icon";
import CalendarIcon from "@/components/icons/calendar-icon";
import LicenceThirdPartyIcon from "@/components/icons/licence-third-party-icon";
import UserTabs from "@/components/user/user-tabs";
import {useBottomModal} from "@/contexts/bottom-modal-context";
import BottomModal from "@/components/bottom-modal";
import styles from "@/styles/profile/edit-profile.style";

interface EditableProfile {
  name: string;
  email: string;
  cpf: string;
  phone: string;
  birthDate: string;
}

interface Tab {
  title: string;
  id: number;
}

enum ProfileTabsEnum {
  PersonalData = 1,
  ChangePassword = 2
}

interface PhotoSelectionModalProps {
  onSelectFromGallery: () => void;
  onTakePhoto: () => void;
  onCancel: () => void;
}

const PhotoSelectionModal: React.FC<PhotoSelectionModalProps> = ({
  onSelectFromGallery,
  onTakePhoto,
  onCancel
}) => {
  const {t} = useTranslation();

  return (
    <BottomModal.Container>
      <BottomModal.ButtonsContainer>
        <FullSizeButton
          text={t("profile.selectFromGallery", "Selecionar da galeria")}
          onPress={onSelectFromGallery}
        />
        <InvisibleFullSizeButton
          text={t("profile.takePhoto", "Tirar uma foto")}
          onPress={onTakePhoto}
        />
        <InvisibleFullSizeButton
          text={t("profile.cancel", "Cancelar")}
          onPress={onCancel}
        />
      </BottomModal.ButtonsContainer>
    </BottomModal.Container>
  );
};

const EditProfile: React.FC = () => {
  const {t} = useTranslation();
  const {openModal, closeModal} = useBottomModal();
  const [activeTab, setActiveTab] = useState(ProfileTabsEnum.PersonalData);
  const [profile, setProfile] = useState<EditableProfile>({
    name: "Maria Aparecida",
    email: "<EMAIL>",
    cpf: "000.000.000-00",
    phone: "+55 47 0000-0000",
    birthDate: "07/04/1976"
  });

  const [avatar] = useState(
    "https://via.placeholder.com/120x120/007AFF/FFFFFF?text=MA"
  );
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const tabs: Tab[] = [
    {
      title: t("profile.personalData", "Dados pessoais"),
      id: ProfileTabsEnum.PersonalData
    },
    {
      title: t("profile.changePassword", "Alterar senha"),
      id: ProfileTabsEnum.ChangePassword
    }
  ];

  const handleInputChange =
    (field: keyof EditableProfile) => (value: string) => {
      setProfile((prev) => ({
        ...prev,
        [field]: value
      }));
    };

  const handleTabChange = useCallback((id: number) => {
    setActiveTab(id);
  }, []);

  const handleSaveProfile = () => {
    // Validate required fields
    if (!profile.name.trim()) {
      console.log("Nome é obrigatório");
      return;
    }

    if (!profile.email.trim()) {
      console.log("Email é obrigatório");
      return;
    }

    // Simulate saving
    console.log("Profile saved successfully");
  };

  const handleChangePassword = () => {
    if (
      !currentPassword.trim() ||
      !newPassword.trim() ||
      !confirmPassword.trim()
    ) {
      console.log("Todos os campos são obrigatórios");
      return;
    }

    if (newPassword !== confirmPassword) {
      console.log("As senhas não coincidem");
      return;
    }

    console.log("Password changed successfully");
  };

  const handleChangePhoto = () => {
    openModal({
      title: "",
      children: (
        <PhotoSelectionModal
          onSelectFromGallery={() => {
            closeModal();
            console.log("Select from gallery");
          }}
          onTakePhoto={() => {
            closeModal();
            console.log("Take photo");
          }}
          onCancel={closeModal}
        />
      )
    });
  };

  const userIcon = useCallback(
    (errorColor?: ColorValue) => <UserIcon replaceColor={errorColor} />,
    []
  );

  const phoneIcon = useCallback(
    (errorColor?: ColorValue) => <PhoneIcon replaceColor={errorColor} />,
    []
  );

  const calendarIcon = useCallback(
    (errorColor?: ColorValue) => <CalendarIcon replaceColor={errorColor} />,
    []
  );

  const documentIcon = useCallback(
    (errorColor?: ColorValue) => (
      <LicenceThirdPartyIcon replaceColor={errorColor} />
    ),
    []
  );

  return (
    <ScreenWithHeader
      screenTitle={t("profile.profileInfo", "Informações de perfil")}
      backButton
    >
      <View style={styles.container}>
        {/* Tabs */}
        <UserTabs
          tabs={tabs}
          currentTab={activeTab}
          onTabChange={handleTabChange}
          style={styles.tabsContainer}
        />

        <ScrollView style={styles.contentContainer}>
          {activeTab === ProfileTabsEnum.PersonalData ? (
            <>
              {/* Profile Photo */}
              <View style={styles.headerContainer}>
                <TouchableOpacity
                  style={styles.avatarContainer}
                  onPress={handleChangePhoto}
                >
                  <Image source={{uri: avatar}} style={styles.avatar} />
                </TouchableOpacity>
                <TouchableOpacity onPress={handleChangePhoto}>
                  <Text style={styles.changePhotoText}>
                    {t("profile.changePhoto", "Alterar foto")}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Form Fields */}
              <View style={styles.formContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.fullName", "Nome completo")}
                  </Text>
                  <InputField
                    value={profile.name}
                    onChangeText={handleInputChange("name")}
                    placeholder={t("profile.enterName", "Maria Aparecida")}
                    icon={userIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.email", "E-mail")}
                  </Text>
                  <InputField
                    value={profile.email}
                    onChangeText={handleInputChange("email")}
                    placeholder={t(
                      "profile.enterEmail",
                      "<EMAIL>"
                    )}
                    inputMode="email"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>{t("profile.cpf", "CPF")}</Text>
                  <InputField
                    value={profile.cpf}
                    onChangeText={handleInputChange("cpf")}
                    placeholder={t("profile.enterCpf", "000.000.000-00")}
                    icon={documentIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.phone", "Telefone")}
                  </Text>
                  <InputField
                    value={profile.phone}
                    onChangeText={handleInputChange("phone")}
                    placeholder={t("profile.enterPhone", "+55 47 0000-0000")}
                    icon={phoneIcon}
                    inputMode="tel"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.birthDate", "Data de nascimento")}
                  </Text>
                  <InputField
                    value={profile.birthDate}
                    onChangeText={handleInputChange("birthDate")}
                    placeholder={t("profile.enterBirthDate", "07/04/1976")}
                    icon={calendarIcon}
                  />
                </View>
              </View>

              {/* Buttons */}
              <View style={styles.buttonContainer}>
                <FullSizeButton
                  text={t("profile.saveChanges", "Salvar alterações")}
                  onPress={handleSaveProfile}
                />
                <InvisibleFullSizeButton
                  text={t("profile.backToProfile", "Voltar para perfil")}
                  onPress={() => console.log("Back to profile")}
                />
              </View>
            </>
          ) : (
            <>
              {/* Change Password Form */}
              <View style={styles.formContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.currentPassword", "Senha atual")}
                  </Text>
                  <InputField
                    value={currentPassword}
                    onChangeText={setCurrentPassword}
                    placeholder={t(
                      "profile.enterCurrentPassword",
                      "••••••••••••"
                    )}
                    isPassword
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.newPassword", "Nova senha")}
                  </Text>
                  <InputField
                    value={newPassword}
                    onChangeText={setNewPassword}
                    placeholder={t("profile.enterNewPassword", "••••••••••••")}
                    isPassword
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.confirmPassword", "Nova senha")}
                  </Text>
                  <InputField
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    placeholder={t(
                      "profile.enterConfirmPassword",
                      "••••••••••••"
                    )}
                    isPassword
                  />
                </View>

                <Text style={styles.passwordHint}>
                  {t(
                    "profile.passwordHint",
                    "Sua senha deve conter ao menos 8 caracteres."
                  )}
                </Text>
              </View>

              {/* Password Change Buttons */}
              <View style={styles.buttonContainer}>
                <FullSizeButton
                  text={t("profile.changePassword", "Alterar senha")}
                  onPress={handleChangePassword}
                />
                <InvisibleFullSizeButton
                  text={t("profile.back", "Voltar")}
                  onPress={() => console.log("Back")}
                />
              </View>
            </>
          )}
        </ScrollView>
      </View>
    </ScreenWithHeader>
  );
};

export default EditProfile;
