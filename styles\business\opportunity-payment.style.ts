import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: stylesConstants.colors.mainBackground,
    },
    contentContainer: {
        paddingHorizontal: 24,
        paddingVertical: 16,
    },
    headerContainer: {
        marginBottom: 24,
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 24,
        fontWeight: 700,
        lineHeight: 32,
        marginBottom: 8,
    },
    summaryContainer: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        padding: 16,
        marginBottom: 24,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
    },
    summaryTitle: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 600,
        lineHeight: 24,
        marginBottom: 12,
    },
    summaryRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 8,
    },
    summaryLabel: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
    },
    summaryValue: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 600,
        lineHeight: 20,
    },
    totalRow: {
        borderTopWidth: 1,
        borderTopColor: stylesConstants.colors.borderDefault,
        paddingTop: 12,
        marginTop: 12,
    },
    totalValue: {
        color: stylesConstants.colors.brand.primary,
        fontSize: 16,
        fontWeight: 700,
    },
    paymentMethodsContainer: {
        marginBottom: 24,
    },
    sectionTitle: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 600,
        lineHeight: 24,
        marginBottom: 16,
    },
    buttonContainer: {
        gap: 16,
    },
});

export default styles;
