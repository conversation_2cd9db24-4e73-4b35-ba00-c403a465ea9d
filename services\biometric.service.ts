import {from, Observable} from "rxjs";
import * as LocalAuthentication from "expo-local-authentication";
import {TFunction} from "i18next";

class BiometricService {
    public static createBiometricConfig(t: TFunction<"translation", undefined>)
        : LocalAuthentication.LocalAuthenticationOptions {
        return {
            promptMessage: t("login.biometricPrompt"),
            fallbackLabel: t("login.biometricFallback"),
            disableDeviceFallback: false,
        }
    }

    public static hasBiometric(config: LocalAuthentication.LocalAuthenticationOptions): Observable<boolean> {
        return from((async () => {
            if (await LocalAuthentication.hasHardwareAsync() &&
                await LocalAuthentication.isEnrolledAsync()) {
                const resultAuth = await LocalAuthentication.authenticateAsync(config);
                return resultAuth.success;
            }

            return false;
        })());
    }
}

export default BiometricService;