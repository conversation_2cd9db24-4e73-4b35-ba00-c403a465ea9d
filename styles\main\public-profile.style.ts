import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: stylesConstants.colors.mainBackground,
    },
    contentContainer: {
        paddingHorizontal: 24,
        paddingVertical: 16,
    },
    profileHeader: {
        alignItems: "center",
        marginBottom: 32,
    },
    avatarContainer: {
        width: 120,
        height: 120,
        borderRadius: 60,
        marginBottom: 16,
        backgroundColor: stylesConstants.colors.gray300,
        alignItems: "center",
        justifyContent: "center",
    },
    avatar: {
        width: "100%",
        height: "100%",
        borderRadius: 60,
    },
    avatarBorder: {
        position: "absolute",
        width: "100%",
        height: "100%",
        borderRadius: 60,
        borderWidth: 3,
        borderColor: stylesConstants.colors.brand.primary,
    },
    userName: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 24,
        fontWeight: 700,
        lineHeight: 32,
        textAlign: "center",
        marginBottom: 4,
    },
    userTitle: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 400,
        lineHeight: 24,
        textAlign: "center",
        marginBottom: 8,
    },
    userLocation: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
        textAlign: "center",
    },
    statsContainer: {
        flexDirection: "row",
        justifyContent: "space-around",
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        padding: 16,
        marginBottom: 24,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
    },
    statItem: {
        alignItems: "center",
    },
    statValue: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 20,
        fontWeight: 700,
        lineHeight: 28,
        marginBottom: 4,
    },
    statLabel: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18,
    },
    sectionContainer: {
        marginBottom: 24,
    },
    sectionTitle: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 600,
        lineHeight: 24,
        marginBottom: 12,
    },
    bioText: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
    },
    skillsContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8,
    },
    skillTag: {
        backgroundColor: stylesConstants.colors.brand.brand25,
        borderRadius: 16,
        paddingHorizontal: 12,
        paddingVertical: 6,
    },
    skillText: {
        color: stylesConstants.colors.brand.primary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 600,
        lineHeight: 18,
    },
    buttonContainer: {
        gap: 16,
    },
});

export default styles;
