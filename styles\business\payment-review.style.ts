import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    marginBottom: 8
  },
  sectionContainer: {
    marginBottom: 24
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 16
  },
  productContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  productIcon: {
    width: 48,
    height: 48,
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  productIconText: {
    fontSize: 20
  },
  productInfo: {
    flex: 1,
    marginRight: 12
  },
  productName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 22,
    marginBottom: 4
  },
  productDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16
  },
  productPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 700,
    lineHeight: 24
  },
  paymentMethodContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  paymentMethodIcon: {
    width: 40,
    height: 40,
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  paymentMethodInfo: {
    flex: 1
  },
  paymentMethodName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    marginBottom: 2
  },
  paymentMethodStatus: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16
  },
  orderSummaryContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  orderItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12
  },
  orderItemName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    flex: 1
  },
  orderItemPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    paddingTop: 12,
    marginTop: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  totalLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24
  },
  totalValue: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 700,
    lineHeight: 26
  },
  buttonContainer: {
    gap: 16
  }
});

export default styles;
