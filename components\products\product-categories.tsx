import React from "react";
import {useTranslation} from "react-i18next";
import {View, Text} from "react-native";
import CategoryButton from "./category-button";
import BookIcon from "../icons/book-icon";
import GraduationIcon from "../icons/graduation-icon";
import HandshakeIcon from "../icons/handshake-icon";
import GroupIcon from "../icons/group-icon";
import StandIcon from "../icons/stand-icon";
import PenIcon from "../icons/pen-icon";
import styles from "../../styles/components/products/product-categories.style";

const ProductCategories: React.FC = () => {
    const {t} = useTranslation();

    return (
        <View style={styles.container}>
            <Text style={styles.title}>
                {t("products.categories.title")}
            </Text>
            <View style={styles.categoriesContainer}>
                <CategoryButton
                    title={t("products.categories.ebooks")}
                    icon={<BookIcon width={20} height={20} />}
                />
                <CategoryButton
                    title={t("products.categories.courses")}
                    icon={<GraduationIcon width={20} height={20} />}
                />
                <CategoryButton
                    title={t("products.categories.meetings")}
                    icon={<HandshakeIcon width={20} height={20} />}
                />
                <CategoryButton
                    title={t("products.categories.groupMeetings")}
                    icon={<GroupIcon width={20} height={20} />}
                />
                <CategoryButton
                    title={t("products.categories.mentoring")}
                    icon={<StandIcon width={20} height={20} />}
                />
                <CategoryButton
                    title={t("products.categories.services")}
                    icon={<PenIcon width={20} height={20} />}
                />
            </View>
        </View>
    );
};

export default ProductCategories;
