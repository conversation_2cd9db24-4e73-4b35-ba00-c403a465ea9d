import React from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import Screen<PERSON>ithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import CheckCircleIcon from "../../components/icons/check-circle-icon";

const PublicationConfirmation: React.FC = () => {
    const {t} = useTranslation();

    const opportunityDetails = {
        title: "Desenvolvedor React Senior",
        plan: t("publicationConfirmation.planPremium", "Premium"),
        duration: t("publicationConfirmation.duration", "15 dias"),
        publishDate: new Date().toLocaleDateString('pt-BR'),
        expiryDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR')
    };

    return (
        <ScreenWithHeader screenTitle={t("publicationConfirmation.title", "Publicação Confirmada")} backButton>
            <ScrollView style={{flex: 1, padding: 20}}>
                {/* Success Icon */}
                <View style={{alignItems: 'center', marginBottom: 30}}>
                    <CheckCircleIcon width={80} height={80} />
                    <Text style={{color: '#fff', fontSize: 24, fontWeight: 'bold', textAlign: 'center', marginTop: 15}}>
                        {t("publicationConfirmation.success", "Oportunidade Publicada!")}
                    </Text>
                    <Text style={{color: '#ccc', fontSize: 16, textAlign: 'center', marginTop: 10}}>
                        {t("publicationConfirmation.successMessage", "Sua oportunidade foi publicada com sucesso e já está visível para outros membros.")}
                    </Text>
                </View>

                {/* Opportunity Details */}
                <View style={{backgroundColor: 'rgba(255,255,255,0.1)', padding: 20, borderRadius: 12, marginBottom: 20}}>
                    <Text style={{color: '#fff', fontSize: 18, fontWeight: 'bold', marginBottom: 15}}>
                        {t("publicationConfirmation.opportunityDetails", "Detalhes da Oportunidade")}
                    </Text>
                    
                    <View style={{marginBottom: 10}}>
                        <Text style={{color: '#ccc', fontSize: 14}}>
                            {t("publicationConfirmation.opportunityTitle", "Título:")}
                        </Text>
                        <Text style={{color: '#fff', fontSize: 16, fontWeight: 'bold'}}>
                            {opportunityDetails.title}
                        </Text>
                    </View>

                    <View style={{marginBottom: 10}}>
                        <Text style={{color: '#ccc', fontSize: 14}}>
                            {t("publicationConfirmation.selectedPlan", "Plano:")}
                        </Text>
                        <Text style={{color: '#fff', fontSize: 16}}>
                            {opportunityDetails.plan} - {opportunityDetails.duration}
                        </Text>
                    </View>

                    <View style={{marginBottom: 10}}>
                        <Text style={{color: '#ccc', fontSize: 14}}>
                            {t("publicationConfirmation.publishDate", "Data de Publicação:")}
                        </Text>
                        <Text style={{color: '#fff', fontSize: 16}}>
                            {opportunityDetails.publishDate}
                        </Text>
                    </View>

                    <View>
                        <Text style={{color: '#ccc', fontSize: 14}}>
                            {t("publicationConfirmation.expiryDate", "Data de Expiração:")}
                        </Text>
                        <Text style={{color: '#fff', fontSize: 16}}>
                            {opportunityDetails.expiryDate}
                        </Text>
                    </View>
                </View>

                {/* Next Steps */}
                <View style={{backgroundColor: 'rgba(0,122,255,0.1)', padding: 20, borderRadius: 12, marginBottom: 20, borderWidth: 1, borderColor: '#007AFF'}}>
                    <Text style={{color: '#007AFF', fontSize: 16, fontWeight: 'bold', marginBottom: 15}}>
                        {t("publicationConfirmation.nextSteps", "Próximos Passos")}
                    </Text>
                    
                    <View style={{gap: 10}}>
                        <Text style={{color: '#ccc', fontSize: 14}}>
                            ✓ {t("publicationConfirmation.step1", "Sua oportunidade está ativa e visível")}
                        </Text>
                        <Text style={{color: '#ccc', fontSize: 14}}>
                            ✓ {t("publicationConfirmation.step2", "Você receberá notificações sobre candidaturas")}
                        </Text>
                        <Text style={{color: '#ccc', fontSize: 14}}>
                            ✓ {t("publicationConfirmation.step3", "Acompanhe o desempenho na Central de Negócios")}
                        </Text>
                        <Text style={{color: '#ccc', fontSize: 14}}>
                            ✓ {t("publicationConfirmation.step4", "Gerencie candidaturas através do app")}
                        </Text>
                    </View>
                </View>

                {/* Statistics Preview */}
                <View style={{backgroundColor: 'rgba(255,255,255,0.05)', padding: 20, borderRadius: 12, marginBottom: 20}}>
                    <Text style={{color: '#fff', fontSize: 16, fontWeight: 'bold', marginBottom: 15}}>
                        {t("publicationConfirmation.currentStats", "Estatísticas Atuais")}
                    </Text>
                    
                    <View style={{flexDirection: 'row', justifyContent: 'space-around'}}>
                        <View style={{alignItems: 'center'}}>
                            <Text style={{color: '#fff', fontSize: 20, fontWeight: 'bold'}}>0</Text>
                            <Text style={{color: '#ccc', fontSize: 12}}>
                                {t("publicationConfirmation.views", "Visualizações")}
                            </Text>
                        </View>
                        <View style={{alignItems: 'center'}}>
                            <Text style={{color: '#fff', fontSize: 20, fontWeight: 'bold'}}>0</Text>
                            <Text style={{color: '#ccc', fontSize: 12}}>
                                {t("publicationConfirmation.applications", "Candidaturas")}
                            </Text>
                        </View>
                        <View style={{alignItems: 'center'}}>
                            <Text style={{color: '#fff', fontSize: 20, fontWeight: 'bold'}}>15</Text>
                            <Text style={{color: '#ccc', fontSize: 12}}>
                                {t("publicationConfirmation.daysLeft", "Dias Restantes")}
                            </Text>
                        </View>
                    </View>
                </View>

                {/* Action Buttons */}
                <View style={{gap: 10, marginBottom: 20}}>
                    <FullSizeButton
                        text={t("publicationConfirmation.viewOpportunity", "Ver Oportunidade")}
                        onPress={() => {}}
                    />
                    
                    <TouchableOpacity
                        style={{
                            backgroundColor: 'rgba(255,255,255,0.1)',
                            padding: 15,
                            borderRadius: 8,
                            alignItems: 'center'
                        }}
                    >
                        <Text style={{color: '#fff', fontSize: 16}}>
                            {t("publicationConfirmation.manageOpportunities", "Gerenciar Oportunidades")}
                        </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                        style={{
                            backgroundColor: 'rgba(255,255,255,0.1)',
                            padding: 15,
                            borderRadius: 8,
                            alignItems: 'center'
                        }}
                    >
                        <Text style={{color: '#fff', fontSize: 16}}>
                            {t("publicationConfirmation.createAnother", "Criar Outra Oportunidade")}
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Support */}
                <View style={{backgroundColor: 'rgba(255,255,255,0.05)', padding: 15, borderRadius: 8}}>
                    <Text style={{color: '#ccc', fontSize: 12, textAlign: 'center', lineHeight: 18}}>
                        {t("publicationConfirmation.support", "Precisa de ajuda? Entre em contato com nosso suporte através do menu de configurações.")}
                    </Text>
                </View>
            </ScrollView>
        </ScreenWithHeader>
    );
};

export default PublicationConfirmation;
