<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-updates-interface\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-linking" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-linking\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-json-utils\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-manifests\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-image-manipulator" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-image-manipulator\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-image-loader" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-image-loader\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-client\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="app.config" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out\app.config"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-modules-core\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu-interface\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="dev-menu-packager-host" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\dev-menu-packager-host"/><file name="EXDevMenuApp.android.js" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\EXDevMenuApp.android.js"/><file name="Inter-Black.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Black.otf"/><file name="Inter-Bold.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Bold.otf"/><file name="Inter-ExtraBold.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraLight.otf"/><file name="Inter-Light.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Light.otf"/><file name="Inter-Medium.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Medium.otf"/><file name="Inter-Regular.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Regular.otf"/><file name="Inter-SemiBold.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-SemiBold.otf"/><file name="Inter-Thin.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Thin.otf"/></source></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="expo_dev_launcher_android.bundle" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\expo_dev_launcher_android.bundle"/><file name="Inter-Black.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Black.otf"/><file name="Inter-Bold.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Bold.otf"/><file name="Inter-ExtraBold.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraLight.otf"/><file name="Inter-Light.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Light.otf"/><file name="Inter-Medium.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Medium.otf"/><file name="Inter-Regular.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Regular.otf"/><file name="Inter-SemiBold.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-SemiBold.otf"/><file name="Inter-Thin.otf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Thin.otf"/></source></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native-svg\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-pager-view" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native-pager-view\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\assets"><file name="fonts/Inter-Italic-VariableFont_opsz,wght.ttf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\assets\fonts\Inter-Italic-VariableFont_opsz,wght.ttf"/><file name="fonts/Inter-VariableFont_opsz,wght.ttf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\assets\fonts\Inter-VariableFont_opsz,wght.ttf"/><file name="fonts/OpenSans-Italic-VariableFont_wdth,wght.ttf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\assets\fonts\OpenSans-Italic-VariableFont_wdth,wght.ttf"/><file name="fonts/OpenSans-VariableFont_wdth,wght.ttf" path="C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\assets\fonts\OpenSans-VariableFont_wdth,wght.ttf"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>