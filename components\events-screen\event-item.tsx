import React, {useMemo} from "react";
import {Text, TouchableOpacity, View} from "react-native";
import styles from "../../styles/components/events-screen/event-item.style";
import Badge, {BadgeColor} from "../badge";
import {useTranslation} from "react-i18next";
import stylesConstants from "../../styles/styles-constants";
import HandshakeIcon from "../icons/handshake-icon";
import CartPlusIcon from "../cart-plus-icon";
import AnnounceIcon from "../icons/announce-icon";
import ChevronRightIcon from "../icons/chevron-right-icon";

const EventItem: React.FC = () => {
    const {t} = useTranslation();

    const freeBadgeColor: BadgeColor = useMemo(() => ({
        borderColor: stylesConstants.colors.success200,
        textColor: stylesConstants.colors.success700,
        backgroundColor: stylesConstants.colors.success50
    }), []);

    const businessBadgeColor: BadgeColor = useMemo(() => ({
        borderColor: stylesConstants.colors.gray200,
        textColor: stylesConstants.colors.gray700,
        backgroundColor: stylesConstants.colors.gray50
    }), []);

    return (
        <View style={styles.container}>
            <View style={styles.headerContainer}>
                <View style={styles.badgeContainer}>
                    <Badge icon={<HandshakeIcon stroke={stylesConstants.colors.gray700}/>} color={businessBadgeColor}
                           text={t("eventList.badges.business")}/>
                    <Badge color={freeBadgeColor} text={t("eventList.badges.freeEvent")}/>
                </View>
                <TouchableOpacity>
                    <CartPlusIcon/>
                </TouchableOpacity>
            </View>
            <View style={styles.titleContainer}>
                <View style={styles.iconContainer}>
                    <AnnounceIcon replaceColor={stylesConstants.colors.fullWhite} width={24} height={24}/>
                </View>
                <Text style={[styles.text, styles.titleText]} ellipsizeMode="tail" numberOfLines={2}>Encontro anual de
                    empreendedores em Balneário Camboriú - SC</Text>
            </View>
            <Text style={[styles.text, styles.description]}>Lorem ipsum quisque lobortis in eu rhoncus dui nulla lectus sagittis dictum
                dignissim...</Text>
            <View style={styles.footerContainer}>
                <View>
                    <Text style={styles.text}>{t("eventList.from")}</Text>
                    <Text style={[styles.text, styles.priceText]}>{t("eventList.price", {
                        price: "0,00"
                    })}</Text>
                </View>
                <TouchableOpacity style={styles.viewEventButton}>
                    <Text style={[styles.text, styles.viewEventButtonText]}>{t("eventList.viewEvent")}</Text>
                    <ChevronRightIcon/>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default EventItem;