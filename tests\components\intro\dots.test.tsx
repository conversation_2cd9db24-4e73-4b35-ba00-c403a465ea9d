import React from "react";
import { render } from "@testing-library/react-native";
import { View, StyleSheet } from "react-native";
import Dots from "../../../components/intro/dots";

describe("Dots component", () => {
    it("renders three dots and matches snapshot", () => {
        const { UNSAFE_getAllByType, toJSON } = render(<Dots dotState={0} />);

        // The first View is the container; the next three are the dots
        const views = UNSAFE_getAllByType(View);
        const dots = views.slice(1);
        expect(dots).toHaveLength(3);
        expect(toJSON()).toMatchSnapshot();
    });

    it("applies correct active width for current index", () => {
        const { UNSAFE_getAllByType } = render(<Dots dotState={1} />);
        const views = UNSAFE_getAllByType(View);
        const dots = views.slice(1);

        // Active dot should be the second one (index 1)
        const activeDotStyle = StyleSheet.flatten(dots[1].props.style);
        const inactiveDotStyle = StyleSheet.flatten(dots[0].props.style);

        expect(activeDotStyle.width).toBeGreaterThan(inactiveDotStyle.width);
    });
}); 