import React, {useEffect} from "react";
import Animated, {interpolateColor, useAnimatedStyle, useSharedValue, withTiming} from "react-native-reanimated";
import stylesConstants from "../../styles/styles-constants";
import {TouchableWithoutFeedback} from "react-native";
import styles from "../../styles/components/login/select-button.style";
import {SelectOptions} from "./select-button";

export interface OptionButtonProps {
    option: SelectOptions;
    active: boolean;
    onPress: () => void;
}

const OptionButton: React.FC<OptionButtonProps> = (props) => {
    const progress = useSharedValue(props.active ? 1 : 0);

    useEffect(() => {
        progress.value = withTiming(props.active ? 1 : 0, {duration: 250});
    }, [props.active]);

    const animatedButtonStyle = useAnimatedStyle(() => (
        {
            backgroundColor: interpolateColor(
                progress.value,
                [0, 1],
                ["transparent", stylesConstants.colors.secondary],
            ),
        }
    ));

    const animatedTextStyle = useAnimatedStyle(() => (
        {
            color: interpolateColor(
                progress.value,
                [0, 1],
                [stylesConstants.colors.gray200, stylesConstants.colors.gray900],
            ),
        }
    ));

    return (
        <TouchableWithoutFeedback onPress={props.onPress}>
            <Animated.View style={[styles.button, animatedButtonStyle]}>
                <Animated.Text style={[styles.buttonText, animatedTextStyle]}>
                    {props.option.label}
                </Animated.Text>
            </Animated.View>
        </TouchableWithoutFeedback>
    );
};

export default OptionButton;