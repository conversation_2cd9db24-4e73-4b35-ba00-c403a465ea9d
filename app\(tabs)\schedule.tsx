import React, {useCallback} from "react";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import ScheduleOptionButton from "../../components/schedule/schedule-option-button";
import CalendarBackgroundIcon from "../../components/icons/calendar-background-icon";
import CalendarIcon from "../../components/icons/calendar-icon";
import {StyleProp, Text, TextStyle, TouchableOpacity, View} from "react-native";
import styles from "../../styles/tabs/schedule.style";
import AnnounceIcon from "../../components/icons/announce-icon";
import AnnounceBackgroundIcon from "../../components/icons/announce-background-icon";
import {useRouter} from "expo-router";
import TicketIcon from "../../components/icons/ticket-icon";
import TicketBackgroundIcon from "../../components/icons/ticket-background-icon";
import PlusIcon from "../../components/icons/plus-icon";

const Schedule: React.FC = () => {
    const {t} = useTranslation();
    const router = useRouter();

    const scheduleSubtitle = useCallback(
        (style: StyleProp<TextStyle>) => (
            <View style={styles.scheduleTextContainer}>
                <Text style={style}>
                    {t("schedule.options.calendar.subtitle")}
                </Text>
                <Text
                    style={[style, styles.scheduleTextBold]}
                >{`0 ${t("schedule.options.calendar.event")}`}</Text>
                <Text style={style}>
                    {t("schedule.options.calendar.endSubtitle")}
                </Text>
            </View>
        ),
        []
    );

    const myTicketsSubtitle = useCallback(
        (style: StyleProp<TextStyle>) => (
            <View style={styles.scheduleTextContainer}>
                <Text style={style}>
                    {t("schedule.options.tickets.subtitle")}
                </Text>
                <Text
                    style={[style, styles.scheduleTextBold]}
                >{`0 ${t("schedule.options.tickets.event")}`}</Text>
                <Text style={style}>
                    {t("schedule.options.tickets.endSubtitle")}
                </Text>
            </View>
        ),
        []
    );

    const handleSchedulePress = useCallback(
        () => router.push("/(logged-stack)/calendar"),
        [router]
    );

    const handleEventListPress = useCallback(
        () => router.push("/(logged-stack)/event-list"),
        [router]
    );

    const handleTicketsPress = useCallback(
        () => router.push("/(logged-stack)/my-tickets"),
        [router]
    );

    const handleNewEventPress = useCallback(() => router.push("/(logged-stack)/create-event"), [router]);

    return (
        <ScreenWithHeader screenTitle={t("schedule.title")}>
            <View style={styles.pageContainer}>
                <View style={styles.container}>
                    <ScheduleOptionButton
                        bigIcon={<TicketBackgroundIcon />}
                        icon={<TicketIcon />}
                        iconColor="#40B98733"
                        title={t("schedule.options.tickets.title")}
                        onPress={handleTicketsPress}
                        subtitle={myTicketsSubtitle}
                    ></ScheduleOptionButton>
                    <ScheduleOptionButton
                        bigIcon={<CalendarBackgroundIcon />}
                        icon={<CalendarIcon replaceColor="#F2994A" />}
                        iconColor="rgba(242, 153, 74, 0.20)"
                        title={t("schedule.options.calendar.title")}
                        onPress={handleSchedulePress}
                        subtitle={scheduleSubtitle}
                    ></ScheduleOptionButton>
                    <ScheduleOptionButton
                        bigIcon={<AnnounceBackgroundIcon />}
                        icon={<AnnounceIcon />}
                        iconColor="rgba(64, 121, 185, 0.20)"
                        onPress={handleEventListPress}
                        title={t("schedule.options.eventList.title")}
                        subtitle={t("schedule.options.eventList.subtitle")}
                    ></ScheduleOptionButton>
                </View>
                <View style={[styles.createEventButtonContainer]}>
                    <TouchableOpacity style={styles.createEventButton} onPress={handleNewEventPress}>
                        <PlusIcon />
                        <Text style={styles.createEventButtonText}>
                            {t("schedule.options.createEventButton")}
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </ScreenWithHeader>
    );
};

export default Schedule;
