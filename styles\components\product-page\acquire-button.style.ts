import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    touchableHighlight: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 8,
        borderWidth: 1,
        borderColor: stylesConstants.colors.acquireBtnBorder,
        backgroundColor: stylesConstants.colors.brand.primary,
        width: "100%",
        height: 40
    },
    tagContainer: {
        position: "absolute",
        top: -55,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: "center",
        alignItems: "center"
    },
    contentContainer: {
        display: "flex",
        flexDirection: "row",
        gap: 6,
        justifyContent: "center",
        alignItems: "center"
    },
    labelText: {
        color: stylesConstants.colors.gray50,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20
    },
    priceText: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 700,
        lineHeight: 24
    }
});

export default styles;
