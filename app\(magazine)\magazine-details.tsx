import React, {useState, useEffect} from "react";
import {
  Text,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  Share
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useLocalSearchParams} from "expo-router";

interface MagazineArticle {
  id: string;
  title: string;
  content: string;
  author: string;
  publishDate: string;
  category: string;
  readTime: string;
  coverImage: string;
  tags: string[];
  isPremium: boolean;
  likes: number;
  isLiked: boolean;
}

const MagazineDetails: React.FC = () => {
  const {t} = useTranslation();
  const {id} = useLocalSearchParams();
  const [article, setArticle] = useState<MagazineArticle | null>(null);
  const [isLiked, setIsLiked] = useState(false);

  useEffect(() => {
    // Simulate fetching article data
    const mockArticle: MagazineArticle = {
      id: id as string,
      title: t("magazine.sampleTitle", "Tendências do Mercado 2024"),
      content: t(
        "magazine.sampleContent",
        `
O mercado global está passando por transformações significativas que moldarão o futuro dos negócios. Neste artigo, exploramos as principais tendências que definirão 2024.

## Transformação Digital Acelerada

A digitalização continua sendo uma força motriz fundamental. Empresas que abraçaram a tecnologia estão vendo resultados extraordinários em eficiência e alcance de mercado.

### Principais Pontos:
• Automação de processos
• Inteligência artificial aplicada
• Experiência do cliente digital
• Trabalho remoto e híbrido

## Sustentabilidade como Diferencial

A consciência ambiental não é mais apenas uma tendência, mas uma necessidade. Consumidores e investidores estão priorizando empresas com práticas sustentáveis.

## Economia Colaborativa

O modelo de negócios colaborativo continua crescendo, criando novas oportunidades de mercado e formas inovadoras de gerar valor.

## Conclusão

As empresas que se adaptarem rapidamente a essas tendências estarão melhor posicionadas para o sucesso em 2024 e além.
            `
      ),
      author: "Dr. João Silva",
      publishDate: "2024-01-15",
      category: t("magazine.business", "Negócios"),
      readTime: "15 min",
      coverImage:
        "https://via.placeholder.com/400x300/007AFF/FFFFFF?text=Artigo",
      tags: ["mercado", "tendências", "2024", "negócios"],
      isPremium: false,
      likes: 127,
      isLiked: false
    };

    setArticle(mockArticle);
    setIsLiked(mockArticle.isLiked);
  }, [id, t]);

  const handleLike = () => {
    setIsLiked(!isLiked);
    if (article) {
      setArticle({
        ...article,
        likes: isLiked ? article.likes - 1 : article.likes + 1,
        isLiked: !isLiked
      });
    }
  };

  const handleShare = async () => {
    if (!article) return;

    try {
      await Share.share({
        message: `${article.title}\n\n${t(
          "magazine.shareMessage",
          "Confira este artigo interessante no Club M!"
        )}\n\nClub M App`,
        title: article.title
      });
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "long",
      year: "numeric"
    });
  };

  if (!article) {
    return (
      <ScreenWithHeader
        screenTitle={t("magazine.loading", "Carregando...")}
        backButton
      >
        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
          <Text style={{color: "#fff", fontSize: 16}}>
            {t("magazine.loading", "Carregando artigo...")}
          </Text>
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader screenTitle={t("magazine.details", "Artigo")} backButton>
      <ScrollView style={{flex: 1}}>
        {/* Cover Image */}
        <View style={{height: 250, backgroundColor: "#333"}}>
          <Image
            source={{uri: article.coverImage}}
            style={{width: "100%", height: "100%"}}
            resizeMode="cover"
          />
          {article.isPremium && (
            <View
              style={{
                position: "absolute",
                top: 20,
                right: 20,
                backgroundColor: "#FFD700",
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 15
              }}
            >
              <Text style={{color: "#000", fontSize: 12, fontWeight: "bold"}}>
                {t("magazine.premium", "PREMIUM")}
              </Text>
            </View>
          )}
        </View>

        <View style={{padding: 20}}>
          {/* Article Header */}
          <View style={{marginBottom: 20}}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 10
              }}
            >
              <Text
                style={{
                  color: "#007AFF",
                  fontSize: 14,
                  fontWeight: "bold",
                  textTransform: "uppercase"
                }}
              >
                {article.category}
              </Text>
              <Text style={{color: "#666", fontSize: 14, marginLeft: 10}}>
                • {article.readTime}
              </Text>
            </View>

            <Text
              style={{
                color: "#fff",
                fontSize: 24,
                fontWeight: "bold",
                lineHeight: 32,
                marginBottom: 15
              }}
            >
              {article.title}
            </Text>

            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 15
              }}
            >
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t("magazine.by", "Por")} {article.author}
              </Text>
              <Text style={{color: "#666", fontSize: 14, marginLeft: 10}}>
                • {formatDate(article.publishDate)}
              </Text>
            </View>

            {/* Tags */}
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={{flexDirection: "row", gap: 8}}>
                {article.tags.map((tag, index) => (
                  <View
                    key={`tag-${tag.slice(0, 20)}-${index}`}
                    style={{
                      backgroundColor: "rgba(255,255,255,0.1)",
                      paddingHorizontal: 12,
                      paddingVertical: 6,
                      borderRadius: 15
                    }}
                  >
                    <Text style={{color: "#ccc", fontSize: 12}}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </ScrollView>
          </View>

          {/* Action Buttons */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              paddingVertical: 15,
              borderTopWidth: 1,
              borderBottomWidth: 1,
              borderColor: "rgba(255,255,255,0.1)",
              marginBottom: 20
            }}
          >
            <TouchableOpacity
              style={{flexDirection: "row", alignItems: "center"}}
              onPress={handleLike}
            >
              <Text style={{fontSize: 20, marginRight: 8}}>
                {isLiked ? "❤️" : "🤍"}
              </Text>
              <Text style={{color: "#fff", fontSize: 16}}>{article.likes}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{flexDirection: "row", alignItems: "center"}}
              onPress={handleShare}
            >
              <Text style={{fontSize: 20, marginRight: 8}}>📤</Text>
              <Text style={{color: "#fff", fontSize: 16}}>
                {t("magazine.share", "Compartilhar")}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Article Content */}
          <View style={{marginBottom: 30}}>
            <Text
              style={{
                color: "#fff",
                fontSize: 16,
                lineHeight: 24,
                textAlign: "justify"
              }}
            >
              {article.content}
            </Text>
          </View>

          {/* Related Articles Section */}
          <View
            style={{
              backgroundColor: "rgba(255,255,255,0.05)",
              padding: 20,
              borderRadius: 12,
              marginBottom: 20
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: 18,
                fontWeight: "bold",
                marginBottom: 15
              }}
            >
              {t("magazine.related", "Artigos Relacionados")}
            </Text>

            <Text style={{color: "#ccc", fontSize: 14, textAlign: "center"}}>
              {t(
                "magazine.relatedSoon",
                "Em breve mais artigos relacionados aparecerão aqui"
              )}
            </Text>
          </View>
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default MagazineDetails;
