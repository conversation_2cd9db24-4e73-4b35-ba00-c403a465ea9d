import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8
  },

  // Stats Card Styles
  statCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault,
    position: "relative"
  },
  
  statHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    gap: 8
  },
  
  statIcon: {
    fontSize: 20
  },
  
  statDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  
  statContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  
  statLeft: {
    flexDirection: "column"
  },
  
  statValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: "700",
    lineHeight: 32,
    marginBottom: 4
  },
  
  statLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  
  statAction: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4
  },
  
  statActionText: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  
  statActionArrow: {
    color: stylesConstants.colors.brand.primary,
    fontSize: 16,
    fontWeight: "600"
  },
  
  activeIndicator: {
    position: "absolute",
    top: 16,
    right: 16,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: stylesConstants.colors.green400
  }
});

export default styles;
