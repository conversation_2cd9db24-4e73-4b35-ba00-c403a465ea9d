import React, {useEffect, useState} from "react";
import {Text, View, Dimensions, TouchableWithoutFeedback} from "react-native";
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    Easing,
    interpolate,
    Extrapolation,
    runOnJS
} from "react-native-reanimated";
import styles from "../styles/components/bottom-modal.style";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {useBottomModal} from "../contexts/bottom-modal-context";


export type BottomModalType = React.FC & {
    Container: React.FC<React.PropsWithChildren>,
    FeaturedContainer: React.FC<React.PropsWithChildren>,
    ButtonsContainer: React.FC<React.PropsWithChildren>,
};

const BottomModal: BottomModalType = () => {
    const insets = useSafeAreaInsets();
    const modal = useBottomModal();
    const screenHeight = Dimensions.get("window").height;

    const [isVisible, setIsVisible] = useState(false);
    const animationProgress = useSharedValue(0);

    const hideModal = () => {
        setIsVisible(false);
    };

    useEffect(() => {
        if (modal.currentModal) {
            setIsVisible(true);
            animationProgress.value = withTiming(1, {
                duration: 400,
                easing: Easing.out(Easing.back(1.1)),
            });
        } else if (isVisible) {
            animationProgress.value = withTiming(0, {
                duration: 400,
                easing: Easing.out(Easing.cubic),
            }, () => {
                runOnJS(hideModal)();
            });
        }
    }, [modal.currentModal, isVisible]);

    const modalAnimatedStyle = useAnimatedStyle(() => {
        const translateY = interpolate(
            animationProgress.value,
            [0, 1],
            [screenHeight, 0],
            Extrapolation.CLAMP
        );

        return {
            transform: [{translateY}],
        };
    });

    const backdropAnimatedStyle = useAnimatedStyle(() => {
        const opacity = interpolate(
            animationProgress.value,
            [0, 1],
            [0, 0.6],
            Extrapolation.CLAMP
        );

        return {
            opacity,
        };
    });

    const handleBackdropPress = () => {
        modal.closeModal();
    };

    return (
        isVisible && (
            <>
                <Animated.View
                    style={[
                        styles.baseAnimationPosition,
                        backdropAnimatedStyle
                    ]}
                >
                    <TouchableWithoutFeedback onPress={handleBackdropPress}>
                        <View style={styles.backgroundTouch}/>
                    </TouchableWithoutFeedback>
                </Animated.View>

                <Animated.View style={[styles.container, {paddingBottom: insets.bottom}, modalAnimatedStyle]}>
                    <View style={styles.header}>
                        <View style={styles.notch}></View>
                        <Text style={styles.title}>{modal.currentModal?.title}</Text>
                    </View>
                    <View>
                        {modal.currentModal?.children}
                    </View>
                </Animated.View>
            </>
        )
    );
};

BottomModal.Container = (props) => {
    return (
        <View style={styles.bodyContainer}>
            {props.children}
        </View>
    );
};

BottomModal.FeaturedContainer = (props) => {
    return (
        <View style={styles.bodyFeaturedContainer}>
            {props.children}
        </View>
    );
};

BottomModal.ButtonsContainer = (props) => {
    return (
        <View style={styles.buttonsContainer}>
            {props.children}
        </View>
    );
};

export default BottomModal;