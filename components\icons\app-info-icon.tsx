import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface AppInfoIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const AppInfoIcon: React.FC<AppInfoIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={14} height={20} viewBox="0 0 14 20" fill="none" {...props}>
      <Path
        d="M9.49996 1.66699V2.83366C9.49996 3.30037 9.49996 3.53372 9.40913 3.71198C9.32924 3.86879 9.20175 3.99627 9.04495 4.07616C8.86669 4.16699 8.63334 4.16699 8.16663 4.16699H5.83329C5.36658 4.16699 5.13323 4.16699 4.95497 4.07616C4.79817 3.99627 4.67068 3.86879 4.59079 3.71198C4.49996 3.53372 4.49996 3.30037 4.49996 2.83366V1.66699M3.83329 18.3337H10.1666C11.1 18.3337 11.5668 18.3337 11.9233 18.152C12.2369 17.9922 12.4918 17.7372 12.6516 17.4236C12.8333 17.0671 12.8333 16.6004 12.8333 15.667V4.33366C12.8333 3.40024 12.8333 2.93353 12.6516 2.57701C12.4918 2.2634 12.2369 2.00844 11.9233 1.84865C11.5668 1.66699 11.1 1.66699 10.1666 1.66699H3.83329C2.89987 1.66699 2.43316 1.66699 2.07664 1.84865C1.76304 2.00844 1.50807 2.2634 1.34828 2.57701C1.16663 2.93353 1.16663 3.40024 1.16663 4.33366V15.667C1.16663 16.6004 1.16663 17.0671 1.34828 17.4236C1.50807 17.7372 1.76304 17.9922 2.07664 18.152C2.43316 18.3337 2.89987 18.3337 3.83329 18.3337Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default AppInfoIcon;
