import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flexDirection: "column",
        flexGrow: 1,
        justifyContent: "space-between",
        borderTopWidth: 1,
        borderTopColor: stylesConstants.colors.gray800
    },
    messageListContainer: {
        flexGrow: 1
    },
    selfMessageContainer: {
        display: "flex",
        flexDirection: "column",
        gap: 6
    },
    chatContent: {
        paddingVertical: 16,
        gap: 24,
        display: "flex",
        justifyContent: "flex-end",
        flexGrow: 1
    },
    section: {
        gap: 24
    },
    sectionHeader: {
        alignItems: "center",
        justifyContent: "center"
    },
    sectionTitle: {
        fontSize: 14,
        fontWeight: "600",
        lineHeight: 20,
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans
    },
    messagesContainer: {
        gap: 20
    },
    messageRow: {
        flexDirection: "row",
        alignItems: "flex-start",
        gap: 12
    },
    messageRowUser: {
        justifyContent: "flex-end"
    },
    avatarContainer: {
        width: 32,
        height: 32,
        borderRadius: 16,
        position: "relative"
    },
    avatar: {
        width: "100%",
        height: "100%",
        borderRadius: 16
    },
    avatarBorder: {
        position: "absolute",
        width: "100%",
        height: "100%",
        borderRadius: 16,
        borderWidth: 0.5,
        borderColor: stylesConstants.colors.gray900,
        opacity: 0.08
    },
    onlineIndicator: {
        position: "absolute",
        bottom: 0,
        right: 0,
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: stylesConstants.colors.brand.primary,
        borderWidth: 1.5,
        borderColor: stylesConstants.colors.fullWhite
    },
    messageContainer: {
        maxWidth: 268,
        paddingVertical: 10,
        paddingHorizontal: 14,
        borderRadius: 8
    },
    messageFromOther: {
        backgroundColor: stylesConstants.colors.gray800,
        borderTopLeftRadius: 0
    },
    messageFromUser: {
        backgroundColor: stylesConstants.colors.gray800,
        borderTopRightRadius: 0
    },
    messageText: {
        fontSize: 14,
        fontWeight: "400",
        lineHeight: 20,
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans
    },
    timeContainer: {
        justifyContent: "flex-end",
        alignItems: "flex-end",
        gap: 6
    },
    timeText: {
        fontSize: 10,
        fontWeight: "400",
        lineHeight: 18,
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        textAlign: "right"
    },
    inputContainer: {
        width: "100%",
        paddingHorizontal: 24,
        paddingVertical: 16,
        borderTopWidth: 1,
        height: 64,
        borderTopColor: stylesConstants.colors.gray800
    },
    inputRow: {
        display: "flex",
        flexDirection: "row",
        gap: 12,
        height: 44,
        width: "100%"
    },
    textInput: {
        flex: 1
    },
    sendButton: {
        width: 44,
        height: "100%",
        borderRadius: 8,
        backgroundColor: stylesConstants.colors.brand.primary,
        justifyContent: "center",
        alignItems: "center",
        borderWidth: 1,
        borderColor: stylesConstants.colors.brand.primary,
        shadowColor: stylesConstants.colors.gray900,
        shadowOffset: {width: 0, height: 1},
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 1
    }
});

export default styles;
