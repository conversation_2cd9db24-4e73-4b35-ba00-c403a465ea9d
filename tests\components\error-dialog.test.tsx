import React from "react";
import {render, fireEvent} from "@testing-library/react-native";

const mockCleanError = jest.fn();
const mockEmitError = jest.fn();
let mockCurrentError: any = null;

jest.mock("../../contexts/error-dialog-context", () => ({
    useErrorMessage: () => ({
        currentError: mockCurrentError,
        cleanError: mockCleanError,
        emitError: mockEmitError
    }),
    ErrorType: {
        Warning: "warning",
        Error: "error"
    }
}));

jest.mock("react-native-reanimated", () =>
    require("react-native-reanimated/mock")
);

jest.mock("expo-linear-gradient", () => ({
    LinearGradient: ({children}: any) => children
}));

jest.mock("../../components/icons/warning-icon", () => () => null);
jest.mock("../../components/icons/close-icon", () => () => null);
jest.mock("../../components/icons/error-icon", () => () => null);

jest.mock("../../styles/components/error-dialog.style", () => ({
    container: {},
    linear: {},
    iconsContainer: {},
    icon: {},
    closeButton: {},
    text: {},
    title: {}
}));

jest.mock("../../styles/styles-constants", () => ({
    colors: {
        error: {
            backgroundGradientStart: "#ff0000",
            backgroundGradientEnd: "#ff6666"
        }
    }
}));

import ErrorDialog from "../../components/error-dialog";

describe("ErrorDialog", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockCurrentError = null;
    });

    it("renders nothing when no error", () => {
        const {queryByText} = render(<ErrorDialog />);
        expect(queryByText("Error Title")).toBeNull();
        expect(queryByText("Warning Title")).toBeNull();
    });

    it("renders warning error dialog", () => {
        mockCurrentError = {
            errorType: "warning",
            title: "Warning Title",
            description: "Warning Description"
        };

        const {getByText} = render(<ErrorDialog />);
        expect(getByText("Warning Title")).toBeTruthy();
        expect(getByText("Warning Description")).toBeTruthy();
    });

    it("renders error dialog", () => {
        mockCurrentError = {
            errorType: "error",
            title: "Error Title",
            description: "Error Description"
        };

        const {getByText} = render(<ErrorDialog />);
        expect(getByText("Error Title")).toBeTruthy();
        expect(getByText("Error Description")).toBeTruthy();
    });

    it("calls cleanError when close button pressed", () => {
        mockCurrentError = {
            errorType: "error",
            title: "Error Title",
            description: "Error Description"
        };

        const {UNSAFE_getByType} = render(<ErrorDialog />);
        const touchableOpacity = UNSAFE_getByType(
            require("react-native").TouchableOpacity
        );
        fireEvent.press(touchableOpacity);
        expect(mockCleanError).toHaveBeenCalledTimes(1);
    });
});
