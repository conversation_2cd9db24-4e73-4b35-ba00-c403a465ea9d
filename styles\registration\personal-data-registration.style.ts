import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: stylesConstants.colors.primary,
    },
    contentContainer: {
        flex: 1,
        paddingHorizontal: 24,
        paddingVertical: 40,
    },
    headerContainer: {
        marginBottom: 32,
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 24,
        fontWeight: 700,
        lineHeight: 32,
        marginBottom: 8,
    },
    subtitle: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 400,
        lineHeight: 24,
    },
    progressContainer: {
        marginBottom: 32,
    },
    progressBar: {
        height: 4,
        backgroundColor: stylesConstants.colors.gray300,
        borderRadius: 2,
        marginBottom: 8,
    },
    progressFill: {
        height: "100%",
        backgroundColor: stylesConstants.colors.brand.primary,
        borderRadius: 2,
    },
    progressText: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18,
        textAlign: "center",
    },
    formContainer: {
        flex: 1,
        gap: 24,
    },
    inputGroup: {
        gap: 8,
    },
    label: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 600,
        lineHeight: 20,
    },
    requiredLabel: {
        color: stylesConstants.colors.error600,
    },
    inputRow: {
        flexDirection: "row",
        gap: 12,
    },
    inputHalf: {
        flex: 1,
    },
    datePickerContainer: {
        backgroundColor: stylesConstants.colors.inputBackground,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
        paddingHorizontal: 12,
        paddingVertical: 12,
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    datePickerText: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
    },
    datePickerPlaceholder: {
        color: stylesConstants.colors.textPrimary,
    },
    genderContainer: {
        gap: 12,
    },
    genderOptions: {
        flexDirection: "row",
        gap: 12,
    },
    genderOption: {
        flex: 1,
        backgroundColor: stylesConstants.colors.inputBackground,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
        paddingVertical: 12,
        alignItems: "center",
    },
    genderOptionSelected: {
        borderColor: stylesConstants.colors.brand.primary,
        backgroundColor: stylesConstants.colors.highlightBackground,
    },
    genderOptionText: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
    },
    genderOptionTextSelected: {
        color: stylesConstants.colors.fullWhite,
        fontWeight: 600,
    },
    phoneContainer: {
        flexDirection: "row",
        gap: 8,
    },
    countryCode: {
        width: 80,
    },
    phoneInput: {
        flex: 1,
    },
    termsContainer: {
        flexDirection: "row",
        alignItems: "flex-start",
        gap: 12,
        marginTop: 16,
    },
    checkbox: {
        width: 20,
        height: 20,
        borderRadius: 4,
        borderWidth: 2,
        borderColor: stylesConstants.colors.borderDefault,
        alignItems: "center",
        justifyContent: "center",
        marginTop: 2,
    },
    checkboxChecked: {
        backgroundColor: stylesConstants.colors.brand.primary,
        borderColor: stylesConstants.colors.brand.primary,
    },
    termsText: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18,
        flex: 1,
    },
    termsLink: {
        color: stylesConstants.colors.brand.primary,
        textDecorationLine: "underline",
    },
    buttonContainer: {
        gap: 16,
        paddingTop: 16,
    },
});

export default styles;
