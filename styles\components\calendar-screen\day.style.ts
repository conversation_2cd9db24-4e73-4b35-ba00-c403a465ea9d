import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    mainContainer: {
        paddingBottom: 10
    },
    container: {
        display: "flex",
        paddingTop: 8,
        paddingBottom: 12,
        paddingHorizontal: 12,
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        borderColor: stylesConstants.colors.gray25,
        borderWidth: 1,
        borderStyle: "solid",
        borderRadius: 4
    },
    text: {
        color: stylesConstants.colors.fullWhite,
        textAlign: "center",
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontStyle: "normal",
        fontWeight: 400,
        lineHeight: 18
    },
    dayNumber: {
        fontSize: 14,
        fontWeight: 600,
        lineHeight: 20
    },
    selectedDay: {
        backgroundColor: stylesConstants.colors.brand.brand400,
        borderColor: stylesConstants.colors.brand.brand400,
    },
    dotList: {
        position: "absolute",
        display: "flex",
        flexDirection: "row",
        bottom: 5,
        justifyContent: "center",
        width: "100%",
        left: -2,
        right: 0
    },
    dot: {
        width: 10,
        aspectRatio: 1,
        borderRadius: "50%",
        marginRight: -4,
    }
});

export default styles;