import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface SupportHelpIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const SupportHelpIcon: React.FC<SupportHelpIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
      <Path
        d="M7.05367 7.05406L4.1074 4.10779M4.1074 15.8929L7.05369 12.9466M12.9462 12.9466L15.8925 15.8929M15.8925 4.10775L12.9462 7.05404M18.3333 10.0003C18.3333 14.6027 14.6023 18.3337 9.99996 18.3337C5.39759 18.3337 1.66663 14.6027 1.66663 10.0003C1.66663 5.39795 5.39759 1.66699 9.99996 1.66699C14.6023 1.66699 18.3333 5.39795 18.3333 10.0003ZM14.1666 10.0003C14.1666 12.3015 12.3011 14.167 9.99996 14.167C7.69877 14.167 5.83329 12.3015 5.83329 10.0003C5.83329 7.69914 7.69877 5.83366 9.99996 5.83366C12.3011 5.83366 14.1666 7.69914 14.1666 10.0003Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default SupportHelpIcon;
