import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface ManagePermissionsIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const ManagePermissionsIcon: React.FC<ManagePermissionsIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={16} height={19} viewBox="0 0 16 19" fill="none" {...props}>
      <Path
        d="M5.50004 9.58363L7.16671 11.2503L10.9167 7.5003M14.6667 10.0003C14.6667 14.0907 10.2051 17.0656 8.5817 18.0127C8.39721 18.1203 8.30496 18.1741 8.17477 18.2021C8.07374 18.2237 7.92634 18.2237 7.82531 18.2021C7.69512 18.1741 7.60287 18.1203 7.41838 18.0127C5.79501 17.0656 1.33337 14.0907 1.33337 10.0003V6.01497C1.33337 5.34871 1.33337 5.01558 1.44234 4.72922C1.5386 4.47625 1.69503 4.25053 1.89809 4.07157C2.12795 3.869 2.43987 3.75203 3.06371 3.51809L7.53188 1.84253C7.70512 1.77756 7.79175 1.74508 7.88086 1.7322C7.9599 1.72078 8.04018 1.72078 8.11922 1.7322C8.20834 1.74508 8.29496 1.77756 8.4682 1.84253L12.9364 3.51809C13.5602 3.75203 13.8721 3.869 14.102 4.07157C14.3051 4.25053 14.4615 4.47625 14.5577 4.72922C14.6667 5.01558 14.6667 5.34871 14.6667 6.01497V10.0003Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default ManagePermissionsIcon;
