import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image
} from "react-native";
import ScreenWithHeader from "../../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import SearchIcon from "../../../components/icons/search-icon";
import styles from "@/styles/partners/index.style";

interface Partner {
  id: string;
  name: string;
  logo: any;
  category: string;
}

const PartnersScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("Todos");

  const categories = [
    "Todos",
    "Descontos",
    "Ofertas",
    "Serviços VIP",
    "Assinatura",
    "Saúde"
  ];

  const partners: Partner[] = [
    {
      id: "1",
      name: "7Club",
      logo: null, // Will use text fallback
      category: "Descontos"
    },
    {
      id: "2",
      name: "She<PERSON>",
      logo: null,
      category: "Ofertas"
    },
    {
      id: "3",
      name: "Grupo Belga",
      logo: null,
      category: "Serviços VIP"
    },
    {
      id: "4",
      name: "Altimas",
      logo: null,
      category: "Assinatura"
    },
    {
      id: "5",
      name: "Aquarius",
      logo: null,
      category: "Saúde"
    },
    {
      id: "6",
      name: "MCMII",
      logo: null,
      category: "Ofertas"
    },
    {
      id: "7",
      name: "Anamnese",
      logo: null,
      category: "Saúde"
    },
    {
      id: "8",
      name: "Alfar",
      logo: null,
      category: "Descontos"
    },
    {
      id: "9",
      name: "ATAAA",
      logo: null,
      category: "Serviços VIP"
    },
    {
      id: "10",
      name: "TG",
      logo: null,
      category: "Assinatura"
    }
  ];

  const filteredPartners = partners.filter((partner) => {
    const matchesSearch = partner.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "Todos" || partner.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handlePartnerPress = (partnerId: string) => {
    router.push(`/(main)/partners/partner-benefits?id=${partnerId}`);
  };

  const renderPartnerCard = (partner: Partner) => (
    <TouchableOpacity
      key={partner.id}
      style={styles.partnerCard}
      onPress={() => handlePartnerPress(partner.id)}
    >
      <View style={styles.partnerLogoContainer}>
        {partner.logo ? (
          <Image
            source={partner.logo}
            style={styles.partnerLogo}
            resizeMode="contain"
          />
        ) : (
          <Text style={styles.partnerName}>{partner.name}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <ScreenWithHeader
      screenTitle={t("partners.title", "Parceiros Club M")}
      backButton
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <SearchIcon width={20} height={20} />
            <TextInput
              style={styles.searchInput}
              value={searchTerm}
              onChangeText={setSearchTerm}
              placeholder={t(
                "partners.searchPlaceholder",
                "Buscar parceiro, benefício, etc..."
              )}
              placeholderTextColor="#8B8B8B"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
        </View>

        {/* Categories */}
        <View style={styles.categoriesContainer}>
          <Text style={styles.categoriesTitle}>
            {t("partners.categories", "Categorias")}
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesScrollView}
            contentContainerStyle={styles.categoriesContent}
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryButton,
                  selectedCategory === category && styles.categoryButtonActive
                ]}
                onPress={() => setSelectedCategory(category)}
              >
                <Text
                  style={[
                    styles.categoryButtonText,
                    selectedCategory === category &&
                      styles.categoryButtonTextActive
                  ]}
                >
                  {t(`partners.category.${category.toLowerCase()}`, category)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Partners Grid */}
        <View style={styles.partnersGrid}>
          {filteredPartners.map(renderPartnerCard)}
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PartnersScreen;
