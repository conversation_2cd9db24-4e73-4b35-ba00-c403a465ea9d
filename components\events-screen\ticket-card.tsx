import React from "react";
import {useTranslation} from "react-i18next";
import {Text, TouchableOpacity, View} from "react-native";
import styles from "../../styles/components/events-screen/ticket-card.style";
import SmallTicket from "../svgs-forms/small-ticket";

export interface TicketCardProps {
    title: string;
    organizer: string;
    date: string;
    code: string;
}

const TicketCard: React.FC<TicketCardProps> = (props) => {
    const {t} = useTranslation();

    return (
        <SmallTicket>
            <View style={styles.ticketContainer}>
                <View style={styles.ticketContent}>
                    <Text style={styles.ticketTitle}>{props.title}</Text>
                    <View style={styles.organizerContainer}>
                        <Text style={styles.organizerLabel}>
                            {t("myTickets.organizedBy")}
                        </Text>
                        <Text style={styles.organizerName}>
                            {props.organizer}
                        </Text>
                    </View>
                </View>
                <View style={styles.ticketFooter}>
                    <View style={styles.ticketInfo}>
                        <Text style={styles.infoLabel}>
                            {t("myTickets.eventDate")}
                        </Text>
                        <Text style={styles.infoValue}>{props.date}</Text>
                    </View>
                    <View style={styles.ticketInfo}>
                        <Text style={styles.infoLabel}>
                            {t("myTickets.ticketCode")}
                        </Text>
                        <Text style={styles.infoValue}>{props.code}</Text>
                    </View>
                </View>
                <TouchableOpacity style={styles.detailsButton}>
                    <Text style={styles.detailsButtonText}>
                        {t("myTickets.viewDetails")}
                    </Text>
                </TouchableOpacity>
            </View>
        </SmallTicket>
    );
};

export default TicketCard;
