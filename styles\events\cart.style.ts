import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    marginBottom: 8
  },
  itemCount: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24
  },
  emptyCartContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
    paddingHorizontal: 24
  },
  emptyCartIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: stylesConstants.colors.highlightBackground,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24
  },
  emptyCartTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: 600,
    lineHeight: 28,
    textAlign: "center",
    marginBottom: 12
  },
  emptyCartText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 32
  },
  cartItemsList: {
    gap: 16,
    marginBottom: 24
  },
  cartItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  itemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12
  },
  itemTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    flex: 1,
    marginRight: 12
  },
  itemPrice: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24
  },
  itemDetails: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginBottom: 12
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: stylesConstants.colors.highlightBackground,
    alignItems: "center",
    justifyContent: "center"
  },
  quantityText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    minWidth: 24,
    textAlign: "center"
  },
  summaryContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8
  },
  summaryLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  summaryValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    paddingTop: 12,
    marginTop: 12
  },
  totalValue: {
    color: stylesConstants.colors.brand.primary,
    fontSize: 18,
    fontWeight: 700
  },
  buttonContainer: {
    gap: 16
  },
  itemIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: stylesConstants.colors.highlightBackground,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12
  },
  itemContent: {
    flex: 1
  },
  removeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(217, 45, 32, 0.1)",
    alignItems: "center",
    justifyContent: "center"
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    marginTop: 12
  },
  quantityButtonContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  }
});

export default styles;
