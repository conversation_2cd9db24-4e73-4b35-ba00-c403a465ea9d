import React, {forwardRef, useCallback} from "react";
import {NativeSyntheticEvent, TextInput, TextInputKeyPressEventData, View} from "react-native";
import styles from "../../styles/components/recovery-password/mega-digit.style";
import stylesConstants from "../../styles/styles-constants";

export interface MegaDigitProps {
    value: string;
    onChange: (value: string) => void;
    onBackspace?: () => void;
}

const MegaDigit: React.ExoticComponent<MegaDigitProps & React.RefAttributes<TextInput>>
    = forwardRef((props, ref) => {
    const onChange = useCallback((text: string) => {
        if (/^\d*$/.exec(text))
            props.onChange(text);
    }, [props.onChange]);

    const onKeyPress = useCallback((e: NativeSyntheticEvent<TextInputKeyPressEventData>) => {
        if (e.nativeEvent.key === "Backspace") {
            props.onBackspace?.();
        }
    }, [props.onBackspace]);

    return (
        <View style={[styles.container, props.value && styles.onInputFillBorder]}>
            <TextInput placeholder="0"
                       ref={ref}
                       onChangeText={onChange}
                       value={props.value}
                       onKeyPress={onKeyPress}
                       placeholderTextColor={stylesConstants.colors.gray300}
                       style={styles.input}
                       keyboardType="numeric"
                       returnKeyType="next"
                       submitBehavior="submit"
                       maxLength={1}/>
        </View>
    );
});

export default MegaDigit;