// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ChevronRight component renders correctly and matches snapshot 1`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight={20}
  bbWidth={20}
  fill="none"
  focusable={false}
  height={20}
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 20,
        "width": 20,
      },
    ]
  }
  testID="icon"
  vbHeight={20}
  vbWidth={20}
  width={20}
>
  <RNSVGGroup
    fill={null}
    propList={
      [
        "fill",
      ]
    }
  >
    <RNSVGPath
      d="M7.5 15l5-5-5-5"
      fill={
        {
          "payload": 4278190080,
          "type": 0,
        }
      }
      propList={
        [
          "stroke",
          "strokeWidth",
          "strokeLinecap",
          "strokeLinejoin",
        ]
      }
      stroke={
        {
          "payload": 4294967295,
          "type": 0,
        }
      }
      strokeLinecap={1}
      strokeLinejoin={1}
      strokeWidth={2}
    />
  </RNSVGGroup>
</RNSVGSvgView>
`;
