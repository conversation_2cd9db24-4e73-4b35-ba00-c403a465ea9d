import React from "react";
import {Text, View} from "react-native";
import Screen from "../../components/screen";
import BackgroundLogoTexture from "../../components/logos/background-logo-texture";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import CheckCircleIcon from "../../components/icons/check-circle-icon";

const RegistrationConfirmation: React.FC = () => {
    const {t} = useTranslation();

    return (
        <>
            <BackgroundLogoTexture />
            <Screen>
                <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20}}>
                    <View style={{alignItems: 'center', marginBottom: 40}}>
                        <CheckCircleIcon width={80} height={80} />
                    </View>
                    
                    <Text style={{fontSize: 28, fontWeight: 'bold', color: '#fff', textAlign: 'center', marginBottom: 20}}>
                        {t("registrationConfirmation.title", "Cadastro Realizado!")}
                    </Text>
                    
                    <Text style={{fontSize: 16, color: '#fff', textAlign: 'center', marginBottom: 30, lineHeight: 24}}>
                        {t("registrationConfirmation.description", "Parabéns! Seu cadastro foi realizado com sucesso. Agora você faz parte do Club M!")}
                    </Text>
                    
                    <View style={{backgroundColor: 'rgba(255,255,255,0.1)', padding: 20, borderRadius: 12, marginBottom: 40, width: '100%'}}>
                        <Text style={{color: '#fff', fontSize: 16, textAlign: 'center', marginBottom: 10}}>
                            {t("registrationConfirmation.nextSteps", "Próximos passos:")}
                        </Text>
                        <Text style={{color: '#ccc', fontSize: 14, textAlign: 'center', lineHeight: 20}}>
                            {t("registrationConfirmation.nextStepsDesc", "• Verifique seu e-mail para confirmar sua conta\n• Complete seu perfil para ter acesso completo\n• Explore os benefícios exclusivos do Club M")}
                        </Text>
                    </View>
                    
                    <FullSizeButton
                        text={t("registrationConfirmation.continue", "Continuar")}
                        onPress={() => {}}
                    />
                </View>
            </Screen>
        </>
    );
};

export default RegistrationConfirmation;
