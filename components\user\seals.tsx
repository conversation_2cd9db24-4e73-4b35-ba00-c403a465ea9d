import React, {useMemo} from "react";
import {useTranslation} from "react-i18next";
import {Text, View} from "react-native";
import styles from "../../styles/components/user/seals.style";
import BigSeal from "../seals/big-seal";

export interface Seal {
    title: string;
}

export interface SealsProps {
    seals: Seal[];
}

const Seals: React.FC<SealsProps> = (props) => {
    const {t} = useTranslation();

    const seals = useMemo(() => {
        return props.seals.map((seal, index) => (
            <View key={index + 1}>
                <BigSeal source={require("../../assets/seals/Diamond.png")} />
                <Text style={styles.sealTitle}>{seal.title}</Text>
            </View>
        ));
    }, [props.seals]);

    return (
        <View>
            <View style={styles.titleContainer}>
                <Text style={styles.title}>{t("user.seals")}</Text>
                <Text style={styles.seeMore}>{t("user.seeMore")}</Text>
            </View>
            <View style={styles.sealsContainer}>{seals}</View>
        </View>
    );
};

export default Seals;
