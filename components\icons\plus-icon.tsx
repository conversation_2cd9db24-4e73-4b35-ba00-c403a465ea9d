import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface PlusIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const PlusIcon: React.FC<PlusIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M10 4.167v11.667M4.167 10h11.666"
      />
    </Svg>
  );
};

export default PlusIcon;
