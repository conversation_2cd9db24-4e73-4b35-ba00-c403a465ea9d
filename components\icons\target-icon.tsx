import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const TargetIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={21}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M13.417 6.667v-2.5l2.5-2.5.833 1.667 1.667.833-2.5 2.5h-2.5Zm0 0L10.083 10m8.334 0a8.333 8.333 0 1 1-8.334-8.333M14.25 10a4.167 4.167 0 1 1-4.167-4.166"
            />
        </Svg>
    );
};

export default TargetIcon;