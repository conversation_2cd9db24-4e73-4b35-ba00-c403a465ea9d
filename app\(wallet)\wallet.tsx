import React, {useState, useRef} from "react";
import {Text, View, TouchableOpacity, NativeSyntheticEvent} from "react-native";
import {Image} from "expo-image";
import PagerView, {
  PagerViewOnPageScrollEventData
} from "react-native-pager-view";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import PlusIcon from "../../components/icons/plus-icon";
import EyeIcon from "../../components/icons/eye-icon";
import DisableEyeIcon from "../../components/icons/disable-eye-icon";
import EditIcon from "../../components/icons/edit-icon";
import styles from "@/styles/wallet/wallet.style";

interface MemberCard {
  id: string;
  name: string;
  photo: string;
  membershipType: string;
  cardNumber: string;
  federationUnit: string;
  associationUnit: string;
  activeSince: string;
  isDefault: boolean;
}

const Wallet: React.FC = () => {
  const {t} = useTranslation();
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [showCardInfo, setShowCardInfo] = useState(false);
  const pagerRef = useRef<PagerView>(null);

  const [memberCards] = useState<MemberCard[]>([
    {
      id: "1",
      name: "Maria Aparecida dos Santos",
      photo:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      membershipType: "Membro VIP",
      cardNumber: "1234",
      federationUnit: "Balneário Camboriú - SC",
      associationUnit: "Unidade Osvaldo Reis",
      activeSince: "Setembro de 2025",
      isDefault: true
    },
    {
      id: "2",
      name: "João Silva Santos",
      photo:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      membershipType: "Membro Premium",
      cardNumber: "5678",
      federationUnit: "São Paulo - SP",
      associationUnit: "Unidade Central",
      activeSince: "Janeiro de 2024",
      isDefault: false
    }
  ]);

  const currentCard = memberCards[currentCardIndex];

  const handlePageScroll = (
    event: NativeSyntheticEvent<PagerViewOnPageScrollEventData>
  ) => {
    const {position} = event.nativeEvent;
    setCurrentCardIndex(position);
  };

  return (
    <ScreenWithHeader
      screenTitle={t("wallet.title", "Sua carteira")}
      backButton
      rightHeaderChild={
        <TouchableOpacity style={styles.addCardButton}>
          <PlusIcon width={16} height={16} />
          <Text style={styles.addCardButtonText}>
            {t("wallet.addCard", "Ad. cartão")}
          </Text>
        </TouchableOpacity>
      }
    >
      <View style={styles.container}>
        {/* Card Carousel */}
        <View style={styles.carouselContainer}>
          <View style={styles.carouselHeader}>
            <Text style={styles.carouselTitle}>
              {t("wallet.memberCard", "Cartão de associado")}
            </Text>
            <Text style={styles.carouselSubtitle}>
              {t("wallet.yourCards", "Seus cartões")}
            </Text>
          </View>

          <PagerView
            ref={pagerRef}
            style={[styles.carousel, {height: 200}]}
            initialPage={0}
            onPageScroll={handlePageScroll}
          >
            {memberCards.map((card) => (
              <View key={card.id} style={styles.memberCard}>
                <View style={styles.cardHeader}>
                  <Image
                    source={{uri: card.photo}}
                    style={styles.memberPhoto}
                  />
                  <View style={styles.cardBrandContainer}>
                    <Text style={styles.cardBrand}>VISA</Text>
                  </View>
                </View>

                <View style={styles.memberInfo}>
                  <Text style={styles.memberName}>{card.name}</Text>
                  <Text style={styles.membershipType}>
                    {card.membershipType}
                  </Text>
                </View>

                <View style={styles.cardNumberContainer}>
                  <Text style={styles.cardNumber}>{card.cardNumber}</Text>
                  <Text style={styles.cardNumberLabel}>
                    (Principais créditos)
                  </Text>
                </View>
              </View>
            ))}
          </PagerView>

          {/* Pagination Dots */}
          <View style={styles.paginationContainer}>
            {memberCards.map((card, index) => (
              <View
                key={`pagination-${card.id}`}
                style={[
                  styles.paginationDot,
                  index === currentCardIndex && styles.paginationDotActive
                ]}
              />
            ))}
          </View>
        </View>

        {/* Card Information Section */}
        <View style={styles.cardInfoContainer}>
          <View style={styles.cardInfoHeader}>
            <Text style={styles.cardInfoTitle}>
              {t("wallet.cardInfo", "Informações do cartão")}
            </Text>
            <TouchableOpacity
              style={styles.toggleButton}
              onPress={() => setShowCardInfo(!showCardInfo)}
            >
              {showCardInfo ? (
                <DisableEyeIcon width={20} height={20} />
              ) : (
                <EyeIcon width={20} height={20} />
              )}
              <Text style={styles.toggleButtonText}>
                {showCardInfo
                  ? t("wallet.hide", "Esconder")
                  : t("wallet.show", "Mostrar")}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.cardInfoContent}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>
                {t("wallet.memberName", "Nome do associado")}
              </Text>
              <Text style={styles.infoValue}>
                {showCardInfo ? currentCard.name : "••••••••••••••••••••••"}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>
                {t("wallet.federationUnit", "Unidade Federativa")}
              </Text>
              <Text style={styles.infoValue}>
                {showCardInfo
                  ? currentCard.federationUnit
                  : "••••••••••••••••••••••"}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>
                {t("wallet.associationUnit", "Unidade de Associação")}
              </Text>
              <Text style={styles.infoValue}>
                {showCardInfo
                  ? currentCard.associationUnit
                  : "••••••••••••••••••••••"}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>
                {t("wallet.activeSince", "Membro ativo desde")}
              </Text>
              <Text style={styles.infoValue}>
                {showCardInfo ? currentCard.activeSince : "••••••••"}
              </Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity style={styles.editButton}>
              <EditIcon width={16} height={16} />
              <Text style={styles.editButtonText}>
                {t("wallet.editCard", "Editar cartão")}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.deleteButton}>
              <Text style={styles.deleteButtonText}>
                {t("wallet.deleteCard", "Excluir cartão")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ScreenWithHeader>
  );
};

export default Wallet;
