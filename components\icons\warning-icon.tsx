import React from "react";
import Svg, {<PERSON><PERSON><PERSON><PERSON>, Defs, G, <PERSON>, Rect, SvgProps} from "react-native-svg";

const WarningIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={38}
            height={38}
            fill="none"
            {...props}
        >
            <Rect
                width={26}
                height={26}
                x={6}
                y={6}
                stroke="#F80"
                strokeWidth={2}
                opacity={0.3}
                rx={13}
            />
            <Rect
                width={36}
                height={36}
                x={1}
                y={1}
                stroke="#F80"
                strokeWidth={2}
                opacity={0.1}
                rx={18}
            />
            <G clipPath="url(#a)">
                <Path
                    stroke="#F80"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.667}
                    d="M19 15.667V19m0 3.334h.008M27.333 19a8.333 8.333 0 1 1-16.666 0 8.333 8.333 0 0 1 16.666 0Z"
                />
            </G>
            <Defs>
                <ClipPath id="a">
                    <Path fill="#fff" d="M9 9h20v20H9z" />
                </ClipPath>
            </Defs>
        </Svg>
    );
};

export default WarningIcon;