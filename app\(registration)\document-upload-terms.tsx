import React, {useState} from "react";
import {Text, View, TouchableOpacity, ScrollView} from "react-native";
import Screen from "../../components/screen";
import BackgroundLogoTexture from "../../components/logos/background-logo-texture";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import CheckIcon from "../../components/icons/check-icon";

const DocumentUploadTerms: React.FC = () => {
  const {t} = useTranslation();
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [acceptedPrivacy, setAcceptedPrivacy] = useState(false);

  const handleDocumentUpload = (type: string) => {
    // Placeholder for document upload functionality
    console.log(`Upload ${type} document`);
  };

  const handleNext = () => {
    // Placeholder for next step functionality
    console.log("Proceeding to next step");
  };

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <ScrollView style={{flex: 1, padding: 20}}>
          <BackButton />
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#fff",
              textAlign: "center",
              marginBottom: 20
            }}
          >
            {t("documentUploadTerms.title", "Documentos e Termos")}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "#fff",
              textAlign: "center",
              marginBottom: 30
            }}
          >
            {t(
              "documentUploadTerms.description",
              "Envie seus documentos e aceite os termos"
            )}
          </Text>

          <View style={{marginBottom: 30}}>
            <Text
              style={{
                fontSize: 18,
                fontWeight: "bold",
                color: "#fff",
                marginBottom: 15
              }}
            >
              {t(
                "documentUploadTerms.documentsTitle",
                "Documentos Necessários"
              )}
            </Text>

            <TouchableOpacity
              style={{
                backgroundColor: "rgba(255,255,255,0.1)",
                padding: 15,
                borderRadius: 8,
                marginBottom: 10
              }}
              onPress={() => handleDocumentUpload("identity")}
            >
              <Text style={{color: "#fff", fontSize: 16}}>
                {t("documentUploadTerms.identity", "📄 RG ou CNH")}
              </Text>
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t(
                  "documentUploadTerms.identityDesc",
                  "Documento de identidade com foto"
                )}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                backgroundColor: "rgba(255,255,255,0.1)",
                padding: 15,
                borderRadius: 8,
                marginBottom: 10
              }}
              onPress={() => handleDocumentUpload("cpf")}
            >
              <Text style={{color: "#fff", fontSize: 16}}>
                {t("documentUploadTerms.cpf", "📄 CPF")}
              </Text>
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t("documentUploadTerms.cpfDesc", "Documento do CPF")}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                backgroundColor: "rgba(255,255,255,0.1)",
                padding: 15,
                borderRadius: 8,
                marginBottom: 20
              }}
              onPress={() => handleDocumentUpload("proof")}
            >
              <Text style={{color: "#fff", fontSize: 16}}>
                {t("documentUploadTerms.proof", "📄 Comprovante de Residência")}
              </Text>
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t(
                  "documentUploadTerms.proofDesc",
                  "Conta de luz, água ou telefone"
                )}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={{marginBottom: 30}}>
            <Text
              style={{
                fontSize: 18,
                fontWeight: "bold",
                color: "#fff",
                marginBottom: 15
              }}
            >
              {t("documentUploadTerms.termsTitle", "Termos e Condições")}
            </Text>

            <TouchableOpacity
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 15
              }}
              onPress={() => setAcceptedTerms(!acceptedTerms)}
            >
              <View
                style={{
                  width: 20,
                  height: 20,
                  borderWidth: 2,
                  borderColor: "#fff",
                  marginRight: 10,
                  backgroundColor: acceptedTerms ? "#fff" : "transparent",
                  justifyContent: "center",
                  alignItems: "center"
                }}
              >
                {acceptedTerms && <CheckIcon />}
              </View>
              <Text style={{color: "#fff", flex: 1}}>
                {t(
                  "documentUploadTerms.acceptTerms",
                  "Aceito os Termos de Uso"
                )}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 20
              }}
              onPress={() => setAcceptedPrivacy(!acceptedPrivacy)}
            >
              <View
                style={{
                  width: 20,
                  height: 20,
                  borderWidth: 2,
                  borderColor: "#fff",
                  marginRight: 10,
                  backgroundColor: acceptedPrivacy ? "#fff" : "transparent",
                  justifyContent: "center",
                  alignItems: "center"
                }}
              >
                {acceptedPrivacy && <CheckIcon />}
              </View>
              <Text style={{color: "#fff", flex: 1}}>
                {t(
                  "documentUploadTerms.acceptPrivacy",
                  "Aceito a Política de Privacidade"
                )}
              </Text>
            </TouchableOpacity>
          </View>

          <FullSizeButton
            text={t("documentUploadTerms.next", "Próximo")}
            onPress={
              !acceptedTerms || !acceptedPrivacy ? undefined : handleNext
            }
          />
        </ScrollView>
      </Screen>
    </>
  );
};

export default DocumentUploadTerms;
