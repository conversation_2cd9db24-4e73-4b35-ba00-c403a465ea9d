import React, {useCallback, useMemo, useState} from "react";
import {Text, TouchableOpacity, View, Switch, ScrollView} from "react-native";
import <PERSON>WithHeader from "../../components/screen-with-header";
import InputField from "../../components/input-field";
import Button from "../../components/button";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import styles from "../../styles/logged-stack/create-event.style";
import stylesConstants from "../../styles/styles-constants";
import PlusIcon from "../../components/icons/plus-icon";
import LockIcon from "../../components/icons/lock-icon";
import Type2Icon from "../../components/icons/type2-icon";
import CalendarTimeIcon from "../../components/icons/calendar-time-icon";
import CalendarHeartIcon from "../../components/icons/calendar-heart-icon";
import UsersPlusIcon from "../../components/icons/users-plus-icon";
import {useSafeAreaInsets} from "react-native-safe-area-context";

const CreateEvent: React.FC = () => {
    const {t} = useTranslation();
    const router = useRouter();
    const insets = useSafeAreaInsets();

    const [eventTitle, setEventTitle] = useState<string>("");
    const [eventDescription, setEventDescription] = useState<string>("");
    const [eventDateTime] = useState<string>("");
    const [eventType] = useState<string>("");
    const [inviteMembers] = useState<string>("");
    const [isPublic, setIsPublic] = useState<boolean>(true);

    const handleEventTitleChange = useCallback((value: string) => {
        setEventTitle(value);
    }, []);

    const handleEventDescriptionChange = useCallback((value: string) => {
        setEventDescription(value);
    }, []);

    const handleDateTimePress = useCallback(() => {
    }, []);

    const handleEventTypePress = useCallback(() => {
    }, []);

    const handleInviteMembersPress = useCallback(() => {
    }, []);

    const handleToggleVisibility = useCallback((value: boolean) => {
        setIsPublic(value);
    }, []);

    const handleAdditionalInfoPress = useCallback(() => {
    }, []);

    const handlePublishEvent = useCallback(() => {
        router.back();
    }, [router]);

    const handleCancel = useCallback(() => {
        router.back();
    }, [router]);

    const titleIcon = useCallback(() => (
        <Type2Icon/>
    ), []);

    const dateTimeIcon = useCallback(() => (
        <CalendarTimeIcon/>
    ), []);

    const eventTypeIcon = useCallback(() => (
        <CalendarHeartIcon/>
    ), []);

    const inviteMembersIcon = useCallback(() => (
        <UsersPlusIcon/>
    ), []);

    const rightHeaderChild = useMemo(() => (
        <View style={styles.rightHeaderContainer}>
            <Text style={styles.privateText}>
                {t("createEvent.private")}
            </Text>
            <LockIcon/>
        </View>
    ), []);

    return (
        <ScreenWithHeader
            screenTitle={t("createEvent.title")}
            backButton={true}
            rightHeaderChild={rightHeaderChild}
            disablePadding={true}
            disableScrollView={true}
        >
            <ScreenWithHeader.InternalPadding>
                <ScrollView
                    contentContainerStyle={styles.contentContainer}
                    showsVerticalScrollIndicator={false}

                >
                    <View style={styles.formContainer}>
                        <InputField
                            label={t("createEvent.fields.eventTitle.label")}
                            placeholder={t("createEvent.fields.eventTitle.placeholder")}
                            value={eventTitle}
                            onChangeText={handleEventTitleChange}
                            icon={titleIcon}
                            maxLength={150}
                        />

                        <InputField
                            label={t("createEvent.fields.description.label")}
                            placeholder={t("createEvent.fields.description.placeholder")}
                            value={eventDescription}
                            onChangeText={handleEventDescriptionChange}
                            maxLength={250}
                            multiline={true}
                            numberOfLines={4}
                            showCharacterCount={true}
                        />

                        <View style={styles.visibilityAvatarContainer}>
                            <View style={styles.creatorContainer}>
                                <View style={styles.avatarContainer}/>
                                <View style={styles.creatorInfo}>
                                    <Text style={styles.creatorByText}>{t("createEvent.createdBy")}</Text>
                                    <Text style={styles.creatorNameText}>{t("createEvent.createdByName")}</Text>
                                </View>
                            </View>
                            <View style={styles.visibilityContainer}>
                                <Text style={styles.visibilityText}>{t("createEvent.visibleToPublic")}</Text>
                                <Switch
                                    value={isPublic}
                                    onValueChange={handleToggleVisibility}
                                    thumbColor={stylesConstants.colors.gray100}
                                    trackColor={{
                                        false: stylesConstants.colors.gray300,
                                        true: stylesConstants.colors.brand.brand500
                                    }}
                                />
                            </View>
                        </View>


                        <TouchableOpacity onPress={handleDateTimePress}>
                            <InputField
                                label={t("createEvent.fields.dateTime.label")}
                                placeholder={t("createEvent.fields.dateTime.placeholder")}
                                value={eventDateTime}
                                onChangeText={() => {
                                }}
                                icon={dateTimeIcon}
                            />
                        </TouchableOpacity>

                        <TouchableOpacity onPress={handleEventTypePress}>
                            <InputField
                                label={t("createEvent.fields.eventType.label")}
                                placeholder={t("createEvent.fields.eventType.placeholder")}
                                value={eventType}
                                onChangeText={() => {
                                }}
                                icon={eventTypeIcon}
                            />
                        </TouchableOpacity>

                        <TouchableOpacity onPress={handleInviteMembersPress}>
                            <InputField
                                label={t("createEvent.fields.inviteMembers.label")}
                                placeholder={t("createEvent.fields.inviteMembers.placeholder")}
                                value={inviteMembers}
                                onChangeText={() => {
                                }}
                                icon={inviteMembersIcon}
                            />
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={styles.additionalInfoButton}
                            onPress={handleAdditionalInfoPress}
                        >
                            <PlusIcon width={20} height={20}/>
                            <Text style={styles.additionalInfoText}>
                                {t("createEvent.additionalInfoButton")}
                            </Text>
                        </TouchableOpacity>
                    </View>

                    <View style={[styles.bottomContainer, {paddingBottom: insets.bottom}]}>
                        <View style={styles.buttonContainer}>
                            <Button
                                text="createEvent.buttons.publish"
                                onPress={handlePublishEvent}
                                backgroundColor={stylesConstants.colors.brand.brand500}
                            />
                            <Button
                                text="createEvent.buttons.cancel"
                                onPress={handleCancel}
                                backgroundColor="transparent"
                                disableBorder
                                disabledColor
                                borderColor={stylesConstants.colors.gray300}
                            />
                        </View>
                    </View>
                </ScrollView>
            </ScreenWithHeader.InternalPadding>

        </ScreenWithHeader>
    );
};

export default CreateEvent;
