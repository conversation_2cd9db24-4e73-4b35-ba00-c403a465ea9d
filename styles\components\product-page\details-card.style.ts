import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        padding: 16
    },
    title: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        color: stylesConstants.colors.textPrimary,
        marginBottom: 16,
        textAlign: "center",
        lineHeight: 18
    },
    table: {
        gap: 1
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 4,
        marginBottom: 1
    },
    alternateRow: {
        backgroundColor: stylesConstants.colors.gray700
    },
    label: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        color: stylesConstants.colors.textPrimary,
        lineHeight: 18,
        flex: 1
    },
    value: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 600,
        color: stylesConstants.colors.textPrimary,
        textAlign: "right",
        flex: 1
    }
});

export default styles;
