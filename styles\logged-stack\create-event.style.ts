import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    contentContainer: {
        flexGrow: 1,
        gap: 16
    },
    formContainer: {
        gap: 16
    },
    creatorContainer: {
        flexDirection: "row",
        alignItems: "center",
        gap: 16
    },
    avatarContainer: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: stylesConstants.colors.gray200
    },
    creatorInfo: {
        gap: 4
    },
    visibilityAvatarContainer: {
        flexGrow: 1,
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
    },
    creatorByText: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: "400",
        color: stylesConstants.colors.gray100,
        lineHeight: 18
    },
    creatorNameText: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: "700",
        color: stylesConstants.colors.gray100,
        lineHeight: 18
    },
    visibilityContainer: {
        flexDirection: "row",
        gap: 8,
        alignItems: "center",
        justifyContent: "space-between"
    },
    visibilityText: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 10,
        fontWeight: "400",
        color: stylesConstants.colors.gray100,
        lineHeight: 18
    },
    additionalInfoButton: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        gap: 8,
        height: 24
    },
    additionalInfoText: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: "600",
        color: stylesConstants.colors.gray100,
        lineHeight: 24
    },
    bottomContainer: {
        marginBottom: 100,
        gap: 10
    },
    buttonContainer: {

        gap: 10
    },
    rightHeaderContainer: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        gap: 7.33
    },
    privateText: {
        fontSize: 12,
        fontWeight: "600",
        color: stylesConstants.colors.gray100
    },
    privateIconContainer: {
        width: 20,
        height: 20
    }
});

export default styles;

