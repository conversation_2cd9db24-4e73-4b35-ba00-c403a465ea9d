import React from "react";
import { render } from "@testing-library/react-native";

jest.mock("react-native-svg", () => {
  const Svg = ({ children, ...rest }: any) => <svg {...rest}>{children}</svg>;
  const Path = (props: any) => <path {...props} />;
  return {
    __esModule: true,
    default: Svg,
    Svg,
    Path
  };
});

import EyeIcon from "../../../components/icons/eye-icon";

describe("EyeIcon", () => {
  it("renders without crashing", () => {
    const { toJSON } = render(<EyeIcon />);
    expect(toJSON()).not.toBeUndefined();
  });

  it("allows overriding size", () => {
    const { getByTestId } = render(<EyeIcon width={18} height={18} testID="icon" />);
    const icon = getByTestId("icon");
    expect(icon.props.width).toBe(18);
    expect(icon.props.height).toBe(18);
  });
});
