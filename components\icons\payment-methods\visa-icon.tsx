import React from "react";
import Svg, {SvgProps, Path, Rect} from "react-native-svg";

const VisaIcon: React.FC<SvgProps> = (props) => {
    const width = props.width ?? 34;
    const height = props.height ?? 24;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 34 24"
            fill="none"
            {...props}
        >
            <Rect width={33} height={23} x={0.5} y={0.5} fill="#fff" rx={3.5} />
            <Rect
                width={33}
                height={23}
                x={0.5}
                y={0.5}
                stroke="#F2F4F7"
                rx={3.5}
            />
            <Path
                fill="#172B85"
                fillRule="evenodd"
                d="M10.75 15.858H8.69L7.146 9.792c-.073-.279-.229-.526-.458-.642A6.573 6.573 0 0 0 4.8 8.508v-.233h3.318c.458 0 .801.35.859.758l.801 4.375 2.059-5.133h2.002l-3.089 7.583Zm4.234 0H13.04l1.602-7.584h1.945l-1.602 7.584Zm4.119-5.483c.057-.408.4-.642.801-.642a3.534 3.534 0 0 1 1.888.35l.344-1.633a4.801 4.801 0 0 0-1.774-.35c-1.888 0-3.262 1.05-3.262 2.508 0 1.109.973 1.691 1.66 2.042.743.35 1.03.583.972.933 0 .525-.572.758-1.143.758a4.789 4.789 0 0 1-2.003-.467l-.343 1.634c.687.291 1.43.409 2.117.409 2.117.057 3.432-.992 3.432-2.567 0-1.984-2.69-2.1-2.69-2.975Zm9.497 5.483-1.545-7.584h-1.659a.862.862 0 0 0-.801.584l-2.86 7h2.002l.4-1.108h2.46l.23 1.108H28.6Zm-2.917-5.541.571 2.858h-1.602l1.03-2.858Z"
                clipRule="evenodd"
            />
        </Svg>
    );
};

export default VisaIcon;
