import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.primary
  },
  screenView: {
    flex: 1
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 40,
    justifyContent: "space-between"
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 32
  },
  logoContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 28,
    fontWeight: 700,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 36,
    marginBottom: 12
  },
  subtitle: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 16,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 24
  },
  buttonContainer: {
    gap: 16
  }
});

export default styles;
