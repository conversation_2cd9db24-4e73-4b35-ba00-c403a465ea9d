// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PERSON><PERSON><PERSON> renders 1`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight={100}
  bbWidth={73}
  fill="none"
  focusable={false}
  height={100}
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 100,
        "width": 73,
      },
    ]
  }
  testID="logo"
  vbHeight={100}
  vbWidth={73}
  width={73}
>
  <RNSVGGroup
    fill={null}
    propList={
      [
        "fill",
      ]
    }
  >
    <RNSVGPath
      d="M65.325 92.332H7.694L.357 99.014h72.305l-7.337-6.683ZM21.94 87.204c-.604-.411-.645-.839-.645-1.532v-.298a35.644 35.644 0 0 1-6.812-4.394v.902c0 .202-.016.404-.04.605-.146 1.25-.84 4.45-4.088 4.99-.097.017-.178.025-.274.041l-.976 1.193h14.94c-.403-.29-.815-.58-1.218-.87-.298-.21-.588-.436-.887-.637ZM14.475 63.202a24.644 24.644 0 0 0 6.813 9.126V34.79a24.556 24.556 0 0 0-6.813 9.127V63.202Z"
      fill={
        {
          "payload": 4294967295,
          "type": 0,
        }
      }
      propList={
        [
          "fill",
        ]
      }
    />
    <RNSVGPath
      d="M34.405 78.037c-1.016.37-2.064.596-3.136.637-2.282.088-4.281-.726-6.321-1.645l-.024.032c-6.362-3.265-11.24-9.038-13.311-16.004.08-.443.177-.879.298-1.306.08-.29.17-.572.258-.855.024-.088.306-.774.258-.83l-.573-.823c-.225-.322-.733-1.072-1.273-1.862a26.9 26.9 0 0 1-.065-1.814c0-13.4 10.046-24.486 22.994-26.17l1.572 1.128 1.274 1.54.573.935V17.785l-10.167 3.863C13.273 25.953 3.469 38.587 3.429 53.478c-.435 1.306-1 3-1.193 3.572l-.33.968c-.025.064.596.62.668.693.226.234.452.476.67.717.33.363.637.75.919 1.137 2.588 12.11 11.739 21.81 23.534 25.196l9.353 3.555V76.698l-.137.194c-.492.46-2.161 1.008-2.54 1.145h.032Z"
      fill={
        {
          "payload": 4294967295,
          "type": 0,
        }
      }
      propList={
        [
          "fill",
        ]
      }
    />
    <RNSVGPath
      d="M51.143 87.204c-.298.201-.597.427-.887.637-.403.29-.814.58-1.217.87h14.94l-.976-1.193c-.089-.016-.178-.024-.266-.04-3.25-.54-4.523-3.75-4.668-4.99a4.964 4.964 0 0 1-.04-.606l.104-58.71v-.404l4.33-4.99h-16.31l2.83 3.192-11.917 34.435-8.57-24.76a24.273 24.273 0 0 0-6.072 3.29l6.442 18.608s.58 3.523.25 4.49l-.33.968c-.025.065.596.621.669.694.226.234.451.475.669.717.847.944 1.58 1.96 2.096 3.12.162.363.315.734.468 1.097.04.097.08.185.113.282l3.612 10.844v.016l.62 1.943.944-2.797 2.257-6.886 1.629-4.708c.516-1.072 1.193-2.024 1.991-2.91.218-.243.444-.484.67-.718.072-.073.693-.63.669-.694l-.33-.967c-.235-.694-.009-2.685.136-3.773l6.78-19.576v51.987c0 .693-.04 1.12-.644 1.532h.008ZM14.475 26.138a36.09 36.09 0 0 1 4.716-3.265c.186-.105.363-.21.549-.314a35.117 35.117 0 0 1 2.725-1.363c.338-.153.677-.298 1.015-.443.226-.09.452-.178.678-.266.25-.097.508-.202.758-.29l2.136-2.42H9.75l4.217 4.895.508 3.474v-.008ZM71.694 7.127 64.35 8.328l-3.144 2.879h-4.668V8.328H42.323l-1.806-2.2L36.122.781l-.008.008V.782L31.72 6.127l-1.814 2.201H15.7v2.879h-4.668L7.88 8.328.51 7.127l6.781 7.86H64.873l6.821-7.86Z"
      fill={
        {
          "payload": 4294967295,
          "type": 0,
        }
      }
      propList={
        [
          "fill",
        ]
      }
    />
  </RNSVGGroup>
</RNSVGSvgView>
`;
