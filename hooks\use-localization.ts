import {useEffect, useState} from "react";
import {getCurrentLocalization} from "../utils/i18n";

const useCurrentLocalization = (): string => {
    const [currentLocalization, setCurrentLocalization] = useState<
        string | null
    >(null);

    useEffect(() => {
        getCurrentLocalization().then(setCurrentLocalization);
    }, []);

    return currentLocalization ?? "pt-BR";
};

export default useCurrentLocalization;
