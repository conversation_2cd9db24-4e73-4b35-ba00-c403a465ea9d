import React, {Children, cloneElement, isValidElement, useMemo} from "react";
import stylesConstants from "../styles/styles-constants";
import { View } from "react-native";
import styles from "../styles/components/tab-icon.style";

export interface TabIconProps extends React.PropsWithChildren {
    active: boolean;
}

const TabIcon: React.FC<TabIconProps> = (props) => {
    const iconProps = useMemo(() => ({
        replaceColor: props.active
            ? stylesConstants.colors.fullWhite
            : stylesConstants.colors.gray300
    }), [props.active]);

    return (
        <View style={styles.container}>
            {props.active && <View style={styles.activeLight}/>}
            {Children.map(props.children, (child) => {
                if(isValidElement(child)) {
                    return cloneElement(child, iconProps);
                }
                return child;
            })}
        </View>
    );
};

export default TabIcon;