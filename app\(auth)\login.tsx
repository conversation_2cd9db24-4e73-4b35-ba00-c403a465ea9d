import React, {use<PERSON><PERSON>back, useMemo, useState} from "react";
import Background<PERSON>ogoTexture from "@/components/logos/background-logo-texture";
import Screen from "@/components/screen";
import {ColorValue, Text, View} from "react-native";
import styles from "@/styles/auth/login.style";
import Small<PERSON>ogo from "@/components/logos/small-logo";
import {useTranslation} from "react-i18next";
import InputField from "@/components/input-field";
import {Login as LoginModel} from "@/models/login";
import PasswordIcon from "@/components/icons/password-icon";
import FullSizeButton from "@/components/full-size-button";
import useLogin from "@/hooks/use-login";
import {formatCpf} from "@/utils/cpf";
import LicenceThirdPartyIcon from "@/components/icons/licence-third-party-icon";
import UserService from "@/services/user.service";
import useForgetPassword from "@/hooks/use-forget-password";
import UserIcon from "@/components/icons/user-icon";
import PhoneIcon from "@/components/icons/phone-icon";
import useUpsell from "@/hooks/use-upsell";

enum LoginStages {
  Document = 0,
  Password = 1,
  Upsell = 2
}

const Login: React.FC = () => {
  const {t} = useTranslation();
  const loginAction = useLogin();
  const upsellAction = useUpsell();
  const forgetPasswordAction = useForgetPassword();
  const [loginForm, setLoginForm] = useState<LoginModel>({
    document: "",
    password: ""
  });
  const [upsellForm, setUpsellForm] = useState({
    name: "",
    phone: ""
  });
  const [stage, setStage] = useState<LoginStages>(LoginStages.Document);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const errors = useMemo(
    () => ({
      ...loginAction.errors,
      ...upsellAction.errors,
      ...validationErrors
    }),
    [loginAction.errors, validationErrors]
  );

  const validDocument = useMemo(() => {
    return loginForm.document && loginForm.document.length >= 14;
  }, [loginForm.document]);

  const validPhone = useMemo(() => {
    return (
      upsellForm.phone &&
      upsellForm.phone.length >= 10 &&
      upsellForm.phone.length <= 11
    );
  }, [upsellForm.phone]);

  const onFormChange = useCallback(
    (field: "document" | "password") => (value: string) => {
      setLoginForm((prev) => ({
        ...prev,
        [field]: field === "document" ? formatCpf(value) : value
      }));
    },
    []
  );

  const onUpsellFormChange = useCallback(
    (field: "name" | "phone") => (value: string) => {
      setUpsellForm((prev) => ({
        ...prev,
        [field]: value
      }));
    },
    []
  );

  const onNextStage = useCallback(() => {
    switch (stage) {
      case LoginStages.Document:
        if (!validDocument) {
          setValidationErrors({document: t("login.invalidDocument")});
          return;
        }

        UserService.exists(loginForm.document ?? "").subscribe({
          next: (resp) => {
            if (resp.exists) {
              setStage(LoginStages.Password);
            } else {
              setStage(LoginStages.Upsell);
            }
          },
          error: () => {
            setValidationErrors({
              document: t("login.invalidDocument")
            });
          }
        });
        break;
      case LoginStages.Password:
        loginAction.login(loginForm);
        break;
      case LoginStages.Upsell:
        if (!upsellForm.name) {
          setValidationErrors({
            name: t("login.invalidName")
          });
          return;
        }

        if (!validPhone) {
          setValidationErrors({
            phone: t("login.invalidPhone")
          });
          return;
        }

        upsellAction.upsell({
          name: upsellForm.name,
          phoneNumber: upsellForm.phone,
          document: loginForm.document ?? ""
        });
        break;
    }
  }, [stage, loginForm, upsellForm, validDocument, validPhone, t]);

  const documentIcon = useCallback(
    (errorColor?: ColorValue) => (
      <LicenceThirdPartyIcon replaceColor={errorColor} />
    ),
    []
  );

  const passwordIcon = useCallback(
    (errorColor?: ColorValue) => <PasswordIcon replaceColor={errorColor} />,
    []
  );

  const userIcon = useCallback(
    (errorColor?: ColorValue) => <UserIcon replaceColor={errorColor} />,
    []
  );

  const phoneIcon = useCallback(
    (errorColor?: ColorValue) => <PhoneIcon replaceColor={errorColor} />,
    []
  );

  const onRecoveryPasswordButtonPress = useCallback(() => {
    forgetPasswordAction.sendRecoveryEmail({
      document: loginForm.document ?? ""
    });
  }, [forgetPasswordAction.sendRecoveryEmail, loginForm.document]);

  const form = useMemo(() => {
    switch (stage) {
      case LoginStages.Document:
        return (
          <InputField
            key="cpf-input"
            label={t("login.fields.document")}
            icon={documentIcon}
            value={loginForm.document ?? ""}
            maxLength={14}
            onChangeText={onFormChange("document")}
            placeholder={t("login.fields.documentPlaceholder")}
            inputMode="numeric"
            error={errors.document}
          />
        );
      case LoginStages.Password:
        return (
          <InputField
            key="password-input"
            label={t("login.fields.password")}
            icon={passwordIcon}
            value={loginForm.password}
            onChangeText={onFormChange("password")}
            placeholder={t("login.fields.passwordPlaceholder")}
            isPassword
          />
        );
      case LoginStages.Upsell:
        return (
          <View style={styles.gap}>
            <InputField
              key="name-input"
              label={t("login.fields.fullName")}
              icon={userIcon}
              value={upsellForm.name}
              onChangeText={onUpsellFormChange("name")}
              placeholder={t("login.fields.fullNamePlaceholder")}
              error={errors.name}
            />
            <InputField
              key="phone-input"
              label={t("login.fields.phone")}
              icon={phoneIcon}
              value={upsellForm.phone}
              onChangeText={onUpsellFormChange("phone")}
              placeholder={t("login.fields.phonePlaceholder")}
              inputMode="tel"
              maxLength={11}
              error={errors.phone}
            />
          </View>
        );
    }
  }, [stage, loginForm, errors, upsellForm, t]);

  const goToInitialStage = useCallback(() => {
    setStage(LoginStages.Document);
    setLoginForm({document: "", password: ""});
    setValidationErrors({});
  }, []);

  const stageTexts = useMemo(() => {
    switch (stage) {
      case LoginStages.Document:
        return {
          footer: (
            <Text style={[styles.text, styles.createAccountContainer]}>
              {t("login.register.title") + " "}
              <Text style={styles.clickableText}>
                {t("login.register.button")}
              </Text>
            </Text>
          ),
          btnText: t("login.nextButton"),
          description: t("login.description")
        };
      case LoginStages.Password:
        return {
          footer: (
            <View>
              <Text style={[styles.text, styles.createAccountContainer]}>
                {t("login.register.title") + " "}
                <Text style={styles.clickableText}>
                  {t("login.register.button")}
                </Text>
              </Text>
              <Text
                style={[
                  styles.text,
                  styles.forgotPassword,
                  styles.clickableText
                ]}
                onPress={onRecoveryPasswordButtonPress}
              >
                {t("login.forgotPassword")}
              </Text>
            </View>
          ),
          btnText: t("login.nextButton"),
          description: t("login.description")
        };
      case LoginStages.Upsell:
        return {
          footer: (
            <Text
              style={[styles.text, styles.createAccountContainer]}
              onPress={goToInitialStage}
            >
              {t("login.goToStart")}
            </Text>
          ),
          btnText: t("login.access"),
          description: t("login.upsellDescription")
        };
    }
  }, [stage, t]);

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={styles.container}>
          <View>
            <View style={styles.logoContainer}>
              <SmallLogo width={63.3} height={86.4} />
            </View>
            <View style={styles.textContainer}>
              <Text style={[styles.text, styles.title]}>
                {t("login.title")}
              </Text>
              <Text style={styles.text}>{stageTexts.description}</Text>
            </View>
            <View style={styles.fieldsMargin}>{form}</View>
            <FullSizeButton text={stageTexts.btnText} onPress={onNextStage} />
          </View>
          <View>{stageTexts.footer}</View>
        </View>
      </Screen>
    </>
  );
};

export default Login;
