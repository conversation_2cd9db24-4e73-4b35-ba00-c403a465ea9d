import React from "react";
import {View, Text, ScrollView, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import Pill from "../pill";
import styles from "../../styles/components/product-page/similar-products.style";
import stylesConstants from "../../styles/styles-constants";
import ProductPrice from "../products/product-price";
import GroupIcon from "../icons/group-icon";
import OpenProductButton from "../products/open-product-button";

interface SimilarProduct {
  id: string;
  title: string;
  tags: React.ReactNode[];
  price: number;
}

const SimilarProducts: React.FC = () => {
  const {t} = useTranslation();

  const mockProducts: SimilarProduct[] = [
    {
      id: "1",
      title: "Comemoração de 10 anos da Cafeteria Garibaldi",
      tags: [
        <Pill
          key="1"
          icon={
            <GroupIcon
              stroke={stylesConstants.colors.gray700}
              width={12}
              height={12}
            />
          }
          text="ENCONTRO"
          small
        />,
        <Pill
          key="2"
          text="ENCONTRO VIP"
          textColor="#B93815"
          backgroundColor="#FEF6EE"
          small
        />
      ],
      price: 6000
    },
    {
      id: "2",
      title: "Tendências de Design 2025",
      tags: [
        <Pill
          key="3"
          icon={
            <GroupIcon
              stroke={stylesConstants.colors.gray700}
              width={12}
              height={12}
            />
          }
          text="ENCONTRO"
        />,
        <Pill
          key="4"
          text="ENCONTRO VIP"
          textColor="#B93815"
          backgroundColor="#FEF6EE"
        />
      ],
      price: 4500
    },
    {
      id: "3",
      title: "Workshop de Arquitetura Moderna",
      tags: [
        <Pill
          key="5"
          icon={
            <GroupIcon
              stroke={stylesConstants.colors.gray700}
              width={12}
              height={12}
            />
          }
          text="ENCONTRO"
        />,
        <Pill
          key="6"
          text="ENCONTRO VIP"
          textColor="#B93815"
          backgroundColor="#FEF6EE"
        />
      ],
      price: 4500
    }
  ];

  const renderProductCard = (product: SimilarProduct) => (
    <TouchableOpacity key={product.id} style={styles.productCard}>
      <View style={styles.productContent}>
        <View style={styles.tagsContainer}>
          {product.tags.map((tag, index) => (
            <View key={index + 1}>{tag}</View>
          ))}
        </View>

        <Text style={styles.productTitle}>{product.title}</Text>

        <View style={styles.bottomActions}>
          <ProductPrice price={product.price} />
          <View>
            <OpenProductButton noBackground productId={parseInt(product.id)} />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View>
      <View style={styles.header}>
        <Text style={styles.title}>{t("productPage.similarProducts")}</Text>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        {mockProducts.map(renderProductCard)}
      </ScrollView>
    </View>
  );
};

export default SimilarProducts;
