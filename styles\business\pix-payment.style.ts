import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    marginBottom: 8
  },
  subtitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24
  },
  valueContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  valueLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginBottom: 4
  },
  valueAmount: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32
  },
  qrContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    alignItems: "center",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  qrTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 16
  },
  qrCodePlaceholder: {
    width: 200,
    height: 200,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16
  },
  qrCodeText: {
    color: stylesConstants.colors.fullBlack,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    textAlign: "center"
  },
  copyButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: "center"
  },
  copyButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  instructionsContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  instructionsTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 16
  },
  instructionStep: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginBottom: 8
  },
  instructionStepBold: {
    fontWeight: 600
  },
  instructionNote: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    marginTop: 8
  },
  timerContainer: {
    alignItems: "center",
    marginBottom: 24
  },
  timerText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    textAlign: "center"
  },
  timerHighlight: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: 600
  },
  buttonContainer: {
    marginTop: 32,
    gap: 16
  }
});

export default styles;
