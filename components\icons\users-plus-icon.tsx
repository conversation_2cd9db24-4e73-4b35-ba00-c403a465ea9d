import Svg, {Path, SvgProps} from "react-native-svg";
import React from "react";

const UsersPlusIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={20}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke="#F2F4F7"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.67}
                d="M15.833 17.5v-5m-2.5 2.5h5M10 12.5H6.667c-1.554 0-2.33 0-2.943.254a3.334 3.334 0 0 0-1.804 1.804c-.253.612-.253 1.389-.253 2.942m11.25-14.758a3.334 3.334 0 0 1 0 6.182m-1.667-3.09a3.333 3.333 0 1 1-6.667 0 3.333 3.333 0 0 1 6.667 0Z"
            />
        </Svg>
    );
};

export default UsersPlusIcon;