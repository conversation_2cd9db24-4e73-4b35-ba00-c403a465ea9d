import React from "react";
import { render, fireEvent } from "@testing-library/react-native";

// Mock expo-linear-gradient
jest.mock("expo-linear-gradient", () => {
  const { View } = require("react-native");
  return {
    LinearGradient: (props: any) => <View {...props} />,
  };
});

// Mock i18n translation
jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

// Mock expo-router and expose navigateMock
jest.mock("expo-router", () => {
  const navigateMock = jest.fn();
  return {
    __esModule: true,
    useRouter: () => ({ navigate: navigateMock }),
    navigateMock,
  };
});

import OverlayTop from "../../../components/intro/overlay-top";

describe("OverlayTop component", () => {
  it("renders login button with translated text", () => {
    const { getByText } = render(<OverlayTop />);

    expect(getByText("intro.loginButton")).toBeTruthy();
  });

  it("navigates to /login when login button is pressed", () => {
    const { getByText } = render(<OverlayTop />);

    fireEvent.press(getByText("intro.loginButton"));

    const { navigateMock } = require("expo-router");
    expect(navigateMock).toHaveBeenCalledWith("/login");
  });
}); 