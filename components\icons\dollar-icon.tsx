import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface DollarIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const DollarIcon: React.FC<DollarIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#FCFCFD", [props.replaceColor]);

    return (
        <Svg
            width={20}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 1.667v16.666M13.333 5H8.333a2.5 2.5 0 0 0 0 5h3.334a2.5 2.5 0 0 1 0 5H6.667"
            />
        </Svg>
    );
};

export default DollarIcon;
