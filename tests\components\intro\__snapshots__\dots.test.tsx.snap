// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Dots component renders three dots and matches snapshot 1`] = `
<View
  style={
    [
      undefined,
      {
        "alignItems": "center",
        "display": "flex",
        "flexDirection": "row",
        "gap": 12,
        "height": 12,
      },
    ]
  }
>
  <View
    style={
      [
        {
          "alignSelf": "stretch",
          "borderColor": "#FFFFFF",
          "borderRadius": 24,
          "borderStyle": "solid",
          "borderWidth": 2,
          "width": 24,
        },
        {
          "backgroundColor": "rgba(255,255,255,1)",
          "borderRadius": 28,
          "width": 56,
        },
      ]
    }
  />
  <View
    style={
      [
        {
          "alignSelf": "stretch",
          "borderColor": "#FFFFFF",
          "borderRadius": 24,
          "borderStyle": "solid",
          "borderWidth": 2,
          "width": 24,
        },
        {
          "backgroundColor": "rgba(255,255,255,0)",
          "borderRadius": 12,
          "width": 24,
        },
      ]
    }
  />
  <View
    style={
      [
        {
          "alignSelf": "stretch",
          "borderColor": "#FFFFFF",
          "borderRadius": 24,
          "borderStyle": "solid",
          "borderWidth": 2,
          "width": 24,
        },
        {
          "backgroundColor": "rgba(255,255,255,0)",
          "borderRadius": 12,
          "width": 24,
        },
      ]
    }
  />
</View>
`;
