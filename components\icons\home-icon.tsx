import Svg, {Path, SvgProps} from "react-native-svg";
import React, {useMemo} from "react";
import {ColorValue} from "react-native";

export interface HomeIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const HomeIcon: React.FC<HomeIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#D0D5DD", [props.replaceColor]);

    return (
        <Svg
            width={25}
            height={24}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M13.882 2.764c-.351-.273-.527-.41-.72-.463a1 1 0 0 0-.523 0c-.194.053-.37.19-.721.463L5.135 8.039c-.453.353-.68.529-.843.75a2 2 0 0 0-.318.65c-.074.264-.074.551-.074 1.126V17.8c0 1.12 0 1.68.218 2.108a2 2 0 0 0 .874.874C5.42 21 5.98 21 7.1 21h2c.28 0 .42 0 .527-.055a.5.5 0 0 0 .219-.218c.054-.107.054-.247.054-.527v-6.6c0-.56 0-.84.109-1.054a1 1 0 0 1 .437-.437c.214-.11.494-.11 1.054-.11h2.8c.56 0 .84 0 1.054.11a1 1 0 0 1 .437.437c.109.214.109.494.109 1.054v6.6c0 .28 0 .42.054.527a.5.5 0 0 0 .219.218c.107.055.247.055.527.055h2c1.12 0 1.68 0 2.108-.218a2 2 0 0 0 .874-.874c.218-.428.218-.988.218-2.108v-7.235c0-.575 0-.862-.074-1.126a2 2 0 0 0-.318-.65c-.163-.221-.39-.397-.843-.75l-6.783-5.275Z"
            />
        </Svg>
    );
};

export default HomeIcon;