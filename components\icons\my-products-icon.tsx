import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface MyProductsIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const MyProductsIcon: React.FC<MyProductsIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={18} height={20} viewBox="0 0 18 20" fill="none" {...props}>
      <Path
        d="M3.6 2.20033L2.3 3.93366C2.04251 4.27697 1.91377 4.44863 1.91676 4.59232C1.91936 4.71736 1.97799 4.83462 2.07646 4.91172C2.18962 5.00032 2.40419 5.00032 2.83333 5.00032H15.1667C15.5958 5.00032 15.8104 5.00032 15.9235 4.91172C16.022 4.83462 16.0806 4.71736 16.0832 4.59232C16.0862 4.44863 15.9575 4.27697 15.7 3.93366L14.4 2.20033M3.6 2.20033C3.74667 2.00477 3.82 1.90699 3.91294 1.83647C3.99525 1.77401 4.08846 1.72741 4.18782 1.69903C4.3 1.66699 4.42222 1.66699 4.66667 1.66699H13.3333C13.5778 1.66699 13.7 1.66699 13.8122 1.69903C13.9115 1.72741 14.0047 1.77401 14.0871 1.83647C14.18 1.90699 14.2533 2.00477 14.4 2.20033M3.6 2.20033L2.03333 4.28921C1.83545 4.55306 1.73651 4.68498 1.66625 4.83026C1.6039 4.95917 1.55843 5.09559 1.53096 5.23612C1.5 5.3945 1.5 5.55941 1.5 5.88921L1.5 15.667C1.5 16.6004 1.5 17.0671 1.68166 17.4236C1.84144 17.7372 2.09641 17.9922 2.41002 18.152C2.76654 18.3337 3.23325 18.3337 4.16667 18.3337L13.8333 18.3337C14.7668 18.3337 15.2335 18.3337 15.59 18.152C15.9036 17.9922 16.1586 17.7372 16.3183 17.4236C16.5 17.0671 16.5 16.6004 16.5 15.667V5.88921C16.5 5.55941 16.5 5.3945 16.469 5.23613C16.4416 5.09559 16.3961 4.95918 16.3338 4.83026C16.2635 4.68498 16.1646 4.55306 15.9667 4.28921L14.4 2.20033M12.3333 8.33366C12.3333 9.21771 11.9821 10.0656 11.357 10.6907C10.7319 11.3158 9.88405 11.667 9 11.667C8.11594 11.667 7.2681 11.3158 6.64298 10.6907C6.01786 10.0656 5.66667 9.21771 5.66667 8.33366"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default MyProductsIcon;
