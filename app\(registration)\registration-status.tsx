import React from "react";
import {Text, View, ScrollView} from "react-native";
import Screen from "../../components/screen";
import BackgroundLogoTexture from "../../components/logos/background-logo-texture";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import CheckCircleIcon from "../../components/icons/check-circle-icon";
import ClockIcon from "../../components/icons/clock-icon";
import WarningIcon from "../../components/icons/warning-icon";

const RegistrationStatus: React.FC = () => {
  const {t} = useTranslation();

  const statusItems = [
    {
      title: t("registrationStatus.personalData", "Dados Pessoais"),
      status: "completed",
      icon: <CheckCircleIcon width={24} height={24} />
    },
    {
      title: t("registrationStatus.professionalData", "Dados Profissionais"),
      status: "completed",
      icon: <CheckCircleIcon width={24} height={24} />
    },
    {
      title: t("registrationStatus.documents", "Documentos"),
      status: "pending",
      icon: <ClockIcon width={24} height={24} />
    },
    {
      title: t("registrationStatus.payment", "Pagamento"),
      status: "error",
      icon: <WarningIcon width={24} height={24} />
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "#4CAF50";
      case "pending":
        return "#FF9800";
      case "error":
        return "#F44336";
      default:
        return "#ccc";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return t("registrationStatus.completed", "Concluído");
      case "pending":
        return t("registrationStatus.pending", "Pendente");
      case "error":
        return t("registrationStatus.error", "Erro");
      default:
        return "";
    }
  };

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <ScrollView style={{flex: 1, padding: 20}}>
          <BackButton />
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#fff",
              textAlign: "center",
              marginBottom: 20
            }}
          >
            {t("registrationStatus.title", "Status do Cadastro")}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "#fff",
              textAlign: "center",
              marginBottom: 30
            }}
          >
            {t(
              "registrationStatus.description",
              "Acompanhe o progresso do seu cadastro"
            )}
          </Text>

          <View style={{marginBottom: 30}}>
            {statusItems.map((item, index) => (
              <View
                key={`status-${item.title.slice(0, 20)}-${index}`}
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  backgroundColor: "rgba(255,255,255,0.1)",
                  padding: 15,
                  borderRadius: 8,
                  marginBottom: 10
                }}
              >
                <View style={{marginRight: 15}}>{item.icon}</View>
                <View style={{flex: 1}}>
                  <Text
                    style={{color: "#fff", fontSize: 16, fontWeight: "bold"}}
                  >
                    {item.title}
                  </Text>
                  <Text
                    style={{color: getStatusColor(item.status), fontSize: 14}}
                  >
                    {getStatusText(item.status)}
                  </Text>
                </View>
              </View>
            ))}
          </View>

          <View
            style={{
              backgroundColor: "rgba(255,255,255,0.1)",
              padding: 15,
              borderRadius: 8,
              marginBottom: 30
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: 16,
                fontWeight: "bold",
                marginBottom: 10
              }}
            >
              {t("registrationStatus.nextActions", "Próximas Ações")}
            </Text>
            <Text style={{color: "#ccc", fontSize: 14, lineHeight: 20}}>
              {t(
                "registrationStatus.nextActionsDesc",
                "• Aguarde a análise dos seus documentos\n• Verifique se há problemas com o pagamento\n• Entre em contato com o suporte se necessário"
              )}
            </Text>
          </View>

          <FullSizeButton
            text={t("registrationStatus.refresh", "Atualizar Status")}
            onPress={() => {}}
          />
        </ScrollView>
      </Screen>
    </>
  );
};

export default RegistrationStatus;
