import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import { Text } from "react-native";
import FullSizeButton from "../../components/full-size-button";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe("FullSizeButton component", () => {
  it("renders correctly and matches snapshot", () => {
    const onPressMock = jest.fn();
    const { toJSON, getByText } = render(
      <FullSizeButton text="button.text" onPress={onPressMock} icon={<Text>Icon</Text>} />
    );
    expect(getByText("button.text")).toBeTruthy();
    expect(getByText("Icon")).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it("calls onPress when pressed", () => {
    const onPressMock = jest.fn();
    const { getByText } = render(
      <FullSizeButton text="button.text" onPress={onPressMock} />
    );
    fireEvent.press(getByText("button.text"));
    expect(onPressMock).toHaveBeenCalledTimes(1);
  });
}); 