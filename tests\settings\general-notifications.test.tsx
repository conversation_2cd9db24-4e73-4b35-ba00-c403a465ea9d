import React from "react";
import {render, fireEvent} from "@testing-library/react-native";
import GeneralNotifications from "../../app/(settings)/general-notifications";

// Mock the ScreenWithHeader component
jest.mock("../../components/screen-with-header", () => {
  const {View} = require("react-native");
  return ({children, screenTitle}: any) => (
    <View testID="screen-with-header" accessibilityLabel={screenTitle}>
      {children}
    </View>
  );
});

// Mock the useTranslation hook
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, fallback: string) => fallback || key
  })
}));

describe("GeneralNotifications", () => {
  it("renders correctly with all notification options", () => {
    const {getByText, getAllByRole} = render(<GeneralNotifications />);

    // Check if all notification options are rendered
    expect(getByText("Notificações gerais")).toBeTruthy();
    expect(getByText("Notificações push")).toBeTruthy();
    expect(getByText("Notificações por E-mail / SMS")).toBeTruthy();
    expect(
      getByText("Notificar novidades (ofertas, alertas, etc.)")
    ).toBeTruthy();

    // Check if restore button is rendered
    expect(getByText("Restaurar configurações padrão")).toBeTruthy();

    // Check if switches are rendered
    const switches = getAllByRole("switch");
    expect(switches).toHaveLength(4);
  });

  it("has correct initial switch states", () => {
    const {getAllByRole} = render(<GeneralNotifications />);
    const switches = getAllByRole("switch");

    // Initially, first three switches should be enabled, last one disabled
    expect(switches[0].props.value).toBe(true); // generalNotifications
    expect(switches[1].props.value).toBe(true); // pushNotifications
    expect(switches[2].props.value).toBe(true); // emailSmsNotifications
    expect(switches[3].props.value).toBe(false); // newsAndOffers
  });

  it("restores default settings when restore button is pressed", () => {
    const {getByText, getAllByRole} = render(<GeneralNotifications />);
    const restoreButton = getByText("Restaurar configurações padrão");
    const switches = getAllByRole("switch");

    // Press the restore button
    fireEvent.press(restoreButton);

    // Check that switches maintain their default values
    expect(switches[0].props.value).toBe(true);
    expect(switches[1].props.value).toBe(true);
    expect(switches[2].props.value).toBe(true);
    expect(switches[3].props.value).toBe(false);
  });

  it("displays correct subtitle text based on switch state", () => {
    const {getAllByText} = render(<GeneralNotifications />);

    // Check enabled states (should have 3 instances)
    const enabledTexts = getAllByText("Habilitado");
    expect(enabledTexts).toHaveLength(3);

    // Check disabled state (should have 1 instance)
    const disabledTexts = getAllByText("Desabilitado");
    expect(disabledTexts).toHaveLength(1);
  });
});
