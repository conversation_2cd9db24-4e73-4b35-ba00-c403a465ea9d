import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    sealsContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "space-between",
        gap: 16
    },
    sealTitle: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        color: stylesConstants.colors.textPrimary,
        fontWeight: 400,
        lineHeight: 18,
        textAlign: "center",
        marginTop: 4
    },
    titleContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 16
    },
    title: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        color: stylesConstants.colors.textPrimary,
        fontWeight: 400,
        lineHeight: 18
    },
    seeMore: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        color: stylesConstants.colors.fullWhite,
        fontWeight: 600,
        lineHeight: 18
    }
});

export default styles;
