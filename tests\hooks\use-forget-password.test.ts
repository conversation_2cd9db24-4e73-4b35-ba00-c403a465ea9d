import {act, render, renderHook} from "@testing-library/react-native";
import React from "react";
import {of, throwError} from "rxjs";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({t: (k: string) => k})
}));

const mockNavigate = jest.fn();
const mockReplace = jest.fn();
jest.mock("expo-router", () => ({
  useRouter: () => ({
    navigate: mockNavigate,
    replace: mockReplace
  })
}));

const mockForgetPassword = jest.fn();
const mockResetPassword = jest.fn();
jest.mock("../../services/login.service", () => ({
  __esModule: true,
  default: {
    forgetPassword: (...args: any[]) => mockForgetPassword(...args),
    resetPassword: (...args: any[]) => mockResetPassword(...args)
  }
}));

jest.mock("../../models/login", () => ({
  ForgetPasswordRequestSchema: {
    safeParse: (data: any) => {
      if (!data.document || data.document.length < 11) {
        return {
          success: false,
          error: {
            flatten: () => ({fieldErrors: {document: ["Required"]}})
          }
        };
      }
      return {success: true, data};
    }
  },
  ResetPasswordRequestSchema: {
    safeParse: (data: any) => {
      if (!data.code || !data.password || data.password.length < 8) {
        return {
          success: false,
          error: {
            flatten: () => ({
              fieldErrors: {password: ["Too short"]}
            })
          }
        };
      }
      return {success: true, data};
    }
  }
}));

jest.mock("../../utils/zod-utils", () => ({
  formatZodError: (error: any) => ({password: "errors.passwordMinimumLength"})
}));

const mockEmitError = jest.fn();
jest.mock("../../contexts/error-dialog-context", () => ({
  __esModule: true,
  ErrorType: {Warning: "warning", Error: "error"},
  useErrorMessage: () => ({emitError: mockEmitError})
}));

const mockSetCurrentLoading = jest.fn();
jest.mock("../../contexts/loading-context", () => ({
  useLoading: () => ({
    setCurrentLoading: mockSetCurrentLoading
  })
}));

import useForgetPassword from "../../hooks/use-forget-password";

function setupHook() {
  let hook: ReturnType<typeof useForgetPassword> | undefined;
  function Test() {
    hook = useForgetPassword();
    return null;
  }
  render(React.createElement(Test));
  return hook!;
}

describe("useForgetPassword", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("sendRecoveryEmail", () => {
    it("sends recovery email successfully", () => {
      mockForgetPassword.mockReturnValue(of({success: true}));

      const {result} = renderHook(() => useForgetPassword());

      act(() => {
        result.current.sendRecoveryEmail({document: "12345678901"});
      });

      expect(mockSetCurrentLoading).toHaveBeenCalledWith(true);
      expect(mockForgetPassword).toHaveBeenCalledWith({
        document: "12345678901"
      });
      expect(mockNavigate).toHaveBeenCalledWith({
        pathname: "/recovery-password",
        params: {document: "12345678901"}
      });
      expect(mockSetCurrentLoading).toHaveBeenCalledWith(false);
    });

    it("handles validation error", () => {
      const {result} = renderHook(() => useForgetPassword());

      act(() => {
        result.current.sendRecoveryEmail({document: ""});
      });

      expect(mockEmitError).toHaveBeenCalledWith({
        errorType: "warning",
        title: "errors.requiredField",
        description: "errors.forgetPasswordDescription"
      });
      expect(mockForgetPassword).not.toHaveBeenCalled();
    });

    it("handles service error", () => {
      mockForgetPassword.mockReturnValue(
        throwError(() => new Error("Service error"))
      );

      const {result} = renderHook(() => useForgetPassword());

      act(() => {
        result.current.sendRecoveryEmail({document: "12345678901"});
      });

      expect(mockSetCurrentLoading).toHaveBeenCalledWith(true);
      expect(mockEmitError).toHaveBeenCalledWith({
        errorType: "error",
        title: "errors.emptyFields",
        description: "errors.tryLater"
      });
      expect(mockSetCurrentLoading).toHaveBeenCalledWith(false);
    });
  });

  describe("resetPassword", () => {
    it("resets password successfully", () => {
      mockResetPassword.mockReturnValue(of(null));

      const {result} = renderHook(() => useForgetPassword());

      act(() => {
        result.current.resetPassword({
          code: "123456",
          password: "validPassword123"
        });
      });

      expect(mockSetCurrentLoading).toHaveBeenCalledWith(true);
      expect(mockResetPassword).toHaveBeenCalledWith({
        code: "123456",
        password: "validPassword123"
      });
      expect(mockReplace).toHaveBeenCalledWith(
        "/(auth)/password-recovery/recovery-password-success"
      );
      expect(mockSetCurrentLoading).toHaveBeenCalledWith(false);
    });

    it("handles validation error for weak password", () => {
      const {result} = renderHook(() => useForgetPassword());

      act(() => {
        result.current.resetPassword({
          code: "123456",
          password: "123"
        });
      });

      expect(mockEmitError).toHaveBeenCalledWith({
        errorType: "warning",
        title: "errors.passwordNotSecure",
        description: "errors.passwordNotSecureDescription"
      });
      expect(result.current.errors).toEqual({
        password: "errors.passwordMinimumLength"
      });
      expect(mockResetPassword).not.toHaveBeenCalled();
    });

    it("handles validation error for missing code", () => {
      const {result} = renderHook(() => useForgetPassword());

      act(() => {
        result.current.resetPassword({
          code: "",
          password: "validPassword123"
        });
      });

      expect(mockEmitError).toHaveBeenCalledWith({
        errorType: "warning",
        title: "errors.passwordNotSecure",
        description: "errors.passwordNotSecureDescription"
      });
      expect(mockResetPassword).not.toHaveBeenCalled();
    });

    it("handles service error", () => {
      mockResetPassword.mockReturnValue(
        throwError(() => new Error("Service error"))
      );

      const {result} = renderHook(() => useForgetPassword());

      act(() => {
        result.current.resetPassword({
          code: "123456",
          password: "validPassword123"
        });
      });

      expect(mockSetCurrentLoading).toHaveBeenCalledWith(true);
      expect(mockEmitError).toHaveBeenCalledWith({
        errorType: "error",
        title: "errors.emptyFields",
        description: "errors.tryLater"
      });
      expect(mockSetCurrentLoading).toHaveBeenCalledWith(false);
    });

    it("clears errors on successful validation", () => {
      mockResetPassword.mockReturnValue(of(null));

      const {result} = renderHook(() => useForgetPassword());

      act(() => {
        result.current.resetPassword({
          code: "",
          password: "123"
        });
      });

      expect(Object.keys(result.current.errors)).toHaveLength(1);

      act(() => {
        result.current.resetPassword({
          code: "123456",
          password: "validPassword123"
        });
      });

      expect(result.current.errors).toEqual({});
    });
  });

  describe("errors state", () => {
    it("initializes with empty errors", () => {
      const {result} = renderHook(() => useForgetPassword());
      expect(result.current.errors).toEqual({});
    });
  });
});
