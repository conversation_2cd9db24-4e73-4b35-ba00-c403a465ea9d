import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity, Image} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {router} from "expo-router";

interface UserProfile {
  name: string;
  title: string;
  company: string;
  location: string;
  bio: string;
  avatar: string;
  joinDate: string;
  connections: number;
  events: number;
  achievements: string[];
  skills: string[];
  interests: string[];
}

const AboutMe: React.FC = () => {
  const {t} = useTranslation();
  const [profile] = useState<UserProfile>({
    name: "<PERSON>",
    title: "Diretor de Marketing",
    company: "Tech Solutions Ltda",
    location: "São Paulo, SP",
    bio: t(
      "profile.sampleBio",
      "Profissional experiente em marketing digital com mais de 10 anos de experiência. Apaixonado por inovação e networking. Sempre em busca de novas oportunidades de crescimento e parcerias estratégicas."
    ),
    avatar: "https://via.placeholder.com/120x120/007AFF/FFFFFF?text=JS",
    joinDate: "Janeiro 2023",
    connections: 247,
    events: 18,
    achievements: [
      t("profile.achievement1", "Membro Premium"),
      t("profile.achievement2", "Networking Expert"),
      t("profile.achievement3", "Event Organizer")
    ],
    skills: [
      "Marketing Digital",
      "Estratégia",
      "Liderança",
      "Vendas",
      "Análise de Dados"
    ],
    interests: [
      "Tecnologia",
      "Inovação",
      "Empreendedorismo",
      "Sustentabilidade"
    ]
  });

  const handleEditProfile = () => {
    router.push("/profile/edit-profile");
  };

  return (
    <ScreenWithHeader
      screenTitle={t("profile.aboutMe", "Sobre Mim")}
      backButton
    >
      <ScrollView style={{flex: 1, padding: 20}}>
        {/* Profile Header */}
        <View style={{alignItems: "center", marginBottom: 30}}>
          <Image
            source={{uri: profile.avatar}}
            style={{
              width: 120,
              height: 120,
              borderRadius: 60,
              marginBottom: 15,
              backgroundColor: "#333"
            }}
          />

          <Text
            style={{
              color: "#fff",
              fontSize: 24,
              fontWeight: "bold",
              textAlign: "center",
              marginBottom: 5
            }}
          >
            {profile.name}
          </Text>

          <Text
            style={{
              color: "#007AFF",
              fontSize: 16,
              fontWeight: "600",
              textAlign: "center",
              marginBottom: 5
            }}
          >
            {profile.title}
          </Text>

          <Text
            style={{
              color: "#ccc",
              fontSize: 14,
              textAlign: "center",
              marginBottom: 5
            }}
          >
            {profile.company}
          </Text>

          <Text
            style={{
              color: "#999",
              fontSize: 14,
              textAlign: "center",
              marginBottom: 20
            }}
          >
            📍 {profile.location}
          </Text>

          <TouchableOpacity
            style={{
              backgroundColor: "#007AFF",
              paddingHorizontal: 30,
              paddingVertical: 12,
              borderRadius: 25
            }}
            onPress={handleEditProfile}
          >
            <Text style={{color: "#fff", fontSize: 16, fontWeight: "bold"}}>
              {t("profile.editProfile", "Editar Perfil")}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Stats */}
        <View
          style={{
            flexDirection: "row",
            backgroundColor: "rgba(255,255,255,0.1)",
            borderRadius: 12,
            padding: 20,
            marginBottom: 25
          }}
        >
          <View style={{flex: 1, alignItems: "center"}}>
            <Text style={{color: "#007AFF", fontSize: 24, fontWeight: "bold"}}>
              {profile.connections}
            </Text>
            <Text style={{color: "#ccc", fontSize: 14, textAlign: "center"}}>
              {t("profile.connections", "Conexões")}
            </Text>
          </View>

          <View
            style={{
              width: 1,
              backgroundColor: "rgba(255,255,255,0.2)",
              marginHorizontal: 20
            }}
          />

          <View style={{flex: 1, alignItems: "center"}}>
            <Text style={{color: "#00C851", fontSize: 24, fontWeight: "bold"}}>
              {profile.events}
            </Text>
            <Text style={{color: "#ccc", fontSize: 14, textAlign: "center"}}>
              {t("profile.events", "Eventos")}
            </Text>
          </View>

          <View
            style={{
              width: 1,
              backgroundColor: "rgba(255,255,255,0.2)",
              marginHorizontal: 20
            }}
          />

          <View style={{flex: 1, alignItems: "center"}}>
            <Text style={{color: "#FFD700", fontSize: 24, fontWeight: "bold"}}>
              {profile.joinDate.split(" ")[1]}
            </Text>
            <Text style={{color: "#ccc", fontSize: 14, textAlign: "center"}}>
              {t("profile.memberSince", "Membro desde")}
            </Text>
          </View>
        </View>

        {/* Bio */}
        <View style={{marginBottom: 25}}>
          <Text
            style={{
              color: "#fff",
              fontSize: 18,
              fontWeight: "bold",
              marginBottom: 15
            }}
          >
            {t("profile.bio", "Biografia")}
          </Text>

          <Text
            style={{
              color: "#ccc",
              fontSize: 16,
              lineHeight: 24,
              textAlign: "justify"
            }}
          >
            {profile.bio}
          </Text>
        </View>

        {/* Achievements */}
        <View style={{marginBottom: 25}}>
          <Text
            style={{
              color: "#fff",
              fontSize: 18,
              fontWeight: "bold",
              marginBottom: 15
            }}
          >
            {t("profile.achievements", "Conquistas")}
          </Text>

          <View style={{flexDirection: "row", flexWrap: "wrap", gap: 10}}>
            {profile.achievements.map((achievement, index) => (
              <View
                key={`achievement-${achievement.slice(0, 20)}-${index}`}
                style={{
                  backgroundColor: "rgba(255,215,0,0.2)",
                  borderColor: "#FFD700",
                  borderWidth: 1,
                  paddingHorizontal: 15,
                  paddingVertical: 8,
                  borderRadius: 20,
                  flexDirection: "row",
                  alignItems: "center"
                }}
              >
                <Text style={{fontSize: 16, marginRight: 8}}>🏆</Text>
                <Text
                  style={{color: "#FFD700", fontSize: 14, fontWeight: "bold"}}
                >
                  {achievement}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Skills */}
        <View style={{marginBottom: 25}}>
          <Text
            style={{
              color: "#fff",
              fontSize: 18,
              fontWeight: "bold",
              marginBottom: 15
            }}
          >
            {t("profile.skills", "Habilidades")}
          </Text>

          <View style={{flexDirection: "row", flexWrap: "wrap", gap: 10}}>
            {profile.skills.map((skill, index) => (
              <View
                key={`skill-${skill.slice(0, 20)}-${index}`}
                style={{
                  backgroundColor: "rgba(0,122,255,0.2)",
                  borderColor: "#007AFF",
                  borderWidth: 1,
                  paddingHorizontal: 15,
                  paddingVertical: 8,
                  borderRadius: 20
                }}
              >
                <Text
                  style={{color: "#007AFF", fontSize: 14, fontWeight: "600"}}
                >
                  {skill}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Interests */}
        <View style={{marginBottom: 30}}>
          <Text
            style={{
              color: "#fff",
              fontSize: 18,
              fontWeight: "bold",
              marginBottom: 15
            }}
          >
            {t("profile.interests", "Interesses")}
          </Text>

          <View style={{flexDirection: "row", flexWrap: "wrap", gap: 10}}>
            {profile.interests.map((interest, index) => (
              <View
                key={`interest-${interest.slice(0, 20)}-${index}`}
                style={{
                  backgroundColor: "rgba(255,255,255,0.1)",
                  paddingHorizontal: 15,
                  paddingVertical: 8,
                  borderRadius: 20
                }}
              >
                <Text style={{color: "#ccc", fontSize: 14}}>{interest}</Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default AboutMe;
