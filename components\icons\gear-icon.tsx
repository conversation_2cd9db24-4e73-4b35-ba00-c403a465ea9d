import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const GearIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg width={21} height={20} viewBox="0 0 20 20" fill="none" {...props}>
            <Path
                stroke="#FCFCFD"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.667}
                d="m8.08 16.143.486 1.095a1.843 1.843 0 0 0 3.37 0l.488-1.095a2.021 2.021 0 0 1 2.058-1.186l1.192.127a1.844 1.844 0 0 0 1.685-2.919l-.706-.97A2.024 2.024 0 0 1 16.27 10c0-.428.135-.844.387-1.19l.706-.97a1.842 1.842 0 0 0-.655-2.728 1.844 1.844 0 0 0-1.03-.19l-1.192.126a2.028 2.028 0 0 1-1.225-.26 2.022 2.022 0 0 1-.834-.93l-.49-1.096a1.843 1.843 0 0 0-3.37 0l-.488 1.096a2.021 2.021 0 0 1-2.058 1.19l-1.195-.126A1.844 1.844 0 0 0 3.14 7.84l.706.97a2.024 2.024 0 0 1 0 2.381l-.706.97a1.843 1.843 0 0 0 1.686 2.918l1.191-.127a2.028 2.028 0 0 1 2.062 1.19Z"
            />
            <Path
                stroke="#FCFCFD"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.667}
                d="M10.25 12.5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"
            />
        </Svg>
    );
};

export default GearIcon;
