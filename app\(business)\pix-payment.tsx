import React, {useState, useEffect} from "react";
import {Text, View, ScrollView, TouchableOpacity, Alert} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import FullSizeButton from "../../components/full-size-button";
import {router, useLocalSearchParams} from "expo-router";
import styles from "../../styles/business/pix-payment.style";

const PixPayment: React.FC = () => {
  const params = useLocalSearchParams();
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15 minutes in seconds

  // Get payment data from params
  const totalValue = params.totalValue
    ? parseFloat(params.totalValue as string)
    : 150.0;

  let opportunityData = null;
  try {
    if (params.opportunityData && typeof params.opportunityData === "string") {
      opportunityData = JSON.parse(params.opportunityData);
    }
  } catch (error) {
    console.error("Error parsing opportunity data in PIX payment:", error);
    opportunityData = {};
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const pixCode =
    "00020126580014BR.GOV.BCB.PIX013636c4b8c4-4c4c-4c4c-4c4c-4c4c4c4c4c4c5204000053039865802BR5925CLUB M TECNOLOGIA LTDA6009SAO PAULO62070503***6304A1B2";

  const copyPixCode = async () => {
    try {
      // In a real app, you would use Clipboard from react-native
      // await Clipboard.setString(pixCode);
      Alert.alert(
        "Código copiado!",
        "O código PIX foi copiado para a área de transferência."
      );
    } catch (error) {
      Alert.alert("Erro", "Não foi possível copiar o código. Tente novamente.");
    }
  };

  const handleFinishPayment = () => {
    router.push("/(business)/payment-success?paymentType=pix");
  };

  return (
    <ScreenWithHeader screenTitle="Efetuar pagamento via PIX" backButton>
      <ScrollView style={styles.contentContainer}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            Seu anúncio foi publicado com sucesso!
          </Text>
          <Text style={styles.subtitle}>
            Que incrível! Agora é só aguardar os contatos chegarem até você.
          </Text>
        </View>

        {/* Payment Value */}
        <View style={styles.valueContainer}>
          <Text style={styles.valueLabel}>Valor</Text>
          <Text style={styles.valueAmount}>
            R$ {totalValue.toFixed(2).replace(".", ",")}
          </Text>
        </View>

        {/* QR Code Section */}
        <View style={styles.qrContainer}>
          <Text style={styles.qrTitle}>Copiar código PIX</Text>

          {/* QR Code placeholder */}
          <View style={styles.qrCodePlaceholder}>
            <Text style={styles.qrCodeText}>QR CODE{"\n"}PIX</Text>
          </View>

          <TouchableOpacity style={styles.copyButton} onPress={copyPixCode}>
            <Text style={styles.copyButtonText}>📋 Copiar código PIX</Text>
          </TouchableOpacity>
        </View>

        {/* Instructions */}
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>Instruções de pagamento</Text>

          <Text style={styles.instructionStep}>
            <Text style={styles.instructionStepBold}>1° passo:</Text> Digite ou
            copie o código que foi gerado no campo acima.
          </Text>

          <Text style={styles.instructionStep}>
            <Text style={styles.instructionStepBold}>2° passo:</Text> Acesse sua
            plataforma de pagamento / internet banking.
          </Text>

          <Text style={styles.instructionStep}>
            <Text style={styles.instructionStepBold}>3° passo:</Text> Cole o
            código, confirme o valor e efetue o pagamento.
          </Text>

          <Text style={styles.instructionNote}>
            Pronto! Em alguns minutos o status da sua dívida será atualizado em
            nosso sistema.
          </Text>
        </View>

        {/* Timer */}
        <View style={styles.timerContainer}>
          <Text style={styles.timerText}>
            Você tem{" "}
            <Text style={styles.timerHighlight}>
              {formatTime(timeLeft)} minutos
            </Text>{" "}
            para efetuar o pagamento.
          </Text>
        </View>

        <FullSizeButton
          text="Finalizar pagamento"
          onPress={handleFinishPayment}
        />
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PixPayment;
