import {UnknownOutputParams, useLocalSearchParams} from "expo-router";
import React, {useMemo, useState} from "react";
import {Text, View} from "react-native";
import ScreenWithHeader from "@/components/screen-with-header";
import {Image} from "expo-image";
import BookIcon from "@/components/icons/book-icon";
import AcquireButton from "@/components/product-page/acquire-button";
import Pill from "@/components/pill";
import DetailsCard from "@/components/product-page/details-card";
import PaymentMethods from "@/components/product-page/payment-methods";
import SimilarProducts from "@/components/product-page/similar-products";
import styles from "@/styles/logged-stack/product-page.style";
import {useTranslation} from "react-i18next";
import useProduct from "@/hooks/use-product";

export interface ProductPageParams extends UnknownOutputParams {
  id: string;
}

const ProductPage: React.FC = () => {
  const {t} = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);
  const params = useLocalSearchParams<ProductPageParams>();
  const {productQuery} = useProduct(params.id);

  const truncatedDescription = useMemo(() => {
    const product = productQuery.data;
    return (product?.description.length ?? 0) > 108
      ? product?.description.substring(0, 108) + "..."
      : product?.description;
  }, [productQuery.data]);

  const handleSeeMore = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <ScreenWithHeader
      screenTitle="E-Book"
      backButton={true}
      disablePadding={true}
    >
      <Image
        style={styles.backgroundImage}
        source={require("../../assets/textures/rotated-pattern.png")}
      />

      <View style={styles.mainContainer}>
        <View style={styles.iconContainer}>
          <View style={styles.iconWrapper}>
            <BookIcon width={40} height={40} />
          </View>
        </View>

        <View style={styles.contentContainer}>
          <View style={styles.productInfoContainer}>
            <Text style={styles.productTitle}>{productQuery.data?.name}</Text>
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionText}>
                {isExpanded
                  ? productQuery.data?.description
                  : truncatedDescription}
                <Text onPress={handleSeeMore} style={styles.seeMoreText}>
                  {isExpanded
                    ? ` ${t("productPage.seeLess")}`
                    : ` ${t("productPage.seeMore")}`}
                </Text>
              </Text>
            </View>

            <View style={styles.acquire}>
              <AcquireButton
                tag={
                  <Pill
                    text="ENCONTRO VIP"
                    textColor="#B93815"
                    backgroundColor="#FEF6EE"
                  />
                }
                price={productQuery.data?.value}
              />
            </View>
          </View>

          <DetailsCard />
        </View>
        <View style={styles.bottomSection}>
          <PaymentMethods />
          <SimilarProducts />
        </View>
      </View>
    </ScreenWithHeader>
  );
};

export default ProductPage;
