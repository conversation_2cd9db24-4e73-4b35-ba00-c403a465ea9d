import {Text, View, ColorValue} from "react-native";
import styles from "../styles/components/pill.style";
import stylesConstants from "../styles/styles-constants";

export interface PillProps {
    text: string;
    icon?: React.ReactNode;
    backgroundColor?: ColorValue;
    textColor?: ColorValue;
    borderColor?: ColorValue;
    small?: boolean;
}

const Pill: React.FC<PillProps> = (props) => {
    return (
        <View
            style={[
                {
                    backgroundColor:
                        props.backgroundColor ?? stylesConstants.colors.gray50,
                    borderColor:
                        props.borderColor ?? stylesConstants.colors.gray200
                },
                styles.container,
                props.small && styles.containerSmall
            ]}
        >
            {props.icon}
            <View>
                <Text
                    style={[
                        {
                            color:
                                props.textColor ??
                                stylesConstants.colors.gray700
                        },
                        styles.text,
                        props.small && styles.textSmall
                    ]}
                >
                    {props.text}
                </Text>
            </View>
        </View>
    );
};

export default Pill;
