import {View, Text} from "react-native";
import {Image} from "expo-image";
import styles from "../../styles/components/products/partner-card.style";

export interface ParnerCardProps {
    name: string;
    logo: string;
}

const PartnerCard: React.FC<ParnerCardProps> = (props) => {
    return (
        <View style={styles.container}>
            <Image
                source={{
                    uri: props.logo
                }}
                style={styles.image}
            />
            <Text style={styles.name}>
                {props.name}
            </Text>
        </View>
    );
};

export default PartnerCard;
