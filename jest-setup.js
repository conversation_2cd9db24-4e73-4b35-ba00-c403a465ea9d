// Jest setup file for global mocks

// Mock react-native-reanimated
jest.mock("react-native-reanimated", () => {
  const Reanimated = require("react-native-reanimated/mock");

  // The mock for `call` immediately calls the callback which is incorrect
  // So we override it with a no-op
  Reanimated.default.call = () => {};

  return Reanimated;
});

// Mock expo-notifications
jest.mock("expo-notifications", () => ({
  getExpoPushTokenAsync: jest.fn().mockResolvedValue({data: "mock-token"}),
  getDevicePushTokenAsync: jest
    .fn()
    .mockResolvedValue({data: "mock-device-token"}),
  setNotificationHandler: jest.fn(),
  setNotificationChannelAsync: jest.fn().mockResolvedValue(undefined),
  addNotificationReceivedListener: jest.fn(),
  addNotificationResponseReceivedListener: jest.fn(),
  removeNotificationSubscription: jest.fn(),
  requestPermissionsAsync: jest.fn().mockResolvedValue({status: "granted"}),
  getPermissionsAsync: jest.fn().mockResolvedValue({status: "granted"}),
  AndroidImportance: {
    MAX: 5,
    HIGH: 4,
    DEFAULT: 3,
    LOW: 2,
    MIN: 1
  }
}));

// Mock expo-device
jest.mock("expo-device", () => ({
  isDevice: true,
  deviceType: 1,
  deviceName: "Test Device"
}));

// Mock expo-local-authentication
jest.mock("expo-local-authentication", () => ({
  hasHardwareAsync: jest.fn().mockResolvedValue(true),
  isEnrolledAsync: jest.fn().mockResolvedValue(true),
  authenticateAsync: jest.fn().mockResolvedValue({success: true}),
  AuthenticationType: {
    FINGERPRINT: 1,
    FACIAL_RECOGNITION: 2
  }
}));

// Mock expo-asset
jest.mock("expo-asset", () => ({
  Asset: {
    fromModule: jest.fn(() => ({
      uri: "mocked-asset-uri",
      downloadAsync: jest.fn().mockResolvedValue(undefined)
    }))
  }
}));

// Mock expo-image
jest.mock("expo-image", () => ({
  Image: "Image"
}));

// Mock expo-router
jest.mock("expo-router", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true)
  }),
  useLocalSearchParams: () => ({}),
  useGlobalSearchParams: () => ({}),
  useSegments: () => [],
  usePathname: () => "/",
  Link: "Link",
  Redirect: "Redirect",
  Stack: {
    Screen: "StackScreen"
  },
  Tabs: {
    Screen: "TabsScreen"
  },
  router: {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true)
  }
}));

// Mock @react-native-async-storage/async-storage
jest.mock("@react-native-async-storage/async-storage", () =>
  require("@react-native-async-storage/async-storage/jest/async-storage-mock")
);

// Mock Firebase service
jest.mock("./services/firebase.service", () => ({
  __esModule: true,
  default: {
    initPushNotification: jest.fn(() => ({
      subscribe: jest.fn(({ next }) => {
        // Don't call next immediately to avoid act() warnings
        // Tests can manually trigger this if needed
        return { unsubscribe: jest.fn() };
      })
    }))
  }
}));