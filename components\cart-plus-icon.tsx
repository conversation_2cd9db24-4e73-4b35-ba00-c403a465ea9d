import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const CartPlusIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={24}
            height={24}
            fill="none"
            {...props}
        >
            <Path
                fill="#FCFCFD"
                d="M10.16 10.924c0-.472.388-.863.857-.863h1.428V8.62c0-.472.389-.863.857-.863.469 0 .857.391.857.863v1.44h1.429c.469 0 .857.391.857.863a.867.867 0 0 1-.857.864h-1.429v1.44a.867.867 0 0 1-.857.863.867.867 0 0 1-.857-.864v-1.44h-1.428a.867.867 0 0 1-.857-.863Zm.982 8.349c0 .956-.765 1.727-1.714 1.727a1.718 1.718 0 0 1-1.714-1.727c0-.956.766-1.727 1.714-1.727.949 0 1.714.771 1.714 1.727Zm7.429 0c0 .956-.766 1.727-1.715 1.727a1.718 1.718 0 0 1-1.714-1.727c0-.956.766-1.727 1.714-1.727.949 0 1.715.771 1.715 1.727Zm3.405-12.748-2.285 9.213a.864.864 0 0 1-.835.656H7.428a.869.869 0 0 1-.845-.714L4.423 3.727H2.857A.867.867 0 0 1 2 2.864C2 2.392 2.389 2 2.857 2h2.286c.411 0 .765.3.845.714l.492 2.74h14.662a.856.856 0 0 1 .674.333.87.87 0 0 1 .16.738Zm-1.931.657H6.788l1.349 7.485h10.045l1.851-7.485h.012Z"
            />
        </Svg>
    );
};

export default CartPlusIcon;