let storageLang: string | null = null;

jest.doMock("@react-native-async-storage/async-storage", () => ({
  getItem: jest.fn(() => Promise.resolve(storageLang)),
}));

jest.doMock("expo-localization", () => ({
  getLocales: () => [{ languageCode: "fr" }],
}));

const initMock = jest.fn().mockResolvedValue(undefined);
const useMock = jest.fn().mockReturnThis();

jest.doMock("i18next", () => ({
  __esModule: true,
  default: {
    use: useMock,
    init: initMock,
  },
}));

// minimal mock for react-i18next
jest.doMock("react-i18next", () => ({ initReactI18next: {} }));

describe("i18n init", () => {
  afterEach(() => {
    jest.resetModules();
    initMock.mockClear();
    useMock.mockClear();
  });

  it("prefers saved language from AsyncStorage", async () => {
    storageLang = "en";
    
    jest.isolateModules(() => {
      require("../../utils/i18n");
    });
    
    // Wait for all pending promises to resolve
    await new Promise(resolve => setTimeout(resolve, 0));
    
    expect(initMock).toHaveBeenCalledWith(expect.objectContaining({ lng: "en" }));
  });

  it("falls back to device locale when storage empty", async () => {
    storageLang = null;
    
    jest.isolateModules(() => {
      require("../../utils/i18n");
    });
    
    // Wait for all pending promises to resolve
    await new Promise(resolve => setTimeout(resolve, 0));
    
    expect(initMock).toHaveBeenCalledWith(expect.objectContaining({ lng: "fr" }));
  });
}); 