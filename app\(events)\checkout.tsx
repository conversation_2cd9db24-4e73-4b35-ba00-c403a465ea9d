import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import Screen<PERSON>ithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import {router} from "expo-router";
import styles from "../../styles/events/checkout.style";

interface CheckoutItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  type: "event" | "product" | "service";
}

interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: "pix" | "boleto" | "mastercard" | "visa";
}

const Checkout: React.FC = () => {
  const {t} = useTranslation();
  const [currentStep, setCurrentStep] = useState<"cart" | "payment">("cart");
  const [items, setItems] = useState<CheckoutItem[]>([
    {
      id: "1",
      name: "Mentoria Coletiva sobre Resoluções Jurídicas",
      price: 1580.0,
      quantity: 1,
      type: "event"
    },
    {
      id: "2",
      name: "Encontro - Comemoração de 10 anos da Cafeteria...",
      price: 1200.0,
      quantity: 1,
      type: "event"
    }
  ]);

  const [paymentMethods] = useState<PaymentMethod[]>([
    {
      id: "1",
      name: "PIX (À Vista)",
      description: "Pagamento imediato",
      icon: "�",
      type: "pix"
    },
    {
      id: "2",
      name: "Via Boleto (À Vista / parcelado)",
      description: "Aprovação em até 3 horas",
      icon: "📄",
      type: "boleto"
    },
    {
      id: "3",
      name: "Cartão Mastercard (À vista / parcelado)",
      description: "Crédito (com final 9876) - Pagamento imediato",
      icon: "�",
      type: "mastercard"
    },
    {
      id: "4",
      name: "Cartão Visa (À vista / parcelado)",
      description: "Crédito (com final 1234) - Pagamento imediato",
      icon: "�",
      type: "visa"
    }
  ]);

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    string | null
  >(null);

  const subtotal = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  const discount = 520.0; // Desconto fixo baseado na imagem
  const total = subtotal - discount;

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) return;
    setItems(
      items.map((item) =>
        item.id === itemId ? {...item, quantity: newQuantity} : item
      )
    );
  };

  const removeItem = (itemId: string) => {
    setItems(items.filter((item) => item.id !== itemId));
  };

  const handleNextStep = () => {
    if (currentStep === "cart") {
      if (items.length === 0) {
        // Não permitir avançar se não há itens no carrinho
        return;
      }
      setCurrentStep("payment");
    } else {
      // Finalizar pagamento
      if (selectedPaymentMethod) {
        const selectedMethod = paymentMethods.find(
          (method) => method.id === selectedPaymentMethod
        );
        const paymentType = selectedMethod?.type || "pix";
        router.push(`/(events)/payment-review?paymentType=${paymentType}`);
      } else {
        // Mostrar feedback visual se nenhum método foi selecionado
        console.log("Selecione um método de pagamento");
      }
    }
  };

  const getItemIcon = () => {
    return "�"; // Ícone padrão baseado na imagem
  };

  const renderCartScreen = () => (
    <ScrollView
      style={{flex: 1, paddingHorizontal: 24}}
      showsVerticalScrollIndicator={false}
    >
      {/* Lista de produtos */}
      <View style={styles.sectionContainer}>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 16
          }}
        >
          <Text style={styles.sectionTitle}>Lista de produtos</Text>
          <Text style={{color: "#ccc", fontSize: 14}}>
            ({items.length} itens)
          </Text>
        </View>

        {items.length === 0 ? (
          <View style={styles.emptyCartContainer}>
            <Text style={styles.emptyCartText}>Seu carrinho está vazio</Text>
            <Text style={styles.emptyCartSubtext}>
              Adicione alguns itens para continuar
            </Text>
          </View>
        ) : (
          items.map((item) => (
            <View key={item.id} style={styles.productCard}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginBottom: 12
                }}
              >
                <View style={styles.productIcon}>
                  <Text style={{fontSize: 20}}>{getItemIcon()}</Text>
                </View>

                <View style={styles.productInfo}>
                  <Text style={styles.productName}>{item.name}</Text>
                  <Text style={styles.productPrice}>
                    R$ {item.price.toFixed(2).replace(".", ",")}
                  </Text>
                </View>

                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => removeItem(item.id)}
                >
                  <Text style={styles.removeButtonText}>🗑️ Remover</Text>
                </TouchableOpacity>
              </View>

              <View style={{flexDirection: "row", justifyContent: "flex-end"}}>
                <View style={styles.quantityControls}>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => updateQuantity(item.id, item.quantity - 1)}
                  >
                    <Text style={styles.quantityButtonText}>−</Text>
                  </TouchableOpacity>

                  <Text style={styles.quantityText}>{item.quantity}</Text>

                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => updateQuantity(item.id, item.quantity + 1)}
                  >
                    <Text style={styles.quantityButtonText}>+</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ))
        )}
      </View>

      {/* Resumo de compra */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Resumo de compra</Text>

        <View style={styles.orderSummaryContainer}>
          <View style={styles.orderItem}>
            <Text style={styles.orderItemName}>
              Subtotal ({items.length} itens)
            </Text>
            <Text style={styles.orderItemPrice}>
              R$ {subtotal.toFixed(2).replace(".", ",")}
            </Text>
          </View>

          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Valor total</Text>
            <Text style={styles.totalValue}>
              R$ {total.toFixed(2).replace(".", ",")}
            </Text>
          </View>
        </View>
      </View>

      <FullSizeButton
        text="Ir para métodos de pagamento"
        onPress={handleNextStep}
        variant={items.length === 0 ? "secondary" : "primary"}
      />
    </ScrollView>
  );

  const renderPaymentScreen = () => (
    <ScrollView
      style={{flex: 1, paddingHorizontal: 24}}
      showsVerticalScrollIndicator={false}
    >
      {/* Métodos de pagamento */}
      <View style={styles.sectionContainer}>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 16
          }}
        >
          <Text style={styles.sectionTitle}>Métodos de pagamento</Text>
          <TouchableOpacity>
            <Text style={{color: "#0F7C4D", fontSize: 14, fontWeight: 600}}>
              Adicionar novo
            </Text>
          </TouchableOpacity>
        </View>

        {paymentMethods.map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.paymentMethodCard,
              selectedPaymentMethod === method.id &&
                styles.paymentMethodCardSelected
            ]}
            onPress={() => setSelectedPaymentMethod(method.id)}
          >
            <View style={styles.paymentMethodIcon}>
              <Text style={{fontSize: 16}}>{method.icon}</Text>
            </View>

            <View style={styles.paymentMethodInfo}>
              <Text style={styles.paymentMethodName}>{method.name}</Text>
              <Text style={styles.paymentMethodDescription}>
                {method.description}
              </Text>
            </View>

            <View
              style={[
                styles.radioButton,
                selectedPaymentMethod === method.id &&
                  styles.radioButtonSelected
              ]}
            >
              {selectedPaymentMethod === method.id && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Resumo de compra */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Resumo de compra</Text>

        <View style={styles.orderSummaryContainer}>
          <View style={styles.orderItem}>
            <Text style={styles.orderItemName}>
              Subtotal ({items.length} itens)
            </Text>
            <Text style={styles.orderItemPrice}>
              R$ {subtotal.toFixed(2).replace(".", ",")}
            </Text>
          </View>

          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Valor total</Text>
            <Text style={styles.totalValue}>
              R$ {total.toFixed(2).replace(".", ",")}
            </Text>
          </View>
        </View>
      </View>

      <FullSizeButton
        text="Selecionar pagamento"
        onPress={handleNextStep}
        variant={selectedPaymentMethod ? "primary" : "secondary"}
      />
    </ScrollView>
  );

  return (
    <ScreenWithHeader
      screenTitle={currentStep === "cart" ? "Carrinho de compras" : "Pagamento"}
      backButton
    >
      {currentStep === "cart" ? renderCartScreen() : renderPaymentScreen()}
    </ScreenWithHeader>
  );
};

export default Checkout;
