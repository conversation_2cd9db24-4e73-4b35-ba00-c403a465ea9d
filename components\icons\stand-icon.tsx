import React from "react";
import Svg, {SvgProps, Path} from "react-native-svg";

const StandIcon: React.FC<SvgProps> = (props) => {
    const width = props.width ?? 20;
    const height = props.height ?? 20;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill="none"
            {...props}
        >
            <Path
                stroke={props.stroke ?? "#fff"}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="m7.5 14.167-4.167 4.167m9.167-4.167 4.167 4.167M10 1.667v1.667m0 15v-4.167m-5.667 0h11.334c.933 0 1.4 0 1.756-.182.314-.16.569-.414.729-.728.181-.357.181-.823.181-1.757V6c0-.933 0-1.4-.181-1.756a1.667 1.667 0 0 0-.729-.729c-.356-.181-.823-.181-1.756-.181H4.333c-.933 0-1.4 0-1.756.181-.314.16-.569.415-.729.729-.181.356-.181.823-.181 1.756v5.5c0 .934 0 1.4.181 1.757.16.314.415.569.729.728.356.182.823.182 1.756.182Z"
            />
        </Svg>
    );
};

export default StandIcon;
