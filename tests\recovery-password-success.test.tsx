import React from "react";
import {render, fireEvent} from "@testing-library/react-native";

// Translation mock
jest.mock("react-i18next", () => ({
  useTranslation: () => ({t: (k: string) => k})
}));

// Stub heavy components
jest.mock("@/components/logos/background-logo-texture", () => () => null);
jest.mock("@/components/screen", () => {
  return ({children}: any) => <>{children}</>;
});

jest.mock("@/components/icons/check-circle-icon", () => () => null);

// Reanimated mock
jest.mock("react-native-reanimated", () =>
  require("react-native-reanimated/mock")
);

// Router mock
const mockReplace = jest.fn();
jest.mock("expo-router", () => ({
  useRouter: () => ({replace: mockReplace})
}));

// Import component under test
import RecoveryPasswordSuccess from "../app/(auth)/password-recovery/recovery-password-success";

describe("RecoveryPasswordSuccess screen", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders title and description", () => {
    const {getByText} = render(<RecoveryPasswordSuccess />);
    expect(getByText("recoveryPasswordSuccess.successTitle")).toBeTruthy();
    expect(
      getByText("recoveryPasswordSuccess.successDescription")
    ).toBeTruthy();
  });

  it("navigates to login on button press", () => {
    const {getByText} = render(<RecoveryPasswordSuccess />);
    fireEvent.press(getByText("recoveryPasswordSuccess.successButton"));
    expect(mockReplace).toHaveBeenCalledWith("/(auth)/login");
  });
});
