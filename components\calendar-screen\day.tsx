import React, {useCallback, useMemo} from "react";
import {TouchableOpacity, Text, ColorValue, View} from "react-native";
import styles from "../../styles/components/calendar-screen/day.style";
import useWeekday from "../../hooks/use-weekday";

export interface DayProps {
    date: Date;
    selected: boolean;
    onPress?: (date: Date) => void;
    dots?: ColorValue[];
}

const Day: React.FC<DayProps> = (props) => {
    const weekday = useWeekday(props.date);
    const day = useMemo(() => props.date.getDate(), [props.date]);

    const handlePress = useCallback(() => props.onPress?.(props.date),
        [props.date, props.onPress]);

    return (
        <View style={styles.mainContainer}>
            <TouchableOpacity style={[styles.container, props.selected && styles.selectedDay]} onPress={handlePress}>
                <Text style={[styles.text, styles.dayNumber]}>{day}</Text>
                <Text style={styles.text}>{weekday}</Text>
            </TouchableOpacity>
            {props.dots && (<View style={styles.dotList}>
                {props.dots.map((color) => (
                    <View style={[styles.dot, {backgroundColor: color}]} key={`dot${color.toString()}`}/>
                ))}
            </View>)}
        </View>

    );
};

export default Day;