import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface PurchaseHistoryIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const PurchaseHistoryIcon: React.FC<PurchaseHistoryIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={16} height={20} viewBox="0 0 16 20" fill="none" {...props}>
      <Path
        d="M9.66671 1.8916V5.33372C9.66671 5.80043 9.66671 6.03378 9.75754 6.21204C9.83743 6.36885 9.96491 6.49633 10.1217 6.57622C10.3 6.66705 10.5333 6.66705 11 6.66705H14.4422M9.66671 14.167H4.66671M11.3334 10.8337H4.66671M14.6667 8.32385V14.3337C14.6667 15.7338 14.6667 16.4339 14.3942 16.9686C14.1545 17.439 13.7721 17.8215 13.3017 18.0612C12.7669 18.3337 12.0668 18.3337 10.6667 18.3337H5.33337C3.93324 18.3337 3.23318 18.3337 2.6984 18.0612C2.22799 17.8215 1.84554 17.439 1.60586 16.9686C1.33337 16.4339 1.33337 15.7338 1.33337 14.3337V5.66699C1.33337 4.26686 1.33337 3.5668 1.60586 3.03202C1.84554 2.56161 2.22799 2.17916 2.6984 1.93948C3.23318 1.66699 3.93324 1.66699 5.33337 1.66699H8.00985C8.62133 1.66699 8.92707 1.66699 9.21479 1.73607C9.46988 1.79731 9.71374 1.89832 9.93742 2.03539C10.1897 2.19 10.4059 2.40619 10.8383 2.83857L13.4951 5.49542C13.9275 5.9278 14.1437 6.14399 14.2983 6.39628C14.4354 6.61996 14.5364 6.86382 14.5976 7.11891C14.6667 7.40663 14.6667 7.71237 14.6667 8.32385Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default PurchaseHistoryIcon;
