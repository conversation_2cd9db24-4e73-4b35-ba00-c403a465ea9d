import React from "react";
import {render, fireEvent} from "@testing-library/react-native";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

jest.mock("@react-native-async-storage/async-storage", () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(),
  removeItem: jest.fn()
}));

jest.mock("expo-localization", () => ({
  getLocales: () => [{languageCode: "pt-BR"}]
}));

jest.mock("expo-router", () => ({
  useLocalSearchParams: () => ({document: "52921723085"})
}));

jest.mock("../components/logos/background-logo-texture", () => () => null);
jest.mock("../components/screen", () => {
  return ({children}: any) => <>{children}</>;
});
jest.mock("../components/back-button", () => () => null);

jest.mock("../components/recovery-password/recovery-digits", () => {
  const {TextInput} = require("react-native");
  return ({value, onChange}: any) => (
    <TextInput testID="recovery-digits" value={value} onChangeText={onChange} />
  );
});

jest.mock("../components/icons/password-icon", () => () => null);

const mockResetPassword = jest.fn();
jest.mock("../hooks/use-forget-password", () => () => ({
  resetPassword: (...args: any[]) => mockResetPassword(...args),
  sendRecoveryEmail: jest.fn(),
  errors: {}
}));

const mockEmitError = jest.fn();
jest.mock("../contexts/error-dialog-context", () => ({
  useErrorMessage: () => ({
    emitError: mockEmitError
  }),
  ErrorType: {
    Error: "error"
  }
}));

jest.mock("react-native-reanimated", () =>
  require("react-native-reanimated/mock")
);

import RecoveryPassword from "../app/(auth)/password-recovery/recovery-password";

describe("RecoveryPassword screen", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("advances to step 2 when confirm button pressed", () => {
    const {getByText, getByTestId} = render(<RecoveryPassword />);

    fireEvent.changeText(getByTestId("recovery-digits"), "123456");
    const confirmBtn = getByText("recoveryPassword.confirmButton");
    fireEvent.press(confirmBtn);
    expect(getByText("recoveryPassword.confirmNewPasswordButton")).toBeTruthy();
  });

  it("calls resetPassword with form values on second confirm", () => {
    const {getByText, getByPlaceholderText, getByTestId} = render(
      <RecoveryPassword />
    );

    fireEvent.changeText(getByTestId("recovery-digits"), "123456");
    fireEvent.press(getByText("recoveryPassword.confirmButton"));

    fireEvent.changeText(
      getByPlaceholderText("recoveryPassword.newPasswordPlaceholder"),
      "newPass123"
    );
    fireEvent.changeText(
      getByPlaceholderText("recoveryPassword.repeatNewPasswordPlaceholder"),
      "newPass123"
    );

    fireEvent.press(getByText("recoveryPassword.confirmNewPasswordButton"));

    expect(mockResetPassword).toHaveBeenCalledWith({
      code: "123456",
      password: "newPass123"
    });
  });

  it("shows error when recovery code is not 6 digits", () => {
    const {getByText, getByTestId} = render(<RecoveryPassword />);

    fireEvent.changeText(getByTestId("recovery-digits"), "123");
    fireEvent.press(getByText("recoveryPassword.confirmButton"));

    expect(mockEmitError).toHaveBeenCalledWith({
      errorType: "error",
      title: "errors.authenticationError",
      description: "errors.authenticationErrorDescription"
    });
  });

  it("shows error when passwords don't match", () => {
    const {getByText, getByPlaceholderText, getByTestId} = render(
      <RecoveryPassword />
    );

    fireEvent.changeText(getByTestId("recovery-digits"), "123456");
    fireEvent.press(getByText("recoveryPassword.confirmButton"));

    fireEvent.changeText(
      getByPlaceholderText("recoveryPassword.newPasswordPlaceholder"),
      "newPass123"
    );
    fireEvent.changeText(
      getByPlaceholderText("recoveryPassword.repeatNewPasswordPlaceholder"),
      "differentPass"
    );

    fireEvent.press(getByText("recoveryPassword.confirmNewPasswordButton"));

    expect(mockResetPassword).not.toHaveBeenCalled();
  });

  it("can go back from step 2 to step 1", () => {
    const {getByText, getByTestId} = render(<RecoveryPassword />);

    fireEvent.changeText(getByTestId("recovery-digits"), "123456");
    fireEvent.press(getByText("recoveryPassword.confirmButton"));

    expect(getByText("recoveryPassword.confirmNewPasswordButton")).toBeTruthy();

    expect(getByText("recoveryPassword.confirmNewPasswordButton")).toBeTruthy();
  });

  it("renders step 1 content correctly", () => {
    const {getByText, getByTestId} = render(<RecoveryPassword />);

    expect(getByText("recoveryPassword.title")).toBeTruthy();
    expect(getByText("recoveryPassword.confirmButton")).toBeTruthy();
    expect(getByTestId("recovery-digits")).toBeTruthy();
  });

  it("renders step 2 content correctly", () => {
    const {getByText, getByPlaceholderText, getByTestId} = render(
      <RecoveryPassword />
    );

    fireEvent.changeText(getByTestId("recovery-digits"), "123456");
    fireEvent.press(getByText("recoveryPassword.confirmButton"));

    expect(getByText("recoveryPassword.confirmNewPasswordButton")).toBeTruthy();
    expect(
      getByPlaceholderText("recoveryPassword.newPasswordPlaceholder")
    ).toBeTruthy();
    expect(
      getByPlaceholderText("recoveryPassword.repeatNewPasswordPlaceholder")
    ).toBeTruthy();
    expect(getByText("recoveryPassword.passwordRequisition")).toBeTruthy();
  });
});
