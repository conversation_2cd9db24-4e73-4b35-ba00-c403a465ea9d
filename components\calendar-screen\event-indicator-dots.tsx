import React from "react";
import {View} from "react-native";
import styles from "../../styles/components/calendar-screen/event-indicator-dots.style";

export interface EventIndicatorDotsProps {
  colors?: string[];
}

const EventIndicatorDots: React.FC<EventIndicatorDotsProps> = ({
  colors = []
}) => {
  // Default colors for demonstration - in real app this would come from event data
  const defaultColors = [
    "#FDB022", // alert400
    "#FDB022", // alert400
    "#FDB022", // alert400
    "#FDB022", // alert400
    "#FDB022", // alert400
    "#FDB022" // alert400
  ];

  const dotsToShow = colors.length > 0 ? colors : defaultColors;

  return (
    <View style={styles.container}>
      {dotsToShow.map((color, index) => (
        <View
          key={`event-dot-${color}-${index}`}
          style={[styles.dot, {backgroundColor: color}]}
        />
      ))}
    </View>
  );
};

export default EventIndicatorDots;
