import React, {useMemo} from "react";
import {Text, TouchableOpacity, View} from "react-native";
import {useTranslation} from "react-i18next";
import UserIcon from "../icons/user-icon";
import ClockIcon from "../icons/clock-icon";
import MarkerPinIcon from "../icons/marker-pin-icon";
import styles from "../../styles/components/calendar-screen/calendar-event.style";
import CheckIcon from "../icons/check-icon";
import Badge, {BadgeColor} from "../badge";
import stylesConstants from "../../styles/styles-constants";

export interface CalendarEventData {
  id: string;
  title: string;
  description: string;
  location: string;
  startTime: string;
  endTime: string;
  isPrivate: boolean;
  isFree: boolean;
  isAttending: boolean;
  borderColor?: string;
}

export interface CalendarEventProps {
  event?: CalendarEventData;
}

const CalendarEvent: React.FC<CalendarEventProps> = ({event}) => {
  const {t} = useTranslation();

  // Default event data for backward compatibility
  const defaultEvent: CalendarEventData = {
    id: "default",
    title:
      "Reunião de alinhamento para oportunidade de negócios na Praia Brava - SC",
    description:
      "Lorem ipsum quisque lobortis in eu rhoncus dui nulla lectus sagittis dictum dignissim tellus montes velit lacus mauris id magna tincidunt egestas proin viverra in.",
    location: "Praia dos Amores - Santa Catarina",
    startTime: "08:00 AM",
    endTime: "12:30 PM",
    isPrivate: true,
    isFree: true,
    isAttending: true,
    borderColor: stylesConstants.colors.alert400
  };

  const eventData = event || defaultEvent;

  const successBadgeColors: BadgeColor = useMemo(
    () => ({
      backgroundColor: stylesConstants.colors.brand.brand25,
      borderColor: stylesConstants.colors.success200,
      textColor: stylesConstants.colors.success700
    }),
    []
  );

  return (
    <View>
      <View style={styles.presenceInformation}>
        <View style={styles.presenceLabelContainer}>
          <ClockIcon />
          <Text style={styles.text}>
            {eventData.startTime} - {eventData.endTime}
          </Text>
        </View>
        <View style={styles.presenceLabelContainer}>
          <Text style={styles.text}>
            {eventData.isPrivate
              ? t("calendar.eventTypes.yourAndPrivate")
              : t("calendar.eventTypes.public")}
          </Text>
          <UserIcon width={16} height={16} />
        </View>
      </View>
      <TouchableOpacity
        style={[styles.touchableArea, {borderLeftColor: eventData.borderColor}]}
      >
        <View style={styles.locationLabelContainer}>
          <MarkerPinIcon />
          <Text style={styles.text}>{eventData.location}</Text>
        </View>
        <Text
          ellipsizeMode="tail"
          numberOfLines={2}
          style={[styles.text, styles.eventName]}
        >
          {eventData.title}
        </Text>
        <Text
          ellipsizeMode="tail"
          numberOfLines={1}
          style={[styles.text, styles.eventDescription]}
        >
          {eventData.description}
        </Text>
        <View style={styles.footer}>
          <View style={styles.presenceContainer}>
            <Text style={[styles.text, styles.presenceText]}>
              {eventData.isAttending
                ? t("calendar.eventPresence.iWillGo")
                : t("calendar.eventPresence.notGoing")}
            </Text>
            {eventData.isAttending && <CheckIcon />}
          </View>
          <Badge
            color={successBadgeColors}
            text={
              eventData.isFree
                ? t("calendar.eventTypes.free")
                : t("calendar.eventTypes.paid")
            }
          />
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default CalendarEvent;
