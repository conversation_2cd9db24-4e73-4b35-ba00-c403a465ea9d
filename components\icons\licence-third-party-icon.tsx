import React, {useMemo} from "react";
import {ColorValue} from "react-native";
import Svg, {SvgProps, Path} from "react-native-svg";

export interface LicenceThirdPartyIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const LicenceThirdPartyIcon: React.FC<LicenceThirdPartyIconProps> = (props) => {
    const width = props.width ?? 20;
    const height = props.height ?? 20;
    const color = useMemo(
        () => props.replaceColor ?? "#F2F4F7",
        [props.replaceColor]
    );

    return (
        <Svg
            viewBox="0 0 20 20"
            width={width}
            height={height}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8.725 18.333c-3.327 0-4.99 0-6.025-.976-1.033-.976-1.033-2.548-1.033-5.69V8.333c0-3.142 0-4.714 1.033-5.69 1.034-.976 2.698-.976 6.025-.976h.883c3.328 0 4.991 0 6.025.976 1.034.976 1.034 2.548 1.034 5.69m-10.834-2.5H12.5M5.833 10h5m.834 8.333c1.531-2.29 5.099-2.415 6.666 0m-1.666-5.892c0 .887-.746 1.606-1.667 1.606-.92 0-1.667-.719-1.667-1.606 0-.888.746-1.608 1.667-1.608.92 0 1.667.72 1.667 1.608Z"
            />
        </Svg>
    );
};

export default LicenceThirdPartyIcon;
