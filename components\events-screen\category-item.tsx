import React from "react";
import {Text, TouchableOpacity, View} from "react-native";
import styles from "../../styles/components/events-screen/category-item.style";

export interface CategoryItemProps {
    icon: React.ReactNode;
    name: string;
}

const CategoryItem: React.FC<CategoryItemProps> = (props) => {
    return (
        <TouchableOpacity style={styles.container}>
            <View style={styles.grayContainer}>
                {props.icon}
            </View>
            <Text style={styles.label} ellipsizeMode="tail" numberOfLines={1}>{props.name}</Text>
        </TouchableOpacity>
    );
};

export default CategoryItem;