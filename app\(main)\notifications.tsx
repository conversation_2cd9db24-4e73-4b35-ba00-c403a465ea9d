import React, {useState, useMemo} from "react";
import {Text, View, ScrollView, TouchableOpacity, Image} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import styles from "../../styles/main/notifications.style";
import ClockIcon from "../../components/icons/clock-icon";
import DotsVerticalIcon from "../../components/icons/dots-vertical-icon";

type NotificationFilter = "all" | "read" | "unread";

interface Notification {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type: "event" | "survey" | "general";
  avatar?: string;
  actionButton?: {
    text: string;
    onPress: () => void;
  };
}

const Notifications: React.FC = () => {
  const {t} = useTranslation();
  const [activeFilter, setActiveFilter] = useState<NotificationFilter>("all");

  const [notifications] = useState<Notification[]>([
    {
      id: "1",
      title:
        "Phoenix Baker enviou um convite para o evento Encontro de anual de empreendedores em Balneário C...",
      message: "",
      time: "Enviado hoje, às 9:40 AM",
      read: false,
      type: "event",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      actionButton: {
        text: "Ver evento",
        onPress: () => console.log("Ver evento")
      }
    },
    {
      id: "2",
      title:
        "Lorem ipsum neque sodales amet porttitor vulputate id purus condimentum suspendisse a blandit...",
      message: "",
      time: "Enviado terça-feira, às 15:45 AM",
      read: true,
      type: "general"
    },
    {
      id: "3",
      title:
        "Lorem ipsum neque sodales amet porttitor vulputate id purus condimentum suspendisse a blandit...",
      message: "",
      time: "Enviado sexta, às 15:45 AM",
      read: true,
      type: "general"
    },
    {
      id: "4",
      title: "Club M Alphaville publicou uma nova enquete, participe!",
      message: "",
      time: "Enviado hoje, às 15:45 PM",
      read: false,
      type: "survey",
      actionButton: {
        text: "Ver enquete",
        onPress: () => console.log("Ver enquete")
      }
    },
    {
      id: "5",
      title:
        "Lorem ipsum neque sodales amet porttitor vulputate id purus condimentum suspendisse a blandit...",
      message: "",
      time: "Enviado terça-feira, às 15:45 AM",
      read: true,
      type: "general"
    }
  ]);

  const filteredNotifications = useMemo(() => {
    switch (activeFilter) {
      case "read":
        return notifications.filter((n) => n.read);
      case "unread":
        return notifications.filter((n) => !n.read);
      default:
        return notifications;
    }
  }, [notifications, activeFilter]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "event":
        return <ClockIcon width={20} height={20} />;
      case "survey":
        return (
          <View style={styles.clubIcon}>
            <Text style={styles.clubIconText}>CM</Text>
          </View>
        );
      default:
        return <DotsVerticalIcon width={20} height={20} />;
    }
  };

  const renderFilterTabs = () => (
    <View style={styles.filterContainer}>
      <View style={styles.filterList}>
        <TouchableOpacity
          style={[
            styles.filterTab,
            activeFilter === "all" && styles.filterTabActive
          ]}
          onPress={() => setActiveFilter("all")}
        >
          <Text
            style={[
              styles.filterTabText,
              activeFilter === "all" && styles.filterTabTextActive
            ]}
          >
            Todas
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.filterTab,
            activeFilter === "read" && styles.filterTabActive
          ]}
          onPress={() => setActiveFilter("read")}
        >
          <Text
            style={[
              styles.filterTabText,
              activeFilter === "read" && styles.filterTabTextActive
            ]}
          >
            Lidas
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.filterTab,
            activeFilter === "unread" && styles.filterTabActive
          ]}
          onPress={() => setActiveFilter("unread")}
        >
          <Text
            style={[
              styles.filterTabText,
              activeFilter === "unread" && styles.filterTabTextActive
            ]}
          >
            Não lidas
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <ScreenWithHeader
      screenTitle={t("notifications.title", "Notificações")}
      backButton
    >
      <View style={styles.container}>
        {renderFilterTabs()}
        <ScrollView style={styles.notificationsList}>
          {filteredNotifications.map((notification) => (
            <TouchableOpacity
              key={notification.id}
              style={[
                styles.notificationItem,
                !notification.read && styles.notificationItemUnread
              ]}
            >
              <View style={styles.notificationContent}>
                <View style={styles.notificationHeader}>
                  <View style={styles.notificationIconContainer}>
                    {notification.avatar ? (
                      <Image
                        source={{uri: notification.avatar}}
                        style={styles.notificationAvatar}
                      />
                    ) : (
                      getNotificationIcon(notification.type)
                    )}
                  </View>
                  <View style={styles.notificationTextContainer}>
                    <Text style={styles.notificationTitle}>
                      {notification.title}
                    </Text>
                    {notification.actionButton && (
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={notification.actionButton.onPress}
                      >
                        <Text style={styles.actionButtonText}>
                          {notification.actionButton.text}
                        </Text>
                      </TouchableOpacity>
                    )}
                    <Text style={styles.notificationTime}>
                      {notification.time}
                    </Text>
                  </View>
                  {!notification.read && (
                    <View style={styles.unreadIndicator} />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}

          {filteredNotifications.length === 0 && (
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateText}>
                {(() => {
                  if (activeFilter === "all") {
                    return t("notifications.empty", "Nenhuma notificação");
                  } else if (activeFilter === "read") {
                    return t(
                      "notifications.emptyRead",
                      "Nenhuma notificação lida"
                    );
                  } else {
                    return t(
                      "notifications.emptyUnread",
                      "Nenhuma notificação não lida"
                    );
                  }
                })()}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>
    </ScreenWithHeader>
  );
};

export default Notifications;
