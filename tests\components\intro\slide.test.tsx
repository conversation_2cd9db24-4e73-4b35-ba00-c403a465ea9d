import React from "react";
import { render } from "@testing-library/react-native";

// Mock expo-image Image component
jest.mock("expo-image", () => {
  const { View } = require("react-native");
  return {
    Image: (props: any) => <View {...props} testID="slide-image" />,
  };
});

import Slide from "../../../components/intro/slide";

describe("Slide component", () => {
  const dummyImage = { uri: "dummy" } as any;

  it("renders image with given source", () => {
    const { getByTestId } = render(
      <Slide id={1} image={dummyImage} />
    );

    expect(getByTestId("slide-image").props.source).toBe(dummyImage);
  });

  it("applies imageLeftPosition via contentPosition", () => {
    const { getByTestId } = render(
      <Slide id={1} image={dummyImage} imageLeftPosition={75} />
    );

    expect(getByTestId("slide-image").props.contentPosition.left).toBe("75%");
  });

  it("matches snapshot", () => {
    const { toJSON } = render(
      <Slide id={1} image={dummyImage} imageLeftPosition={10} />
    );
    expect(toJSON()).toMatchSnapshot();
  });
}); 