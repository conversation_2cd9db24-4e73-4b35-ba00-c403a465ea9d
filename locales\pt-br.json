{"intro": {"slide1": {"title": "Bem-vindo ao app do \nClub M Brasil.", "description": "Lorem ipsum mattis pulvinar pellentesque ultrices leo ac mattis consectetur elementum vitae posuere suscipit."}, "slide2": {"title": "Lorem ipsum dolor \nsit amet.", "description": "Lorem ipsum mattis pulvinar pellentesque ultrices leo ac mattis consectetur elementum vitae posuere suscipit."}, "slide3": {"title": "Lorem ipsum dolor \nsit amet.", "description": "Lorem ipsum mattis pulvinar pellentesque ultrices leo ac mattis consectetur elementum vitae posuere suscipit."}, "skipIntroButton": "Pular introdução", "loginButton": "<PERSON><PERSON> possuo uma conta", "skipIntroButtonLastCard": "Ir para Início"}, "login": {"title": "Bem-vindo!", "description": "É bom tê-lo conosco, insira seu CPF no campo abaixo para acessar o aplicativo.", "upsellDescription": "Insira as informações nos campos abaixo para identificarmos você.", "fields": {"document": "CPF", "documentPlaceholder": "Insira seu CPF", "password": "<PERSON><PERSON>", "passwordPlaceholder": "Insira sua senha", "fullName": "Nome completo", "fullNamePlaceholder": "Insira seu nome completo", "phone": "Telefone / WhatsApp", "phonePlaceholder": "Insira seu número de contato"}, "nextButton": "Prosseguir", "access": "Acessar", "register": {"title": "Ainda não possui cadastro?", "button": "Crie aqui."}, "forgotPassword": "<PERSON><PERSON><PERSON> <PERSON>ha senha", "goToStart": "Voltar ao início", "invalidDocument": "CPF inválido", "biometricPrompt": "Confirme que é você", "biometricFallback": "<PERSON><PERSON> se<PERSON>a", "invalidName": "Nome inválido", "invalidPhone": "Telefone inválido"}, "components": {"backButton": "Voltar", "inputField": {"characters": "caracteres"}}, "recoveryPassword": {"title": "<PERSON><PERSON><PERSON> <PERSON>ha senha", "description": "Enviamos o código de autenticação para o e-mail {{email}}", "securityCode": "Insira o código de segurança", "retrySendCodeLabel": "Não recebeu o código?", "retrySendCodeButton": "Reenviar código", "confirmButton": "Enviar código", "secondStepDescription": "Preencha os campos abaixo para redefinir sua senha.", "newPassword": "Nova senha", "repeatNewPassword": "<PERSON>etir nova senha", "passwordRequisition": "Obs.: <PERSON><PERSON> senha deve ter ao menos 8 caracteres.", "confirmNewPasswordButton": "<PERSON><PERSON><PERSON><PERSON>", "newPasswordPlaceholder": "Insira a nova senha", "repeatNewPasswordPlaceholder": "Insira a senha novamente"}, "recoveryPasswordSuccess": {"successTitle": "Senha redefinida com sucesso!", "successDescription": "Sua senha foi redefinida com sucesso.\nAgora, você pode acessar sua conta normalmente utilizando sua nova senha.", "successButton": "Ir para Início"}, "errors": {"invalidEmailFormat": "Formato de e-mail inválido", "requiredField": "Campo obrigatório", "unknownError": "<PERSON><PERSON>conhe<PERSON>", "invalidPasswordLength": "Senha deve ter no mínimo 8 caracteres", "invalidCodeSize": "<PERSON><PERSON><PERSON>", "invalidCpf": "CPF inválido", "emptyFields": "Erro ao acessar", "emptyFieldsDescription": "Preencha os campos abaixo para prosseguir com o acesso ao aplicativo", "forgetPasswordDescription": "Preencha o email para poder dar continuidade na recuperação de senha", "tryLater": "Ocorreu um erro desconhecido, por favor tente novamente mais tarde", "authenticationError": "Erro de autenticação", "authenticationErrorDescription": "Insira o código no campo abaixo para prosseguir com a redefinição da senha.", "passwordNotMatch": "As senhas não são identicas.", "passwordNotSecure": "Senha não cumpre requesitos de segurança", "passwordNotSecureDescription": "Sua senha não cumpre os requesitos minimos de segurança, para continuar forneça uma nova senha", "passwordMinimumLength": "Senha deve ter ao menos 8 caracteres.", "failedToRegisterFirebase": "Falha grave no aplicativo", "failedToRegisterFirebaseDescription": "Consulte o suporte para mais de<PERSON>hes"}, "tabBar": {"home": "Home", "schedule": "Agenda", "products": "<PERSON><PERSON><PERSON>", "profile": "<PERSON><PERSON>", "messages": "Mensagens"}, "schedule": {"title": "Agenda", "options": {"calendar": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Você tem", "endSubtitle": "agendados.", "event": "eventos"}, "tickets": {"title": "<PERSON><PERSON> ingressos", "subtitle": "Você tem", "endSubtitle": "reservados.", "event": "ingressos"}, "eventList": {"title": "Lista de eventos", "subtitle": "Confira os eventos que estão acontecendo próximo de você."}, "createEventButton": "Criar novo evento"}}, "calendar": {"title": "<PERSON><PERSON><PERSON><PERSON>", "yearModal": {"title": "Selecionar ano", "confirmButton": "Selecionar ano", "closeButton": "<PERSON><PERSON><PERSON>"}, "eventsToday": "Eventos agendados do dia", "searchTextFieldPlaceholder": "Buscar nome do evento, etc...", "eventTypes": {"yourAndPrivate": "Seu evento (Privado)", "free": "EVENTO GRATUITO"}, "eventPresence": {"iWillGo": "ESTAREI LÁ"}}, "products": {"title": "<PERSON><PERSON><PERSON>", "priceLabel": "Por:", "openProductButton": "VER PRODUTO", "detailsTitle": "Detalhes do produto", "categories": {"title": "Categorias", "ebooks": "E-books", "courses": "Cursos", "meetings": "Reuniões", "groupMeetings": "Encontros", "mentoring": "Mentorias", "services": "Serviços"}, "partners": {"title": "Parceiros e benefícios"}}, "productPage": {"seeMore": "Ver mais", "seeLess": "<PERSON>er menos", "installments": "(parcele em até 12x sem juros).", "similarProducts": "<PERSON><PERSON><PERSON>"}, "eventList": {"title": "Lista de eventos", "category": "Categorias", "categories": {"business": "<PERSON>eg<PERSON><PERSON><PERSON>", "startups": "Startups", "technology": "Tecnologia", "marketing": "Marketing", "sustainability": "Sustentabilidade"}, "sponsoredEvents": "Eventos patrocinados", "badges": {"freeEvent": "EVENTO GRATUITO", "business": "NEGÓCIOS"}, "viewEvent": "VER EVENTO", "from": "Por:", "price": "R$ {{price}}"}, "recommendedProducts": {"loadingMore": "Carregando mais produtos...", "loadingProducts": "Carregando produtos...", "noProducts": "Nenhum produto encontrado", "recommended": "Recomendados para você", "seeAll": "Ver todos", "searchPlaceholder": "Digite o nome do produto"}, "acquireButton": {"acquire": "Adquira por:"}, "user": {"opportunities": "Oportunidades", "claimedBadges": "<PERSON><PERSON>", "aboutMe": "Sobre mim", "publishedOpportunity": "publicou uma nova oportunidade de negócio em", "investmentValue": "Valor de investimento", "price": "R$ {{price}}", "seeDetals": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "seeMore": "Ver mais", "seeLess": "<PERSON>er menos", "editInfo": "<PERSON><PERSON> dad<PERSON>", "generalSettings": "<PERSON><PERSON><PERSON><PERSON> gera<PERSON>", "seals": "Selos Club M"}, "objectives": {"redeem": "Resgatar", "title": "Objetivos diários", "seeMore": "Ver mais"}, "myTickets": {"title": "<PERSON><PERSON> ingressos", "year": "2025", "reservedTickets": "Ingressos reservados ({{count}})", "searchPlaceholder": "Buscar nome do evento, etc...", "eventDate": "Data/horário do evento", "ticketCode": "Código do ingresso", "organizedBy": "Sediado por:", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "messages": {"title": "Mensagens", "pinned": "Fixado"}, "chat": {"today": "Hoje", "yesterday": "Ontem", "messagePlaceholder": "Digite sua mensagem"}, "createEvent": {"title": "Criar evento", "private": "Privado", "fields": {"eventTitle": {"label": "Título do evento", "placeholder": "Insira o título do evento"}, "description": {"label": "Descrição", "placeholder": "Insira uma descrição..."}, "dateTime": {"label": "Data/horário do evento", "placeholder": "Selecionar data e horário do evento"}, "eventType": {"label": "Tipo de evento", "placeholder": "Selecione o tipo de evento"}, "inviteMembers": {"label": "<PERSON><PERSON><PERSON> memb<PERSON> (opcional)", "placeholder": "Selecione os membros"}}, "createdBy": "Criado por:", "createdByName": "Maria Aparecida", "visibleToPublic": "Visível ao público", "additionalInfoButton": "Inserir informações adicionais", "buttons": {"publish": "Publicar evento", "cancel": "<PERSON><PERSON><PERSON>"}}}