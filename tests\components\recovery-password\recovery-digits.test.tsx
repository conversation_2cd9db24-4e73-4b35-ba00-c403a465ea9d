import React from "react";
import { render, fireEvent } from "@testing-library/react-native";

jest.mock("../../../styles/components/recovery-password/recovery-digits.style", () => ({ container: {}, hyphen: {} }));

jest.mock("../../../components/recovery-password/mega-digit", () => {
  const { TextInput } = require("react-native");
  const {forwardRef} = require("react");
  return forwardRef((props: any, ref: any) => (
    <TextInput
      placeholder="0"
      testID="digit"
      value={props.value}
      onChangeText={props.onChange}
      ref={ref}
    />
  ));
});

import RecoveryDigits from "../../../components/recovery-password/recovery-digits";

describe("RecoveryDigits", () => {
  it("renders six digits and hyphen", () => {
    const { getAllByTestId, getByText } = render(<RecoveryDigits value="" onChange={jest.fn()} />);
    expect(getAllByTestId("digit").length).toBe(6);
    expect(getByText("-")).toBeTruthy();
  });

  it("concatenates value on digit change", () => {
    const onChange = jest.fn();
    const { getAllByTestId } = render(<RecoveryDigits value="" onChange={onChange} />);
    fireEvent.changeText(getAllByTestId("digit")[0], "9");
    expect(onChange).toHaveBeenCalledWith("9");
  });
});
