import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    flex: 1,
    paddingTop: 8
  },
  tabContainer: {
    flexDirection: "row",
    gap: 16,
    paddingLeft: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#667085"
  },
  tab: {
    paddingHorizontal: 4,
    paddingVertical: 1,
    paddingTop: 1,
    minHeight: 32,
    justifyContent: "center",
    alignItems: "flex-start",
    borderBottomWidth: 2,
    borderBottomColor: "transparent"
  },
  activeTab: {
    borderBottomColor: "#FCFCFD"
  },
  tabText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  activeTabText: {
    fontWeight: "700"
  },
  monthLabel: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    marginTop: 12,
    marginLeft: 24,
    marginRight: 24,
    minHeight: 18
  },
  purchaseItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    marginBottom: 4,
    marginLeft: 24,
    marginRight: 24,
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  purchaseIcon: {
    width: 32,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 2,
    marginBottom: 2
  },
  purchaseContent: {
    flex: 1,
    justifyContent: "center",
    minHeight: 36
  },
  purchaseTitle: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    lineHeight: 18,
    textOverflow: "ellipsis"
  },
  purchaseDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.8,
    flex: 1,
    marginRight: 8
  },
  purchaseRight: {
    gap: 2,
    flexDirection: "column",
    alignItems: "stretch",
    minWidth: 52
  },
  purchaseStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginBottom: 6
  },
  statusText: {
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 18,
    minHeight: 18,
    textOverflow: "ellipsis"
  },
  purchasePrice: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 16,
    minHeight: 16
  },
  cancelledPrice: {
    textDecorationLine: "line-through"
  },
  // Status colors
  pendingStatus: {
    backgroundColor: "transparent"
  },
  pendingText: {
    color: "#FDB022"
  },
  completedStatus: {
    backgroundColor: "transparent"
  },
  completedText: {
    color: "#47CD89"
  },
  cancelledStatus: {
    backgroundColor: "transparent"
  },
  cancelledText: {
    color: "#F97066"
  },
  // Detail Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end"
  },
  modalContent: {
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40,
    maxHeight: "90%"
  },
  modalHeader: {
    alignItems: "center",
    marginBottom: 24
  },
  modalIndicator: {
    width: 40,
    height: 4,
    backgroundColor: stylesConstants.colors.textPrimary,
    borderRadius: 2,
    opacity: 0.3,
    marginBottom: 20
  },
  modalTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 28
  },
  detailSection: {
    marginBottom: 24
  },
  detailSectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    marginBottom: 16
  },
  detailItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.08)"
  },
  detailItemLast: {
    borderBottomWidth: 0
  },
  detailLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.8
  },
  detailValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20,
    textAlign: "right",
    flex: 1,
    marginLeft: 16
  },
  closeButton: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 24
  },
  closeButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
