import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16
  },
  tabContainer: {
    flexDirection: "row",
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)"
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent"
  },
  activeTab: {
    borderBottomColor: stylesConstants.colors.fullWhite
  },
  tabText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.7
  },
  activeTabText: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: "600",
    opacity: 1
  },
  monthLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 13,
    fontWeight: "400",
    lineHeight: 18,
    marginBottom: 12,
    marginTop: 20,
    opacity: 0.7
  },
  purchaseItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center"
  },
  purchaseIcon: {
    width: 36,
    height: 36,
    borderRadius: 6,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12
  },
  purchaseContent: {
    flex: 1
  },
  purchaseTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 22,
    flex: 1,
    marginRight: 8
  },
  purchaseDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.8,
    flex: 1,
    marginRight: 8
  },
  purchaseRight: {
    alignItems: "flex-end",
    justifyContent: "flex-start"
  },
  purchaseStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginBottom: 6
  },
  statusText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 16
  },
  purchasePrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 22
  },
  // Status colors
  pendingStatus: {
    backgroundColor: "#FFA500"
  },
  pendingText: {
    color: stylesConstants.colors.fullBlack,
    fontWeight: "600"
  },
  completedStatus: {
    backgroundColor: "#00C851"
  },
  completedText: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: "600"
  },
  cancelledStatus: {
    backgroundColor: "#FF4444"
  },
  cancelledText: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: "600"
  },
  // Detail Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end"
  },
  modalContent: {
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40,
    maxHeight: "90%"
  },
  modalHeader: {
    alignItems: "center",
    marginBottom: 24
  },
  modalIndicator: {
    width: 40,
    height: 4,
    backgroundColor: stylesConstants.colors.textPrimary,
    borderRadius: 2,
    opacity: 0.3,
    marginBottom: 20
  },
  modalTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 28
  },
  detailSection: {
    marginBottom: 24
  },
  detailSectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    marginBottom: 16
  },
  detailItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.08)"
  },
  detailItemLast: {
    borderBottomWidth: 0
  },
  detailLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.8
  },
  detailValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20,
    textAlign: "right",
    flex: 1,
    marginLeft: 16
  },
  closeButton: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 24
  },
  closeButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
