import React from "react";
import {Text, View, ScrollView} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import FullSizeButton from "../../components/full-size-button";
import {router, useLocalSearchParams} from "expo-router";
import styles from "../../styles/business/payment-review.style";
import PixIcon from "../../components/icons/payment-methods/pix-icon";
import VisaIcon from "../../components/icons/payment-methods/visa-icon";
import MastercardIcon from "../../components/icons/payment-methods/mastercard-icon";

// Boleto icon component
const BoletoIcon: React.FC<{width?: number; height?: number}> = ({
  width = 16,
  height = 16
}) => <Text style={{fontSize: width, color: "#FFA500"}}>📄</Text>;

// Icon mapping for payment methods
const paymentIconMap = {
  pix: PixIcon,
  boleto: BoletoIcon,
  mastercard: MastercardIcon,
  visa: VisaIcon
};

const PaymentReview: React.FC = () => {
  const params = useLocalSearchParams();

  let paymentData = null;

  if (params.paymentData && typeof params.paymentData === "string") {
    try {
      // First decode the URI component
      const decodedData = decodeURIComponent(params.paymentData);
      console.log("Decoded data:", decodedData);

      // Then parse the JSON
      paymentData = JSON.parse(decodedData);
      console.log("Parsed payment data:", paymentData);
    } catch (error) {
      console.error("Error parsing payment data:", error);
      console.error("Raw params.paymentData:", params.paymentData);

      // Fallback data for testing
      paymentData = {
        paymentMethod: {type: "pix", name: "PIX"},
        opportunityData: {},
        subtotal: 150,
        total: 150,
        opportunityValue: 150
      };
    }
  } else {
    // No payment data provided, use fallback
    paymentData = {
      paymentMethod: {type: "pix", name: "PIX"},
      opportunityData: {},
      subtotal: 150,
      total: 150,
      opportunityValue: 150
    };
  }

  if (!paymentData) {
    console.error("No payment data available, redirecting back");
    router.back();
    return null;
  }

  console.log("Final payment data:", paymentData);

  const {paymentMethod, opportunityData, subtotal, total, opportunityValue} =
    paymentData;

  // Verificações de segurança
  if (!paymentMethod?.type) {
    router.back();
    return null;
  }

  const getPaymentMethodInfo = () => {
    const safeTotal = total ?? 150;
    switch (paymentMethod.type) {
      case "pix":
        return {
          method: "PIX",
          status: "À Vista",
          totalValue: `R$ ${safeTotal.toFixed(2).replace(".", ",")}`
        };
      case "boleto":
        return {
          method: "Boleto",
          status: "À Vista",
          totalValue: `R$ ${safeTotal.toFixed(2).replace(".", ",")}`
        };
      case "mastercard":
      case "visa":
        return {
          method: `Cartão de crédito`,
          status: "À Vista",
          totalValue: `R$ ${safeTotal.toFixed(2).replace(".", ",")}`
        };
      default:
        return {
          method: paymentMethod.name ?? "Método de pagamento",
          status: "À Vista",
          totalValue: `R$ ${safeTotal.toFixed(2).replace(".", ",")}`
        };
    }
  };

  const selectedPaymentMethod = getPaymentMethodInfo();

  const handleGenerateCode = () => {
    console.log("Generating code for payment method:", paymentMethod.type);

    const paymentParams = {
      paymentType: paymentMethod.type,
      totalValue: (total ?? 150).toString(),
      opportunityData: JSON.stringify(opportunityData ?? {})
    };

    try {
      if (paymentMethod.type === "boleto") {
        router.push(
          `/(business)/boleto-payment?${new URLSearchParams(
            paymentParams
          ).toString()}`
        );
      } else if (
        paymentMethod.type === "mastercard" ||
        paymentMethod.type === "visa"
      ) {
        router.push(
          `/(business)/credit-card-form?${new URLSearchParams(
            paymentParams
          ).toString()}`
        );
      } else {
        // Default to PIX payment
        router.push(
          `/(business)/pix-payment?${new URLSearchParams(
            paymentParams
          ).toString()}`
        );
      }
    } catch (error) {
      console.error("Error navigating to payment screen:", error);
    }
  };

  return (
    <ScreenWithHeader screenTitle="Revisar pagamento" backButton>
      <ScrollView style={styles.contentContainer}>
        {/* Debug Info */}
        <View
          style={{
            backgroundColor: "rgba(255,255,255,0.1)",
            padding: 10,
            marginBottom: 20
          }}
        >
          <Text style={{color: "white", fontSize: 12}}>
            Debug: Payment Method Type: {paymentMethod?.type || "undefined"}
          </Text>
          <Text style={{color: "white", fontSize: 12}}>
            Debug: Total Value: {total ?? "undefined"}
          </Text>
        </View>

        {/* Header */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>Lista de produtos</Text>
        </View>

        {/* Product List */}
        <View style={styles.sectionContainer}>
          <View style={styles.productContainer}>
            <View style={styles.productIcon}>
              <Text style={styles.productIconText}>📢</Text>
            </View>
            <View style={styles.productInfo}>
              <Text style={styles.productName}>Publicação de oportunidade</Text>
              <Text style={styles.productDescription}>
                Oportunidade de negócio
              </Text>
            </View>
            <Text style={styles.productPrice}>
              R$ {(opportunityValue ?? 150).toFixed(2).replace(".", ",")}
            </Text>
          </View>
        </View>

        {/* Payment Method */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Método de pagamento</Text>
          <View style={styles.paymentMethodContainer}>
            <View style={styles.paymentMethodIcon}>
              {(() => {
                const IconComponent =
                  paymentIconMap[
                    paymentMethod.type as keyof typeof paymentIconMap
                  ];
                if (IconComponent) {
                  return <IconComponent width={24} height={24} />;
                } else {
                  // Fallback icon
                  return <Text style={{fontSize: 20}}>💳</Text>;
                }
              })()}
            </View>
            <View style={styles.paymentMethodInfo}>
              <Text style={styles.paymentMethodName}>
                {selectedPaymentMethod.method}
              </Text>
              <Text style={styles.paymentMethodStatus}>
                {selectedPaymentMethod.status}
              </Text>
            </View>
          </View>
        </View>

        {/* Order Summary */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Resumo da compra</Text>
          <View style={styles.orderSummaryContainer}>
            <View style={styles.orderItem}>
              <Text style={styles.orderItemName}>Subtotal (1 item)</Text>
              <Text style={styles.orderItemPrice}>
                R$ {(subtotal ?? 150).toFixed(2).replace(".", ",")}
              </Text>
            </View>

            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>
                {selectedPaymentMethod.totalValue}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <FullSizeButton
            text={`Gerar código de pagamento ${selectedPaymentMethod.method}`}
            onPress={handleGenerateCode}
          />
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PaymentReview;
