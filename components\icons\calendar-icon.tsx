import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface CalendarIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const CalendarIcon: React.FC<CalendarIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#fff", [props.replaceColor]);

    return (
        <Svg
            width={25}
            height={24}
            fill="none"
            viewBox="0 0 25 24"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21.7 10h-18m13-8v4m-8-4v4m-.2 16h8.4c1.68 0 2.52 0 3.162-.327a3 3 0 0 0 1.311-1.311c.327-.642.327-1.482.327-3.162V8.8c0-1.68 0-2.52-.327-3.162a3 3 0 0 0-1.311-1.311C19.42 4 18.58 4 16.9 4H8.5c-1.68 0-2.52 0-3.162.327a3 3 0 0 0-1.311 1.311C3.7 6.28 3.7 7.12 3.7 8.8v8.4c0 1.68 0 2.52.327 3.162a3 3 0 0 0 1.311 1.311C5.98 22 6.82 22 8.5 22Z"
            />
        </Svg>
    );
};

export default CalendarIcon;