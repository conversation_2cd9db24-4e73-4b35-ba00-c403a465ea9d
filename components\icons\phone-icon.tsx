import React, {useMemo} from "react";
import Svg, {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, G, Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface PhoneIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const PhoneIcon: React.FC<PhoneIconProps> = (props) => {
    const color = useMemo(
        () => props.replaceColor ?? "#F2F4F7",
        [props.replaceColor]
    );

    return (
        <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
            <G clipPath="url(#a)">
                <Path
                    stroke={color}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11.708 5A4.166 4.166 0 0 1 15 8.292m-3.292-6.625a7.5 7.5 0 0 1 6.625 6.616m-9.81 3.27A12.168 12.168 0 0 1 6.15 8.21a1.415 1.415 0 0 1-.094-.222.872.872 0 0 1 .123-.718c.04-.056.087-.103.183-.199.29-.29.437-.437.532-.583.359-.552.359-1.265 0-1.817-.095-.146-.241-.292-.532-.583l-.163-.163c-.443-.443-.664-.664-.902-.784a1.667 1.667 0 0 0-1.504 0c-.238.12-.46.341-.903.784l-.13.131c-.442.442-.663.662-.831.963-.188.332-.322.85-.32 1.231 0 .344.067.58.2 1.05a15.866 15.866 0 0 0 4.062 6.903 15.865 15.865 0 0 0 6.903 4.061c.47.134.705.2 1.05.202.381 0 .898-.134 1.231-.32.3-.17.521-.39.962-.831l.132-.132c.443-.443.664-.664.784-.902.24-.473.24-1.031 0-1.504-.12-.238-.341-.46-.784-.902l-.163-.163c-.291-.291-.437-.437-.583-.532a1.667 1.667 0 0 0-1.817 0c-.147.095-.292.24-.584.532a1.685 1.685 0 0 1-.198.183.872.872 0 0 1-.719.122 1.419 1.419 0 0 1-.221-.094 12.17 12.17 0 0 1-3.342-2.371Z"
                />
            </G>
            <Defs>
                <ClipPath id="a">
                    <Path fill="#fff" d="M0 0h20v20H0z" />
                </ClipPath>
            </Defs>
        </Svg>
    );
};

export default PhoneIcon;
