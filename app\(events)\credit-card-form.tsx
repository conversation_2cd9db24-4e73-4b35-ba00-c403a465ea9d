import React, {useState, useEffect} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import {router, useLocalSearchParams} from "expo-router";
import styles from "../../styles/events/credit-card-form.style";
import {
  CreditCardData,
  maskCardNumber,
  maskExpiryDate,
  maskCVV,
  getCardBrand,
  validateCreditCard,
  generateInstallmentOptions,
  formatCardNumberForDisplay
} from "../../utils/credit-card";

const CreditCardForm: React.FC = () => {
  const {t} = useTranslation();
  const params = useLocalSearchParams();
  const paymentType = (params.paymentType as string) || "mastercard";

  // Dados simulados do carrinho
  const totalValue = 2780.0;

  const [cardData, setCardData] = useState<CreditCardData>({
    number: "",
    expiryDate: "",
    cvv: "",
    holderName: "",
    installments: 1
  });

  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [selectedInstallment, setSelectedInstallment] = useState(1);

  const installmentOptions = generateInstallmentOptions(totalValue, 12);
  const cardBrand = getCardBrand(cardData.number);

  const getCardBrandIcon = (brand: string) => {
    switch (brand) {
      case "visa":
        return "💳";
      case "mastercard":
        return "💳";
      case "amex":
        return "💳";
      default:
        return "💳";
    }
  };

  const handleInputChange = (field: keyof CreditCardData, value: string) => {
    let formattedValue = value;

    switch (field) {
      case "number":
        formattedValue = maskCardNumber(value);
        break;
      case "expiryDate":
        formattedValue = maskExpiryDate(value);
        break;
      case "cvv":
        formattedValue = maskCVV(value, cardData.number);
        break;
      case "holderName":
        formattedValue = value.toUpperCase();
        break;
    }

    setCardData((prev) => ({
      ...prev,
      [field]: formattedValue,
      installments: selectedInstallment
    }));

    // Limpa erro do campo quando usuário começa a digitar
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  const handleInstallmentSelect = (installments: number) => {
    setSelectedInstallment(installments);
    setCardData((prev) => ({
      ...prev,
      installments
    }));
  };

  const validateForm = () => {
    const validation = validateCreditCard(cardData);

    if (!validation.isValid) {
      const fieldErrors: {[key: string]: string} = {};
      validation.errors.forEach((error) => {
        if (error.includes("Número")) fieldErrors.number = error;
        if (error.includes("Data")) fieldErrors.expiryDate = error;
        if (error.includes("CVV")) fieldErrors.cvv = error;
        if (error.includes("Nome")) fieldErrors.holderName = error;
        if (error.includes("parcelas")) fieldErrors.installments = error;
      });

      setErrors(fieldErrors);
      return false;
    }

    return true;
  };

  const handleContinue = () => {
    if (validateForm()) {
      // Navegar para tela de confirmação
      const cardDataString = encodeURIComponent(
        JSON.stringify({
          ...cardData,
          brand: cardBrand,
          totalValue,
          installmentValue:
            installmentOptions.find(
              (opt) => opt.installments === selectedInstallment
            )?.value || totalValue
        })
      );

      router.push(
        `/(events)/credit-card-confirmation?cardData=${cardDataString}&paymentType=${paymentType}`
      );
    } else {
      Alert.alert(
        "Dados inválidos",
        "Por favor, verifique os dados do cartão e tente novamente."
      );
    }
  };

  const renderCardPreview = () => (
    <View style={styles.cardPreview}>
      <View>
        <Text style={styles.cardNumber}>
          {cardData.number || "**** **** **** ****"}
        </Text>
        <Text style={styles.cardBrand}>
          {cardBrand !== "unknown" ? cardBrand.toUpperCase() : "CARTÃO"}
        </Text>
      </View>

      <View style={styles.cardInfo}>
        <View>
          <Text style={styles.cardHolder}>
            {cardData.holderName || "NOME DO PORTADOR"}
          </Text>
        </View>
        <Text style={styles.cardExpiry}>{cardData.expiryDate || "MM/AA"}</Text>
      </View>
    </View>
  );

  return (
    <ScreenWithHeader screenTitle="Dados do cartão de crédito" backButton>
      <ScrollView
        style={{flex: 1, paddingHorizontal: 24}}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>Insira os dados do seu cartão</Text>
          <Text style={styles.subtitle}>
            Preencha as informações para finalizar o pagamento
          </Text>
        </View>

        {/* Preview do cartão */}
        {renderCardPreview()}

        {/* Formulário */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Dados do cartão</Text>

          {/* Número do cartão */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Número do cartão</Text>
            <View style={{position: "relative"}}>
              <TextInput
                style={[
                  styles.textInput,
                  focusedField === "number" && styles.textInputFocused,
                  errors.number && styles.textInputError
                ]}
                value={cardData.number}
                onChangeText={(value) => handleInputChange("number", value)}
                onFocus={() => setFocusedField("number")}
                onBlur={() => setFocusedField(null)}
                placeholder="0000 0000 0000 0000"
                placeholderTextColor="#666"
                keyboardType="numeric"
                maxLength={19}
              />
              {cardBrand !== "unknown" && (
                <View style={styles.cardBrandContainer}>
                  <Text style={styles.cardBrandText}>
                    {getCardBrandIcon(cardBrand)}
                  </Text>
                </View>
              )}
            </View>
            {errors.number && (
              <Text style={styles.errorText}>{errors.number}</Text>
            )}
          </View>

          {/* Data de validade e CVV */}
          <View style={styles.inputRow}>
            <View style={[styles.inputGroup, styles.inputHalf]}>
              <Text style={styles.inputLabel}>Validade</Text>
              <TextInput
                style={[
                  styles.textInput,
                  focusedField === "expiryDate" && styles.textInputFocused,
                  errors.expiryDate && styles.textInputError
                ]}
                value={cardData.expiryDate}
                onChangeText={(value) => handleInputChange("expiryDate", value)}
                onFocus={() => setFocusedField("expiryDate")}
                onBlur={() => setFocusedField(null)}
                placeholder="MM/AA"
                placeholderTextColor="#666"
                keyboardType="numeric"
                maxLength={5}
              />
              {errors.expiryDate && (
                <Text style={styles.errorText}>{errors.expiryDate}</Text>
              )}
            </View>

            <View style={[styles.inputGroup, styles.inputHalf]}>
              <Text style={styles.inputLabel}>CVV</Text>
              <TextInput
                style={[
                  styles.textInput,
                  focusedField === "cvv" && styles.textInputFocused,
                  errors.cvv && styles.textInputError
                ]}
                value={cardData.cvv}
                onChangeText={(value) => handleInputChange("cvv", value)}
                onFocus={() => setFocusedField("cvv")}
                onBlur={() => setFocusedField(null)}
                placeholder="000"
                placeholderTextColor="#666"
                keyboardType="numeric"
                maxLength={cardBrand === "amex" ? 4 : 3}
                secureTextEntry
              />
              {errors.cvv && <Text style={styles.errorText}>{errors.cvv}</Text>}
            </View>
          </View>

          {/* Nome do portador */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Nome do portador</Text>
            <TextInput
              style={[
                styles.textInput,
                focusedField === "holderName" && styles.textInputFocused,
                errors.holderName && styles.textInputError
              ]}
              value={cardData.holderName}
              onChangeText={(value) => handleInputChange("holderName", value)}
              onFocus={() => setFocusedField("holderName")}
              onBlur={() => setFocusedField(null)}
              placeholder="Nome como está no cartão"
              placeholderTextColor="#666"
              autoCapitalize="characters"
            />
            {errors.holderName && (
              <Text style={styles.errorText}>{errors.holderName}</Text>
            )}
          </View>
        </View>

        {/* Opções de parcelamento */}
        <View style={styles.installmentsContainer}>
          <Text style={styles.sectionTitle}>Parcelamento</Text>

          {installmentOptions.slice(0, 3).map((option) => (
            <TouchableOpacity
              key={option.installments}
              style={[
                styles.installmentOption,
                selectedInstallment === option.installments &&
                  styles.installmentOptionSelected
              ]}
              onPress={() => handleInstallmentSelect(option.installments)}
            >
              <View
                style={[
                  styles.radioButton,
                  selectedInstallment === option.installments &&
                    styles.radioButtonSelected
                ]}
              >
                {selectedInstallment === option.installments && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>

              <Text style={styles.installmentOptionText}>{option.label}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Informação de segurança */}
        <View style={styles.securityInfo}>
          <Text style={styles.securityIcon}>🔒</Text>
          <Text style={styles.securityText}>
            Seus dados estão protegidos com criptografia de ponta a ponta
          </Text>
        </View>

        <FullSizeButton text="Continuar" onPress={handleContinue} />
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default CreditCardForm;
