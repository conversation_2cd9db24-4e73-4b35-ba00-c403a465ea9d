import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20
  },
  searchContainer: {
    marginBottom: 24,
    paddingHorizontal: 20
  },
  searchInputContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)"
  },
  searchInput: {
    flex: 1,
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    marginLeft: 12
  },
  carouselContainer: {
    marginBottom: 24
  },
  carouselContent: {
    paddingHorizontal: 20,
    gap: 16
  },
  carouselCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    width: 280,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "center"
  },
  carouselIcon: {
    fontSize: 32,
    marginBottom: 12
  },
  carouselTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 8
  },
  carouselDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    textAlign: "center"
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 4,
    marginHorizontal: 20,
    marginBottom: 16
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: "center"
  },
  activeTab: {
    backgroundColor: stylesConstants.colors.mainBackground
  },
  tabText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 500,
    lineHeight: 20
  },
  activeTabText: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: 600
  },
  tabContent: {
    flex: 1,
    paddingHorizontal: 20
  },
  sectionContainer: {
    marginBottom: 32
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: 600,
    lineHeight: 28,
    marginBottom: 16,
    paddingHorizontal: 20
  },
  sectionDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginBottom: 24
  },
  optionsContainer: {
    gap: 10
  },
  optionItem: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 15,
    flexDirection: "row",
    alignItems: "center"
  },
  optionIcon: {
    fontSize: 24,
    marginRight: 15
  },
  optionContent: {
    flex: 1
  },
  optionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 4
  },
  optionDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  optionChevron: {
    color: "#007AFF",
    fontSize: 16
  },
  formContainer: {
    gap: 15
  },
  categorySelector: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 15,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  categoryContent: {
    flex: 1
  },
  categoryTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24
  },
  categoryValue: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  messageContainer: {
    marginBottom: 15
  },
  messageLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 10
  },
  messageInputContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 15,
    minHeight: 120
  },
  characterCount: {
    color: "#666",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16,
    marginTop: 5,
    textAlign: "right"
  },
  infoContainer: {
    backgroundColor: "rgba(0, 122, 255, 0.1)",
    borderColor: "#007AFF",
    borderWidth: 1,
    borderRadius: 12,
    padding: 15,
    marginBottom: 30
  },
  infoTitle: {
    color: "#007AFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24,
    marginBottom: 8
  },
  infoDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  systemInfoContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 12,
    padding: 15,
    marginBottom: 30
  },
  systemInfoTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24,
    marginBottom: 10
  },
  systemInfoList: {
    gap: 5
  },
  systemInfoItem: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  attachFileButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
    borderStyle: "dashed",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    marginBottom: 16
  },
  attachFileText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  contactList: {
    gap: 16,
    marginTop: 16
  },
  contactItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)"
  },
  contactHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12
  },
  contactIcon: {
    width: 40,
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12
  },
  contactIconText: {
    fontSize: 18
  },
  contactInfo: {
    flex: 1
  },
  contactName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24
  },
  contactLocation: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  contactAction: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.1)",
    marginTop: 8
  },
  contactActionIcon: {
    fontSize: 16,
    marginRight: 12,
    width: 20,
    textAlign: "center"
  },
  contactActionText: {
    flex: 1,
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  }
});

export default styles;
