import React from "react";
import Svg, {SvgProps, Path, Rect} from "react-native-svg";

const ApplePayIcon: React.FC<SvgProps> = (props) => {
    const width = props.width ?? 34;
    const height = props.height ?? 24;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 34 24"
            fill="none"
            {...props}
        >
            <Rect width={33} height={23} x={0.5} y={0.5} fill="#fff" rx={3.5} />
            <Rect
                width={33}
                height={23}
                x={0.5}
                y={0.5}
                stroke="#F2F4F7"
                rx={3.5}
            />
            <Path
                fill="#000"
                d="m25.445 9.71 1.356 4.565h.023l1.356-4.564h1.17l-2.022 5.908c-.461 1.353-.989 1.799-2.111 1.799-.086 0-.37-.01-.438-.03v-.926c.072.01.247.02.337.02.509 0 .795-.224.97-.803l.105-.342-1.95-5.626h1.204ZM9.863 9.028c.257.02 1 .1 1.475.832-.038.03-.88.536-.871 1.597.01 1.263 1.056 1.686 1.075 1.7-.01.029-.167.6-.552 1.184-.338.515-.685 1.021-1.236 1.031-.533.01-.71-.332-1.322-.332-.61 0-.81.322-1.314.342-.532.02-.937-.545-1.274-1.06-.69-1.041-1.218-2.935-.504-4.214.347-.639.98-1.04 1.66-1.05.523-.01 1.004.362 1.322.362.314 0 .88-.431 1.541-.392Zm11.758.61c1.317 0 2.174.719 2.174 1.834v3.845h-1.066v-.926h-.024c-.304.604-.975.986-1.698.986-1.07 0-1.817-.664-1.817-1.665 0-.991.723-1.561 2.06-1.646l1.436-.088v-.427c0-.63-.395-.972-1.098-.972-.58 0-1.004.313-1.09.788H19.46c.033-1 .938-1.73 2.16-1.73ZM16.465 7.59c1.436 0 2.44 1.03 2.44 2.537s-1.023 2.548-2.478 2.548h-1.594v2.641h-1.15V7.59h2.782Zm4.941 5.278c-.718.045-1.093.327-1.094.813 0 .47.39.778 1.004.778.78 0 1.37-.516 1.37-1.24v-.435l-1.28.084Zm-6.573-1.2h1.322c1.004 0 1.574-.56 1.574-1.535 0-.977-.57-1.532-1.569-1.532h-1.327v3.068ZM9.868 7c.048.495-.138.981-.419 1.343-.285.352-.742.63-1.198.59-.057-.476.166-.981.428-1.293.285-.362.785-.62 1.189-.64Z"
            />
        </Svg>
    );
};

export default ApplePayIcon;
