import {firstValueFrom, Observable} from "rxjs";
import {PaginationResponse} from "../models/pagination";

export function createInfinityQueryOptions<T>(queryKey: unknown[],
                                              service: (page: number, pageSize: number) => Observable<PaginationResponse<T>>,
                                              initialPage: number = 1,
                                              pageSize: number = 10) {
    return {
        queryKey: queryKey,
        initialPageParam: initialPage,
        queryFn: async ({pageParam = 1}) => await firstValueFrom(service(pageParam, pageSize)),
        getNextPageParam: (lastPage: PaginationResponse<T>) => {
            if(lastPage.hasNextPage) {
                return lastPage.page + 1
            }

            return undefined;
        },
        getPreviousPageParam: (firstPage: PaginationResponse<T>) => {
            if(firstPage.page > 1) {
                return firstPage.page - 1;
            }

            return undefined;
        }
    };
}