import React, {useState, useCallback} from "react";
import {Text, View, ColorValue} from "react-native";
import Screen from "@/components/screen";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import BackButton from "@/components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "@/components/full-size-button";
import InputField from "@/components/input-field";
import PasswordIcon from "@/components/icons/password-icon";

const RecoveryPasswordNew: React.FC = () => {
  const {t} = useTranslation();
  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({...prev, [field]: ""}));
      }
    };

  const handleSubmit = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.newPassword) {
      newErrors.newPassword = t(
        "recoveryPasswordNew.errors.required",
        "Campo obrigatório"
      );
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = t(
        "recoveryPasswordNew.errors.minLength",
        "Mínimo 6 caracteres"
      );
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t(
        "recoveryPasswordNew.errors.required",
        "Campo obrigatório"
      );
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = t(
        "recoveryPasswordNew.errors.mismatch",
        "Senhas não coincidem"
      );
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Handle password reset
    console.log("Reset password with:", formData.newPassword);
  };

  const passwordIcon = useCallback(
    (errorColor?: ColorValue) => <PasswordIcon replaceColor={errorColor} />,
    []
  );

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={{flex: 1, padding: 20}}>
          <BackButton />
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#fff",
              textAlign: "center",
              marginBottom: 20
            }}
          >
            {t("recoveryPasswordNew.title", "Nova Senha")}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "#fff",
              textAlign: "center",
              marginBottom: 30
            }}
          >
            {t(
              "recoveryPasswordNew.description",
              "Crie uma nova senha para sua conta"
            )}
          </Text>

          <View style={{gap: 15, marginBottom: 30}}>
            <InputField
              label={t("recoveryPasswordNew.newPassword", "Nova Senha")}
              value={formData.newPassword}
              onChangeText={handleInputChange("newPassword")}
              placeholder={t(
                "recoveryPasswordNew.newPasswordPlaceholder",
                "Digite sua nova senha"
              )}
              icon={passwordIcon}
              isPassword
              error={errors.newPassword}
            />
            <InputField
              label={t(
                "recoveryPasswordNew.confirmPassword",
                "Confirmar Senha"
              )}
              value={formData.confirmPassword}
              onChangeText={handleInputChange("confirmPassword")}
              placeholder={t(
                "recoveryPasswordNew.confirmPasswordPlaceholder",
                "Digite novamente sua senha"
              )}
              icon={passwordIcon}
              isPassword
              error={errors.confirmPassword}
            />
          </View>

          <View
            style={{
              backgroundColor: "rgba(255,255,255,0.1)",
              padding: 15,
              borderRadius: 8,
              marginBottom: 30
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: 14,
                fontWeight: "bold",
                marginBottom: 5
              }}
            >
              {t("recoveryPasswordNew.requirements", "Requisitos da senha:")}
            </Text>
            <Text style={{color: "#ccc", fontSize: 12, lineHeight: 18}}>
              {t(
                "recoveryPasswordNew.requirementsDesc",
                "• Mínimo de 6 caracteres\n• Pelo menos uma letra maiúscula\n• Pelo menos um número\n• Pelo menos um caractere especial"
              )}
            </Text>
          </View>

          <FullSizeButton
            text={t("recoveryPasswordNew.confirm", "Confirmar Nova Senha")}
            onPress={handleSubmit}
          />
        </View>
      </Screen>
    </>
  );
};

export default RecoveryPasswordNew;
