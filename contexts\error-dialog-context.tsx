import React, {createContext, PropsWithChildren, useCallback, useContext, useEffect, useMemo, useState} from "react";

export enum ErrorType {
    Warning,
    Error
}

export interface Error {
    errorType: ErrorType;
    title: string;
    description: string;
}

interface ErrorContextElements {
    currentError: Error|null;
    setCurrentError: React.Dispatch<React.SetStateAction<Error | null>>;
}

const ErrorContext = createContext<Partial<ErrorContextElements>>({});

const ErrorProvider: React.FC<PropsWithChildren> = (props) => {
    const [currentError, setCurrentError] = useState<Error | null>(null);
    const providerValue = useMemo(() => ({currentError, setCurrentError}),
        [currentError]);

    return (
        <ErrorContext.Provider value={providerValue}>
            {props.children}
        </ErrorContext.Provider>
    );
};

export function useErrorMessage() {
    const {currentError, setCurrentError} = useContext(ErrorContext);

    useEffect(() => {
        const timeout = setTimeout(() => {
            setCurrentError?.(null);
        }, 10000);

        return () => {
            clearTimeout(timeout);
        };
    }, [currentError, setCurrentError]);

    const emitError = useCallback((error: Error) => {
        setCurrentError?.(error)
    }, [setCurrentError]);

    const cleanError = useCallback(() => {
        setCurrentError?.(null);
    }, [setCurrentError]);

    return {
        emitError,
        cleanError,
        currentError
    };
}

export default ErrorProvider;

