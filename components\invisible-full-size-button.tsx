import React, {useCallback} from "react";
import {Text, TouchableOpacity} from "react-native";
import styles from "../styles/components/invisible-full-size-button.style";

export interface InvisibleFullSizeButtonProps {
    text: string;
    onPress?: () => void;
}

const InvisibleFullSizeButton: React.FC<InvisibleFullSizeButtonProps> = (props) => {
    const handleOnButtonPress = useCallback(() => props.onPress?.(), [props.onPress]);

    return (
        <TouchableOpacity style={styles.container} onPress={handleOnButtonPress}>
            <Text style={styles.text}>{props.text}</Text>
        </TouchableOpacity>
    );
};

export default InvisibleFullSizeButton;