import React from "react";
import Svg, {SvgProps, Path} from "react-native-svg";

const GraduationIcon: React.FC<SvgProps> = (props) => {
    const width = props.width ?? 20;
    const height = props.height ?? 20;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill="none"
            {...props}
        >
            <Path
                stroke={props.stroke ?? "#fff"}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.7}
                d="M4.167 8.333v5.01c0 .299 0 .448.045.58.04.117.106.223.193.312.097.1.231.166.499.3l4.5 2.25c.218.11.328.164.442.186a.836.836 0 0 0 .308 0c.114-.022.224-.076.442-.186l4.5-2.25c.268-.134.402-.2.5-.3a.832.832 0 0 0 .192-.312c.045-.132.045-.281.045-.58v-5.01M1.667 7.083l8.035-4.017c.11-.055.164-.082.221-.093.05-.01.103-.01.154 0 .057.01.112.038.221.093l8.035 4.017-8.035 4.018a.902.902 0 0 1-.221.093.414.414 0 0 1-.154 0 .902.902 0 0 1-.221-.093L1.667 7.083Z"
            />
        </Svg>
    );
};

export default GraduationIcon;
