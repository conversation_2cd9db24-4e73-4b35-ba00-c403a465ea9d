import React from "react";
import Svg, {SvgProps, Path} from "react-native-svg";

const PenIcon: React.FC<SvgProps> = (props) => {
    const width = props.width ?? 20;
    const height = props.height ?? 20;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill="none"
            {...props}
        >
            <Path
                stroke={props.stroke ?? "#fff"}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M10.833 5 5.418 6.083c-.302.06-.454.09-.577.164a.833.833 0 0 0-.27.26c-.079.12-.115.27-.187.57L1.667 18.333m0 0 11.255-2.717c.3-.072.45-.108.57-.187a.834.834 0 0 0 .26-.27c.074-.124.104-.275.165-.577L15 9.167M1.667 18.333l6.321-6.321m9.402-5.455L13.444 2.61c-.33-.33-.495-.495-.685-.556a.833.833 0 0 0-.515 0c-.19.061-.356.226-.686.556l-.614.615c-.33.33-.495.495-.557.685a.833.833 0 0 0 0 .515c.062.19.227.355.557.685l3.947 3.948c.33.33.496.495.686.557a.834.834 0 0 0 .515 0c.19-.062.355-.227.685-.557l.614-.614c.33-.33.496-.495.557-.686a.833.833 0 0 0 0-.515c-.061-.19-.226-.355-.556-.685Zm-8.223 2.61a1.667 1.667 0 1 1 0 3.333 1.667 1.667 0 0 1 0-3.333Z"
            />
        </Svg>
    );
};

export default PenIcon;
