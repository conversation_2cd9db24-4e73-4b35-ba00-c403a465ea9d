import React from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import FullSizeButton from "../../components/full-size-button";
import {router, useLocalSearchParams} from "expo-router";

interface CheckoutItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

const PaymentReview: React.FC = () => {
  const params = useLocalSearchParams();
  const paymentType = (params.paymentType as string) || "pix";

  // Dados simulados baseados na imagem de referência
  const items: CheckoutItem[] = [
    {
      id: "1",
      name: "E-book - Tendências de arquitetura em 2025",
      price: 150.0,
      quantity: 1
    }
  ];

  const subtotal = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  const total = 150.0;

  const getPaymentMethodInfo = () => {
    switch (paymentType) {
      case "boleto":
        return {
          name: "<PERSON><PERSON><PERSON> (Parcelado)",
          description: "Aprovação em até 3 horas",
          icon: "📄"
        };
      case "mastercard":
        return {
          name: "Cartão Mastercard (À vista / parcelado)",
          description: "Crédito - Pagamento imediato",
          icon: "💳"
        };
      case "visa":
        return {
          name: "Cartão Visa (À vista / parcelado)",
          description: "Crédito - Pagamento imediato",
          icon: "💳"
        };
      case "pix":
      default:
        return {
          name: "PIX (À Vista)",
          description: "Pagamento imediato",
          icon: "💎"
        };
    }
  };

  const selectedPaymentMethod = getPaymentMethodInfo();

  const handleGenerateCode = () => {
    if (paymentType === "boleto") {
      router.push("/(events)/boleto-payment");
    } else if (paymentType === "mastercard" || paymentType === "visa") {
      router.push(`/(events)/credit-card-form?paymentType=${paymentType}`);
    } else {
      router.push("/(events)/pix-payment");
    }
  };

  const containerStyle = {
    backgroundColor: "#202938",
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#44445A"
  };

  const titleStyle = {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600" as const,
    marginBottom: 16
  };

  const valueStyle = {
    color: "#fff",
    fontSize: 14,
    fontWeight: "400" as const,
    lineHeight: 20
  };

  const labelStyle = {
    color: "#ccc",
    fontSize: 12,
    fontWeight: "400" as const,
    lineHeight: 18,
    marginBottom: 4
  };

  return (
    <ScreenWithHeader screenTitle="Revisar os dados" backButton>
      <ScrollView
        style={{flex: 1, paddingHorizontal: 24}}
        showsVerticalScrollIndicator={false}
      >
        {/* Lista de produtos */}
        <View style={containerStyle}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 16
            }}
          >
            <Text style={titleStyle}>Lista de produtos</Text>
            <Text style={{color: "#ccc", fontSize: 14}}>
              ({items.length} itens)
            </Text>
          </View>

          {items.map((item) => (
            <View key={item.id} style={{marginBottom: 16}}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginBottom: 8
                }}
              >
                <View
                  style={{
                    width: 40,
                    height: 40,
                    backgroundColor: "#1F2238",
                    borderRadius: 8,
                    justifyContent: "center",
                    alignItems: "center",
                    marginRight: 12
                  }}
                >
                  <Text style={{fontSize: 16}}>📄</Text>
                </View>

                <View style={{flex: 1}}>
                  <Text style={valueStyle}>{item.name}</Text>
                  <Text style={{color: "#ccc", fontSize: 12, marginTop: 2}}>
                    {item.quantity}x
                  </Text>
                </View>

                <Text style={{color: "#fff", fontSize: 16, fontWeight: "600"}}>
                  R$ {item.price.toFixed(2).replace(".", ",")}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Métodos de pagamento */}
        <View style={containerStyle}>
          <Text style={titleStyle}>Métodos de pagamento</Text>

          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              padding: 12,
              backgroundColor: "#1F2238",
              borderRadius: 8
            }}
          >
            <View
              style={{
                width: 32,
                height: 32,
                backgroundColor: "#0F7C4D",
                borderRadius: 6,
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12
              }}
            >
              <Text style={{fontSize: 14, color: "#fff"}}>
                {selectedPaymentMethod.icon}
              </Text>
            </View>

            <View style={{flex: 1}}>
              <Text style={{color: "#fff", fontSize: 14, fontWeight: "600"}}>
                {selectedPaymentMethod.name}
              </Text>
              <Text style={{color: "#ccc", fontSize: 12}}>
                {selectedPaymentMethod.description}
              </Text>
            </View>
          </View>

          <TouchableOpacity style={{marginTop: 12, alignItems: "center"}}>
            <Text style={{color: "#0F7C4D", fontSize: 14, fontWeight: "600"}}>
              Selecionar outro método de pagamento
            </Text>
          </TouchableOpacity>
        </View>

        {/* Resumo de compra */}
        <View style={containerStyle}>
          <Text style={titleStyle}>Resumo de compra</Text>

          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 8
            }}
          >
            <Text style={labelStyle}>Subtotal ({items.length} itens)</Text>
            <Text style={valueStyle}>
              R$ {subtotal.toFixed(2).replace(".", ",")}
            </Text>
          </View>

          <View
            style={{
              borderTopWidth: 1,
              borderTopColor: "#44445A",
              paddingTop: 12,
              marginTop: 12,
              flexDirection: "row",
              justifyContent: "space-between"
            }}
          >
            <Text style={{color: "#fff", fontSize: 16, fontWeight: "600"}}>
              Valor a ser pago
            </Text>
            <Text style={{color: "#0F7C4D", fontSize: 18, fontWeight: "700"}}>
              R$ {total.toFixed(2).replace(".", ",")}
            </Text>
          </View>
        </View>

        <FullSizeButton
          text={
            paymentType === "boleto"
              ? "Gerar código de boleto"
              : paymentType === "mastercard" || paymentType === "visa"
              ? "Inserir dados do cartão"
              : "Gerar código de pagamento PIX"
          }
          onPress={handleGenerateCode}
        />
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PaymentReview;
