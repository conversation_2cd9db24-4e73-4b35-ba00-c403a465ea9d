import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        display: "flex",
        height: 44,
        padding: 4,
        justifyContent: "center",
        alignItems: "center",
        gap: 4,
        alignSelf: "stretch",
        borderRadius: 10,
        borderStyle: "solid",
        borderWidth: 1.5,
        flexDirection: "row",
        borderColor: stylesConstants.colors.whiteBorder
    },
    button: {
        display: "flex",
        height: 36,
        paddingVertical: 8,
        paddingHorizontal: 12,
        justifyContent: "center",
        alignItems: "center",
        gap: 8,
        flexGrow: 1,
        flexShrink: 0,
        flexBasis: 0,
        borderRadius: 6,
    },
    buttonActivated: {
        backgroundColor: stylesConstants.colors.secondary,
        boxShadow: "0px 1px 3px 0px rgba(10, 13, 18, 0.10) 0px 1px 2px -1px rgba(10, 13, 18, 0.10)"
    },
    buttonText: {
        fontFamily: stylesConstants.fonts.openSans,
        color: stylesConstants.colors.gray200,
        fontSize: 14,
        fontStyle: "normal",
        fontWeight: 700,
        lineHeight: 20
    },
    buttonTextActivated: {
        color: stylesConstants.colors.gray900,
    }
});

export default styles;