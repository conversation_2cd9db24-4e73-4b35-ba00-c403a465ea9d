import {useCallback, useState} from "react";
import {useTranslation} from "react-i18next";
import {LoginRequest, LoginRequestSchema, LoginResponse} from "@/models/login";
import {formatZodError} from "@/utils/zod-utils";
import LoginService from "@/services/login.service";
import {ErrorType, useErrorMessage} from "@/contexts/error-dialog-context";
import {useLoading} from "@/contexts/loading-context";
import useNotification from "./use-notification";
import BiometricService from "@/services/biometric.service";
import {concatMap, map} from "rxjs";
import {SessionData, useSession} from "@/contexts/session.context";
import {useRouter} from "expo-router";
import * as SecureStore from "expo-secure-store";
import ApiService from "@/services/api.service";

const STORAGE_KEY_NAME: string = "user-login-data";

function useLogin() {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [restoreRequest, setRestoreRequest] = useState<boolean>(false);
  const {t} = useTranslation();
  const {firebaseToken} = useNotification();
  const errorActions = useErrorMessage();
  const loadingAction = useLoading();
  const session = useSession();
  const router = useRouter();

  const login = useCallback(
    (request: LoginRequest) => {
      ApiService.onExpiredToken = handleLogout;
      const validation = LoginRequestSchema.safeParse(request);
      if (!validation.success) {
        setErrors(formatZodError(validation.error));
        errorActions.emitError({
          title: t("errors.emptyFields"),
          description: t("errors.emptyFieldsDescription"),
          errorType: ErrorType.Warning
        });
        return;
      }

      setErrors({});

      loadingAction.setCurrentLoading?.(true);

      BiometricService.hasBiometric(BiometricService.createBiometricConfig(t))
        .pipe(
          concatMap((hasBiometric) => {
            return LoginService.login({
              ...request,
              activatedBiometrics: hasBiometric,
              firebaseToken: firebaseToken ?? undefined
            }).pipe(
              map((data: LoginResponse) => ({
                response: data,
                hasBiometric: hasBiometric
              }))
            );
          })
        )
        .subscribe({
          next: async (response) => {
            const sessionData = {
              token: response.response.accessToken,
              email: "<EMAIL>",
              name: "placeholder name"
            } satisfies SessionData;

            if (response.hasBiometric) {
              await SecureStore.setItemAsync(
                STORAGE_KEY_NAME,
                JSON.stringify(sessionData)
              );
            }
            session.setLoginData?.(sessionData);
            router.navigate("(tabs)/home");
          },
          error: () => {
            errorActions.emitError({
              title: t("errors.emptyFields"),
              description: t("errors.tryLater"),
              errorType: ErrorType.Error
            });
            loadingAction.setCurrentLoading?.(false);
          },
          complete: () => {
            loadingAction.setCurrentLoading?.(false);
          }
        });
    },
    [errorActions, t, loadingAction, firebaseToken, session, router]
  );

  const restoreSession = useCallback(() => {
    ApiService.onExpiredToken = handleLogout;
    console.log("Restoring session", restoreRequest);
    if (restoreRequest) return;

    SecureStore.getItemAsync(STORAGE_KEY_NAME, {
      requireAuthentication: true
    }).then((rawSessionInfo) => {
      if (!rawSessionInfo) return;
      BiometricService.hasBiometric(
        BiometricService.createBiometricConfig(t)
      ).subscribe({
        next: async (hasBiometric) => {
          if (!hasBiometric) return;

          const sessionData = JSON.parse(rawSessionInfo) satisfies SessionData;
          session.setLoginData?.(sessionData);
          router.navigate("/(tabs)/home");
          setRestoreRequest(true);
        }
      });
    });
  }, [router, t, session, restoreRequest]);

  const handleLogout = useCallback(async () => {
    session.setLoginData?.(null);
    await SecureStore.deleteItemAsync(STORAGE_KEY_NAME);
    router.replace("/(auth)/login");
  }, [router, session]);

  return {
    login,
    errors,
    restoreSession
  };
}

export default useLogin;
