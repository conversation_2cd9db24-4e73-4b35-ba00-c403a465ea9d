import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    pageContainer: {
        flexGrow: 1,
        display: "flex",
        justifyContent: "space-between"
    },
    container: {
        display: "flex",
        flexGrow: 1,
        flexShrink: 1,
        gap: 20
    },
    scheduleTextContainer: {
        display: "flex",
        flexDirection: "row"
    },
    scheduleTextBold: {
        fontWeight: "bold",
        marginLeft: 2,
        marginRight: 2
    },
    createEventButtonContainer: {
        display: "flex",
        alignItems: "center"
    },
    createEventButton: {
        alignItems: "center",
        backgroundColor: stylesConstants.colors.brand.brand500,
        paddingHorizontal: 24,
        paddingVertical: 10,
        display: "flex",
        flexDirection: "row",
        gap: 8,
        borderStyle: "solid",
        borderColor: stylesConstants.colors.brand.brand500,
        borderWidth: 1,
        borderRadius: 40
    },
    createEventButtonText: {
        color: stylesConstants.colors.gray25,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 700,
        lineHeight: 24,
        fontStyle: "normal"
    }
});

export default styles;
