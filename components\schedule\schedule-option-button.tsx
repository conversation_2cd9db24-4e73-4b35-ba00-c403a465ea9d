import React from "react";
import {TouchableOpacity, View, Text, ColorValue, StyleProp, TextStyle} from "react-native";
import styles from "../../styles/components/schedule/schedule-option-button.style";

export interface ScheduleOptionButtonProps {
    bigIcon: React.ReactNode;
    title: string;
    subtitle: string | ((styles: StyleProp<TextStyle>) => React.ReactNode);
    icon: React.ReactNode;
    iconColor: ColorValue;
    onPress?: () => void;
}

const ScheduleOptionButton: React.FC<ScheduleOptionButtonProps> = (props) => {
    return (
        <TouchableOpacity style={styles.container} onPress={props.onPress}>
            <View style={styles.bigIcon}>
                {props.bigIcon}
            </View>
            <View style={styles.internalContainer}>
                <View style={styles.textContainer}>
                    <Text style={[styles.text, styles.title]}>{props.title}</Text>
                    {typeof props.subtitle === "string" ? (
                        <Text style={styles.text}>{props.subtitle}</Text>
                    ) : (
                        props.subtitle(styles.text)
                    )}
                </View>
                <View style={[styles.iconContainer, {backgroundColor: props.iconColor}]}>
                    <View style={styles.svgContainer}>
                        {props.icon}
                    </View>
                </View>
            </View>
        </TouchableOpacity>
    );
};

export default ScheduleOptionButton;