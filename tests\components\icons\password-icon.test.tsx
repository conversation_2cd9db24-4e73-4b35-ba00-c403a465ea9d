import React from "react";
import { render } from "@testing-library/react-native";

jest.mock("react-native-svg", () => {
  const Svg = ({ children, ...rest }: any) => <svg {...rest}>{children}</svg>;
  const Path = (props: any) => <path {...props} />;
  return {
    __esModule: true,
    default: Svg,
    Svg,
    Path
  };
});

import PasswordIcon from "../../../components/icons/password-icon";

describe("PasswordIcon", () => {
  it("renders without crashing", () => {
    const { toJSON } = render(<PasswordIcon />);
    expect(toJSON()).not.toBeUndefined();
  });

  it("honors custom size props", () => {
    const { getByTestId } = render(<PasswordIcon width={22} height={22} testID="icon" />);
    const icon = getByTestId("icon");
    expect(icon.props.width).toBe(22);
    expect(icon.props.height).toBe(22);
  });
});
