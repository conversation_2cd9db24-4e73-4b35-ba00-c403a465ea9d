{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-64:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88cfc00e8e0a9ed392dc6923ed9897c3\\transformed\\browser-1.6.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "69,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "7329,8020,8125,8236", "endColumns": "102,104,110,104", "endOffsets": "7427,8120,8231,8336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\559afa6d6ee92008dceb7e133cc79c58\\transformed\\play-services-base-18.0.1\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4853,4960,5136,5274,5383,5541,5677,5799,6057,6236,6343,6521,6659,6821,7000,7068,7134", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "4955,5131,5269,5378,5536,5672,5794,5907,6231,6338,6516,6654,6816,6995,7063,7129,7210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2e07b8f892dff7e7220c58c45859f4c7\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "330,441,549,662,750,856,971,1051,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2368,2469,2579,2697,2805,2968,15232", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "436,544,657,745,851,966,1046,1123,1214,1307,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2363,2464,2574,2692,2800,2963,3065,15312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\463a0a6d18bcedcdf0dcc1e5161e05be\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3793,3890,3992,4093,4190,4297,4405,15557", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3885,3987,4088,4185,4292,4400,4522,15653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59ca874c8ef89227cc0c3a48f60e855c\\transformed\\biometric-1.2.0-alpha04\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,341,455,551,701,819,933,1055,1208,1351,1503,1630,1768,1868,2031,2162,2299,2451,2577,2710,2810,2942,3032,3154,3257,3392", "endColumns": "158,126,113,95,149,117,113,121,152,142,151,126,137,99,162,130,136,151,125,132,99,131,89,121,102,134,105", "endOffsets": "209,336,450,546,696,814,928,1050,1203,1346,1498,1625,1763,1863,2026,2157,2294,2446,2572,2705,2805,2937,3027,3149,3252,3387,3493"}, "to": {"startLines": "33,34,68,70,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,153,159,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3070,3229,7215,7432,7752,7902,8341,8455,8577,8730,8873,9025,9152,9290,9390,9553,9684,9821,9973,15099,15658,15758,15890,15980,16102,16205,16340", "endColumns": "158,126,113,95,149,117,113,121,152,142,151,126,137,99,162,130,136,151,125,132,99,131,89,121,102,134,105", "endOffsets": "3224,3351,7324,7523,7897,8015,8450,8572,8725,8868,9020,9147,9285,9385,9548,9679,9816,9968,10094,15227,15753,15885,15975,16097,16200,16335,16441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2684515d4d7b0c87770167e126691f99\\transformed\\material-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,627,717,822,958,1043,1103,1168,1267,1335,1394,1483,1551,1618,1681,1756,1824,1878,1998,2056,2118,2172,2247,2389,2479,2557,2651,2734,2819,2964,3048,3131,3277,3373,3450,3508,3559,3625,3699,3777,3848,3934,4008,4087,4160,4232,4348,4452,4525,4624,4724,4798,4873,4980,5032,5121,5188,5279,5373,5435,5499,5562,5632,5751,5856,5965,6065,6127,6182,6267,6354,6432", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "275,359,439,525,622,712,817,953,1038,1098,1163,1262,1330,1389,1478,1546,1613,1676,1751,1819,1873,1993,2051,2113,2167,2242,2384,2474,2552,2646,2729,2814,2959,3043,3126,3272,3368,3445,3503,3554,3620,3694,3772,3843,3929,4003,4082,4155,4227,4343,4447,4520,4619,4719,4793,4868,4975,5027,5116,5183,5274,5368,5430,5494,5557,5627,5746,5851,5960,6060,6122,6177,6262,6349,6427,6502"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,71,72,73,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3356,3440,3520,3606,3703,4527,4632,4768,7528,7588,7653,10099,10167,10226,10315,10383,10450,10513,10588,10656,10710,10830,10888,10950,11004,11079,11221,11311,11389,11483,11566,11651,11796,11880,11963,12109,12205,12282,12340,12391,12457,12531,12609,12680,12766,12840,12919,12992,13064,13180,13284,13357,13456,13556,13630,13705,13812,13864,13953,14020,14111,14205,14267,14331,14394,14464,14583,14688,14797,14897,14959,15014,15317,15404,15482", "endLines": "5,35,36,37,38,39,47,48,49,71,72,73,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,155,156,157", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "325,3435,3515,3601,3698,3788,4627,4763,4848,7583,7648,7747,10162,10221,10310,10378,10445,10508,10583,10651,10705,10825,10883,10945,10999,11074,11216,11306,11384,11478,11561,11646,11791,11875,11958,12104,12200,12277,12335,12386,12452,12526,12604,12675,12761,12835,12914,12987,13059,13175,13279,13352,13451,13551,13625,13700,13807,13859,13948,14015,14106,14200,14262,14326,14389,14459,14578,14683,14792,14892,14954,15009,15094,15399,15477,15552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70a5ec28bf4682b0ab5b2d0ef3085bdc\\transformed\\play-services-basement-18.3.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5912", "endColumns": "144", "endOffsets": "6052"}}]}]}