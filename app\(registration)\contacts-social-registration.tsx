import React, {useState, useCallback} from "react";
import {Text, View, ColorValue} from "react-native";
import Screen from "../../components/screen";
import BackgroundLogoTexture from "../../components/logos/background-logo-texture";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";
import PhoneIcon from "../../components/icons/phone-icon";

const ContactsSocialRegistration: React.FC = () => {
  const {t} = useTranslation();
  const [formData, setFormData] = useState({
    emergencyContact: "",
    emergencyPhone: "",
    linkedin: "",
    instagram: "",
    website: ""
  });

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
    };

  const phoneIcon = useCallback(
    (errorColor?: ColorValue) => <PhoneIcon replaceColor={errorColor} />,
    []
  );

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={{flex: 1, padding: 20}}>
          <BackButton />
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#fff",
              textAlign: "center",
              marginBottom: 20
            }}
          >
            {t("contactsSocialRegistration.title", "Contatos e Redes Sociais")}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "#fff",
              textAlign: "center",
              marginBottom: 30
            }}
          >
            {t(
              "contactsSocialRegistration.description",
              "Adicione seus contatos e redes sociais"
            )}
          </Text>

          <View style={{gap: 15, marginBottom: 30}}>
            <Text
              style={{
                fontSize: 18,
                fontWeight: "bold",
                color: "#fff",
                marginBottom: 10
              }}
            >
              {t(
                "contactsSocialRegistration.emergencyContact",
                "Contato de Emergência"
              )}
            </Text>
            <InputField
              label={t(
                "contactsSocialRegistration.emergencyContactName",
                "Nome"
              )}
              value={formData.emergencyContact}
              onChangeText={handleInputChange("emergencyContact")}
              placeholder={t(
                "contactsSocialRegistration.emergencyContactPlaceholder",
                "Nome do contato"
              )}
            />
            <InputField
              label={t("contactsSocialRegistration.emergencyPhone", "Telefone")}
              value={formData.emergencyPhone}
              onChangeText={handleInputChange("emergencyPhone")}
              placeholder={t(
                "contactsSocialRegistration.emergencyPhonePlaceholder",
                "Telefone do contato"
              )}
              icon={phoneIcon}
              inputMode="tel"
            />

            <Text
              style={{
                fontSize: 18,
                fontWeight: "bold",
                color: "#fff",
                marginTop: 20,
                marginBottom: 10
              }}
            >
              {t("contactsSocialRegistration.socialNetworks", "Redes Sociais")}
            </Text>
            <InputField
              label={t("contactsSocialRegistration.linkedin", "LinkedIn")}
              value={formData.linkedin}
              onChangeText={handleInputChange("linkedin")}
              placeholder={t(
                "contactsSocialRegistration.linkedinPlaceholder",
                "linkedin.com/in/seuperfil"
              )}
            />
            <InputField
              label={t("contactsSocialRegistration.instagram", "Instagram")}
              value={formData.instagram}
              onChangeText={handleInputChange("instagram")}
              placeholder={t(
                "contactsSocialRegistration.instagramPlaceholder",
                "@seuperfil"
              )}
            />
            <InputField
              label={t("contactsSocialRegistration.website", "Website")}
              value={formData.website}
              onChangeText={handleInputChange("website")}
              placeholder={t(
                "contactsSocialRegistration.websitePlaceholder",
                "www.seusite.com"
              )}
            />
          </View>

          <FullSizeButton
            text={t("contactsSocialRegistration.next", "Próximo")}
            onPress={() => {}}
          />
        </View>
      </Screen>
    </>
  );
};

export default ContactsSocialRegistration;
