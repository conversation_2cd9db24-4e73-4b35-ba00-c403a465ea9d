{"name": "club-m-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest"}, "dependencies": {"@dev-plugins/react-query": "~0.2.0", "@react-native-async-storage/async-storage": "2.1.2", "@tanstack/react-query": "^5.80.3", "axios": "^1.9.0", "expo": "53.0.10", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.0", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-image": "~2.2.0", "expo-image-manipulator": "~13.1.7", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-local-authentication": "~16.0.4", "expo-localization": "~16.1.5", "expo-network": "~7.1.5", "expo-notifications": "~0.31.3", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.1.6", "i18next": "^25.0.0", "react": "19.0.0", "react-i18next": "^15.4.1", "react-native": "0.79.3", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "rxjs": "^7.8.2", "zod": "^3.24.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "jest": "~29.7.0", "jest-expo": "53.0.0", "react-query-external-sync": "^2.2.0", "typescript": "^5.3.3"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["<rootDir>/jest-setup.js"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@sentry/react-native|native-base|react-native-svg|expo-image-manipulator)"], "moduleNameMapper": {"^expo-image-manipulator$": "<rootDir>/__mocks__/expo-image-manipulator.js", "^@/(.*)$": "<rootDir>/$1", "^@/app/(.*)$": "<rootDir>/app/$1", "^@/components/(.*)$": "<rootDir>/components/$1", "^@/contexts/(.*)$": "<rootDir>/contexts/$1", "^@/hooks/(.*)$": "<rootDir>/hooks/$1", "^@/services/(.*)$": "<rootDir>/services/$1", "^@/models/(.*)$": "<rootDir>/models/$1", "^@/styles/(.*)$": "<rootDir>/styles/$1", "^@/utils/(.*)$": "<rootDir>/utils/$1", "^@/assets/(.*)$": "<rootDir>/assets/$1", "^@/locales/(.*)$": "<rootDir>/locales/$1", "^@/tests/(.*)$": "<rootDir>/tests/$1"}}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}