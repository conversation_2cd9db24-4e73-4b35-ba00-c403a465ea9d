import {useEffect, useState} from "react";
import FirebaseService from "../services/firebase.service";
import {ErrorType, useErrorMessage} from "../contexts/error-dialog-context";
import {useTranslation} from "react-i18next";

function useNotification() {
    const errorAction = useErrorMessage();
    const [firebaseToken, setFirebaseToken] = useState<string | null>(null);
    const {t} = useTranslation();

    useEffect(() => {
        FirebaseService.initPushNotification().subscribe({
            next: (token) => {
                setFirebaseToken(token);
            },
            error: (err) => {
                console.error(err);
                errorAction.emitError({
                    errorType: ErrorType.Error,
                    title: t("errors.failedToRegisterFirebase"),
                    description: t("errors.failedToRegisterFirebaseDescription")
                });
            }
        });
    }, [errorAction.emitError, t]);

    return {
        firebaseToken
    };
}

export default useNotification;