import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface SendIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const SendIcon: React.FC<SendIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#DFE9F0", [props.replaceColor]);

    return (
        <Svg
            width={20}
            height={20}
            viewBox="0 0 20 20"
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                d="M8.7496 11.2501L17.4996 2.50014M8.85591 11.5235L11.046 17.1552C11.2389 17.6513 11.3354 17.8994 11.4744 17.9718C11.5949 18.0346 11.7385 18.0347 11.859 17.972C11.9981 17.8998 12.0949 17.6518 12.2884 17.1559L17.7803 3.08281C17.955 2.63516 18.0424 2.41133 17.9946 2.26831C17.9531 2.1441 17.8556 2.04663 17.7314 2.00514C17.5884 1.95736 17.3646 2.0447 16.9169 2.21939L2.84381 7.71134C2.34791 7.90486 2.09997 8.00163 2.02771 8.14071C1.96507 8.26129 1.96515 8.40483 2.02794 8.52533C2.10036 8.66433 2.34842 8.7608 2.84454 8.95373L8.47621 11.1438C8.57692 11.183 8.62727 11.2026 8.66967 11.2328C8.70725 11.2596 8.74012 11.2925 8.76692 11.3301C8.79717 11.3725 8.81675 11.4228 8.85591 11.5235Z"
                stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"
            />
        </Svg>
    );
};

export default SendIcon; 