import React, {createContext, useCallback, useContext, useMemo} from "react";
import ApiService from "@/services/api.service";

export interface SessionData {
  token: string;
  name: string;
  email: string;
}

interface LoginContextElements {
  loginData: SessionData | null;
  setLoginData: (loginData: SessionData | null) => void;
}

const SessionContext = createContext<Partial<LoginContextElements>>({});

let loginData: SessionData | null = null;

const SessionProvider: React.FC<React.PropsWithChildren> = (props) => {
  const setLoginData = useCallback(
    (newValue: SessionData | null) => {
      loginData = newValue;
      ApiService.authenticationToken = newValue?.token ?? null;
    },
    [loginData]
  );

  const providerValue = useMemo(() => ({loginData, setLoginData}), [loginData]);

  return (
    <SessionContext.Provider value={providerValue}>
      {props.children}
    </SessionContext.Provider>
  );
};

export function useSession() {
  const {loginData, setLoginData} = useContext(SessionContext);
  return {loginData, setLoginData};
}

export default SessionProvider;
