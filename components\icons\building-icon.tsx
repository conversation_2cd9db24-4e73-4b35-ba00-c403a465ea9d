import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface BuildingIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const BuildingIcon: React.FC<BuildingIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#fff",
    [props.replaceColor]
  );
  return (
    <Svg width={21} height={20} fill="none" {...props}>
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M6.5 9.167H4.083c-.466 0-.7 0-.878.09a.833.833 0 0 0-.364.365c-.09.178-.09.411-.09.878v7M14 9.167h2.416c.466 0 .7 0 .878.09.157.08.284.208.364.365.091.178.091.411.091.878v7m-3.75 0V5.167c0-.934 0-1.4-.182-1.757a1.667 1.667 0 0 0-.728-.728c-.357-.182-.823-.182-1.757-.182H9.167c-.934 0-1.4 0-1.757.182-.314.16-.568.414-.728.728-.182.357-.182.823-.182 1.757V17.5m12.083 0H1.917m7.5-11.667h1.666M9.417 9.167h1.666M9.417 12.5h1.666"
      />
    </Svg>
  );
};

export default BuildingIcon;
