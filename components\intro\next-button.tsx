import React from "react";
import {TouchableHighlight} from "react-native";
import ChevronLeftIcon from "../icons/chevron-left-icon";
import ChevronRightIcon from "../icons/chevron-right-icon";
import styles from "../../styles/components/intro/next-button.style";
import stylesConstants from "../../styles/styles-constants";

export interface NextButtonProps {
    isPrevious?: boolean;
    onPress?: () => void;
    disabled?: boolean;
}

const NextButton: React.FC<NextButtonProps> = (props) => {
    return (
        <TouchableHighlight style={[styles.button, props.disabled && styles.disabled]} onPress={props.onPress}
                            disabled={props.disabled}
                            underlayColor={stylesConstants.colors.brand.brand400}>
            {props.isPrevious ? <ChevronLeftIcon/> : <ChevronRightIcon/>}
        </TouchableHighlight>
    );
};

export default NextButton;