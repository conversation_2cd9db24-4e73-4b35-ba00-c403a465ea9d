import React, {useMemo} from "react";
import Svg, {Path, SvgProps, Defs, LinearGradient, Stop} from "react-native-svg";
import {ColorValue} from "react-native";

export interface InstagramIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const InstagramIcon: React.FC<InstagramIconProps> = (props) => {
  const useGradient = !props.replaceColor;

  return (
    <Svg
      width={20}
      height={20}
      fill="none"
      {...props}
    >
      <Defs>
        <LinearGradient id="instagram-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor="#833AB4" />
          <Stop offset="50%" stopColor="#FD1D1D" />
          <Stop offset="100%" stopColor="#FCB045" />
        </LinearGradient>
      </Defs>
      <Path
        fill={useGradient ? "url(#instagram-gradient)" : props.replaceColor}
        d="M10 1.802c2.67 0 2.987.01 4.042.059 2.71.123 3.975 1.409 4.099 4.099.048 1.054.057 1.37.057 4.04 0 2.672-.01 2.988-.057 4.042-.124 2.687-1.387 3.975-4.1 4.099-1.054.048-1.37.058-4.041.058-2.67 0-2.987-.01-4.04-.058-2.717-.124-3.977-1.416-4.1-4.1-.048-1.054-.058-1.37-.058-4.041 0-2.67.01-2.986.058-4.04.124-2.69 1.387-3.977 4.1-4.1 1.054-.048 1.37-.058 4.04-.058zM10 0C7.284 0 6.944.012 5.877.06 2.246.227.227 2.242.061 5.877.012 6.944 0 7.284 0 10s.012 3.057.06 4.123c.167 3.632 2.182 5.65 5.817 5.817 1.067.048 1.407.06 4.123.06s3.057-.012 4.123-.06c3.629-.167 5.652-2.182 5.816-5.817.05-1.066.061-1.407.061-4.123s-.012-3.056-.06-4.122C19.773 2.249 17.757.228 14.123.061 13.056.012 12.716 0 10 0zm0 4.865a5.135 5.135 0 100 10.27 5.135 5.135 0 000-10.27zm0 8.468a3.333 3.333 0 110-6.666 3.333 3.333 0 010 6.666zm5.338-9.87a1.2 1.2 0 100 2.4 1.2 1.2 0 000-2.4z"
      />
    </Svg>
  );
};

export default InstagramIcon;
