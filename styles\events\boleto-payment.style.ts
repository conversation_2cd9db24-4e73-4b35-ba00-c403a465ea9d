import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 600,
    marginBottom: 8,
    textAlign: "center"
  },
  subtitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    textAlign: "center"
  },
  valueContainer: {
    alignItems: "center",
    marginBottom: 24
  },
  valueLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    marginBottom: 4
  },
  valueAmount: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 32,
    fontWeight: 700,
    lineHeight: 40
  },
  codeContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  codeLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    marginBottom: 8
  },
  codeDisplay: {
    backgroundColor: stylesConstants.colors.highlightBackground,
    padding: 16,
    borderRadius: 8,
    marginBottom: 16
  },
  codeText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: "monospace",
    fontSize: 14,
    fontWeight: 400,
    textAlign: "center",
    lineHeight: 20
  },
  buttonContainer: {
    gap: 12
  },
  copyButton: {
    backgroundColor: stylesConstants.colors.fullWhite,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center"
  },
  copyButtonText: {
    color: stylesConstants.colors.mainBackground,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    marginLeft: 8
  },
  pdfButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center"
  },
  pdfButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    marginLeft: 8
  },
  instructionsContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24
  },
  instructionsTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    marginBottom: 12
  },
  instructionStep: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginBottom: 8
  },
  instructionStepBold: {
    fontWeight: 600
  },
  instructionNote: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    marginTop: 8
  },
  timerContainer: {
    alignItems: "center",
    marginBottom: 24
  },
  timerText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    textAlign: "center",
    marginBottom: 8
  },
  timerHighlight: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: 600
  },
  icon: {
    fontSize: 14,
    marginRight: 8
  }
});

export default styles;
