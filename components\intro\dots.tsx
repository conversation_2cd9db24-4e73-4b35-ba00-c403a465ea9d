import React, {useMemo} from "react";
import {StyleProp, View, ViewStyle, StyleSheet} from "react-native";
import styles from "../../styles/components/intro/dots.style";

export interface DotsPros {
  style?: StyleProp<ViewStyle>;
  dotState: number;
}

interface DotData {
  id: string;
  position: number;
}

const Dots: React.FC<DotsPros> = (props) => {
  const baseStyle = StyleSheet.flatten(styles.dot);
  const activeStyle = StyleSheet.flatten(styles.active);
  const minWidth = baseStyle.width;
  const maxWidth = activeStyle.width;
  const widthDiff = maxWidth - minWidth;

  // Create stable dot data with unique identifiers
  const dots = useMemo<DotData[]>(
    () => [
      {id: "intro-dot-0", position: 0},
      {id: "intro-dot-1", position: 1},
      {id: "intro-dot-2", position: 2}
    ],
    []
  );

  return (
    <View style={[props.style, styles.container]}>
      {dots.map((dot) => {
        const distance = Math.abs(props.dotState - dot.position);
        const clamped = Math.max(0, 1 - distance);
        const width = minWidth + widthDiff * clamped;

        return (
          <View
            key={dot.id}
            style={[
              styles.dot,
              {
                width,
                borderRadius: width / 2,
                backgroundColor: `rgba(255,255,255,${clamped})`
              }
            ]}
          />
        );
      })}
    </View>
  );
};

export default Dots;
