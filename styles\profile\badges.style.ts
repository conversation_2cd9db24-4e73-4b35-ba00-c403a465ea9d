import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: stylesConstants.colors.mainBackground,
    },
    contentContainer: {
        paddingHorizontal: 24,
        paddingVertical: 16,
    },
    headerContainer: {
        marginBottom: 24,
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 24,
        fontWeight: 700,
        lineHeight: 32,
        marginBottom: 8,
    },
    subtitle: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 400,
        lineHeight: 24,
    },
    statsContainer: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        padding: 16,
        marginBottom: 24,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
    },
    statsRow: {
        flexDirection: "row",
        justifyContent: "space-around",
    },
    statItem: {
        alignItems: "center",
    },
    statValue: {
        color: stylesConstants.colors.brand.primary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 20,
        fontWeight: 700,
        lineHeight: 28,
        marginBottom: 4,
    },
    statLabel: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18,
        textAlign: "center",
    },
    categoriesContainer: {
        marginBottom: 24,
    },
    categoriesTitle: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 600,
        lineHeight: 24,
        marginBottom: 12,
    },
    categoriesList: {
        flexDirection: "row",
        gap: 8,
    },
    categoryChip: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 16,
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
    },
    categoryChipActive: {
        backgroundColor: stylesConstants.colors.brand.primary,
        borderColor: stylesConstants.colors.brand.primary,
    },
    categoryChipText: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18,
    },
    categoryChipTextActive: {
        color: stylesConstants.colors.fullWhite,
        fontWeight: 600,
    },
    badgesGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 16,
        justifyContent: "space-between",
    },
    badgeItem: {
        width: "48%",
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        padding: 16,
        alignItems: "center",
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
    },
    badgeItemEarned: {
        borderColor: stylesConstants.colors.brand.primary,
        backgroundColor: stylesConstants.colors.highlightBackground,
    },
    badgeItemLocked: {
        opacity: 0.5,
    },
    badgeIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        marginBottom: 12,
        backgroundColor: stylesConstants.colors.gray300,
        alignItems: "center",
        justifyContent: "center",
    },
    badgeIconEarned: {
        backgroundColor: stylesConstants.colors.brand.primary,
    },
    badgeName: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 600,
        lineHeight: 20,
        textAlign: "center",
        marginBottom: 4,
    },
    badgeDescription: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18,
        textAlign: "center",
        marginBottom: 8,
    },
    badgeProgress: {
        width: "100%",
        height: 4,
        backgroundColor: stylesConstants.colors.gray300,
        borderRadius: 2,
        marginBottom: 4,
    },
    badgeProgressFill: {
        height: "100%",
        backgroundColor: stylesConstants.colors.brand.primary,
        borderRadius: 2,
    },
    badgeProgressText: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 10,
        fontWeight: 400,
        lineHeight: 14,
        textAlign: "center",
    },
    earnedDate: {
        color: stylesConstants.colors.brand.primary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 10,
        fontWeight: 600,
        lineHeight: 14,
        textAlign: "center",
    },
});

export default styles;
