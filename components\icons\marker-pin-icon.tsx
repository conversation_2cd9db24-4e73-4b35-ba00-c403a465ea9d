import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface MarkerPinIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const MarkerPinIcon: React.FC<MarkerPinIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#fff",
    [props.replaceColor]
  );
  return (
    <Svg width={16} height={16} fill="none" {...props}>
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M8 8.666a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"
      />
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M8 14.666c2.667-2.666 5.333-5.054 5.333-8a5.333 5.333 0 1 0-10.666 0c0 2.946 2.666 5.334 5.333 8Z"
      />
    </Svg>
  );
};

export default MarkerPinIcon;
