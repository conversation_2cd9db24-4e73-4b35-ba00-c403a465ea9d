import {useCallback, useState} from "react";
import {useTranslation} from "react-i18next";
import {UpsellRequest, UpsellRequestSchema} from "../models/login";
import {formatZodError} from "../utils/zod-utils";
import LoginService from "../services/login.service";
import {ErrorType, useErrorMessage} from "../contexts/error-dialog-context";
import {useLoading} from "../contexts/loading-context";
import {useSession} from "../contexts/session.context";
import {useRouter} from "expo-router";

function useUpsell() {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const {t} = useTranslation();
  const errorActions = useErrorMessage();
  const loadingAction = useLoading();
  const session = useSession();
  const router = useRouter();

  const upsell = useCallback(
    (request: UpsellRequest) => {
      const validation = UpsellRequestSchema.safeParse(request);
      if (!validation.success) {
        setErrors(formatZodError(validation.error));
        errorActions.emitError({
          title: t("errors.emptyFields"),
          description: t("errors.emptyFieldsDescription"),
          errorType: ErrorType.Warning
        });
        return;
      }

      setErrors({});

      loadingAction.setCurrentLoading?.(true);

      LoginService.upsell(request).subscribe({
        next: (response) => {
          session.setLoginData?.({
            token: response.accessToken,
            email: "<EMAIL>",
            name: request.name
          });

          router.navigate("(tabs)/home");
        },
        error: () => {
          errorActions.emitError({
            title: t("errors.emptyFields"),
            description: t("errors.tryLater"),
            errorType: ErrorType.Error
          });
          loadingAction.setCurrentLoading?.(false);
        },
        complete: () => {
          loadingAction.setCurrentLoading?.(false);
        }
      });
    },
    [errorActions, t, loadingAction, session, router]
  );

  return {
    upsell,
    errors
  };
}

export default useUpsell;
