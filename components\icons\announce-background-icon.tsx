import React from "react";
import Svg, {<PERSON><PERSON><PERSON><PERSON>, De<PERSON>, G, <PERSON>, SvgProps} from "react-native-svg";

const AnnounceBackgroundIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={114}
            height={100}
            fill="none"
            {...props}
        >
            <G clipPath="url(#a)" opacity={0.1}>
                <Path
                    stroke="#fff"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={3.5}
                    d="m-1.851 73.012 16.543 29.801c.466.839.698 1.258.936 1.614a10.5 10.5 0 0 0 10.616 4.503c.422-.077.885-.201 1.811-.449 1.16-.311 1.74-.466 2.215-.645a10.499 10.499 0 0 0 6.673-11.558c-.083-.501-.239-1.08-.55-2.24L17.027 21.755M71 50.775c9.803-2.627 15.62-12.703 12.993-22.505-2.626-9.803-12.702-15.62-22.504-12.994m-43.196 6.14L-.723 26.51c-12.603 3.377-20.083 16.331-16.706 28.935 3.377 12.603 16.332 20.082 28.935 16.705l19.017-5.096c8.957-2.4 21.202-.534 30.89 1.809 5.652 1.367 8.478 2.05 10.04 1.42 1.45-.585 2.401-1.56 2.95-3.023.593-1.577-.117-4.225-1.536-9.52L59.622 8.31c-1.419-5.297-2.128-7.944-3.43-9.014-1.206-.992-2.519-1.361-4.066-1.143-1.668.235-3.773 2.24-7.985 6.25-7.219 6.873-16.89 14.611-25.848 17.011Z"
                />
            </G>
            <Defs>
                <ClipPath id="a">
                    <Path
                        fill="#fff"
                        d="M-41.159 7.452 80.548-25.159l32.611 121.707-121.707 32.61z"
                    />
                </ClipPath>
            </Defs>
        </Svg>
    );
};

export default AnnounceBackgroundIcon;