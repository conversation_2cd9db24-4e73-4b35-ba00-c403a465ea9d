{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-64:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2e07b8f892dff7e7220c58c45859f4c7\\transformed\\appcompat-1.7.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,15128", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,15205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\463a0a6d18bcedcdf0dcc1e5161e05be\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "40,41,42,43,44,45,46,165", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3719,3814,3916,4018,4121,4225,4322,15597", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3809,3911,4013,4116,4220,4317,4428,15693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59ca874c8ef89227cc0c3a48f60e855c\\transformed\\biometric-1.2.0-alpha04\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,345,460,550,718,851,966,1090,1219,1352,1488,1607,1746,1841,2003,2130,2279,2421,2549,2674,2775,2911,3019,3162,3264,3401", "endColumns": "160,128,114,89,167,132,114,123,128,132,135,118,138,94,161,126,148,141,127,124,100,135,107,142,101,136,102", "endOffsets": "211,340,455,545,713,846,961,1085,1214,1347,1483,1602,1741,1836,1998,2125,2274,2416,2544,2669,2770,2906,3014,3157,3259,3396,3499"}, "to": {"startLines": "33,34,69,71,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,158,166,167,168,169,170,171,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3014,3175,7022,7237,7546,7714,8164,8279,8403,8532,8665,8801,8920,9059,9154,9316,9443,9592,9734,15003,15698,15799,15935,16043,16186,16288,16425", "endColumns": "160,128,114,89,167,132,114,123,128,132,135,118,138,94,161,126,148,141,127,124,100,135,107,142,101,136,102", "endOffsets": "3170,3299,7132,7322,7709,7842,8274,8398,8527,8660,8796,8915,9054,9149,9311,9438,9587,9729,9857,15123,15794,15930,16038,16181,16283,16420,16523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2684515d4d7b0c87770167e126691f99\\transformed\\material-1.12.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1034,1098,1193,1263,1326,1433,1498,1565,1626,1693,1755,1809,1923,1982,2043,2097,2172,2298,2386,2472,2573,2663,2753,2895,2967,3040,3177,3266,3347,3404,3460,3526,3597,3674,3745,3825,3897,3973,4054,4124,4224,4311,4383,4474,4567,4641,4716,4808,4860,4942,5008,5092,5178,5240,5304,5367,5436,5540,5644,5738,5838,5899,5959,6043,6127,6203", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "268,346,422,501,595,683,775,887,969,1029,1093,1188,1258,1321,1428,1493,1560,1621,1688,1750,1804,1918,1977,2038,2092,2167,2293,2381,2467,2568,2658,2748,2890,2962,3035,3172,3261,3342,3399,3455,3521,3592,3669,3740,3820,3892,3968,4049,4119,4219,4306,4378,4469,4562,4636,4711,4803,4855,4937,5003,5087,5173,5235,5299,5362,5431,5535,5639,5733,5833,5894,5954,6038,6122,6198,6277"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,72,73,74,93,96,98,99,100,101,102,103,104,105,106,107,108,109,110,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3304,3382,3458,3537,3631,4433,4525,4637,7327,7387,7451,9862,10085,10218,10325,10390,10457,10518,10585,10647,10701,10815,10874,10935,10989,11064,11258,11346,11432,11533,11623,11713,11855,11927,12000,12137,12226,12307,12364,12420,12486,12557,12634,12705,12785,12857,12933,13014,13084,13184,13271,13343,13434,13527,13601,13676,13768,13820,13902,13968,14052,14138,14200,14264,14327,14396,14500,14604,14698,14798,14859,14919,15210,15294,15370", "endLines": "5,35,36,37,38,39,47,48,49,72,73,74,93,96,98,99,100,101,102,103,104,105,106,107,108,109,110,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,160,161,162", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "318,3377,3453,3532,3626,3714,4520,4632,4714,7382,7446,7541,9927,10143,10320,10385,10452,10513,10580,10642,10696,10810,10869,10930,10984,11059,11185,11341,11427,11528,11618,11708,11850,11922,11995,12132,12221,12302,12359,12415,12481,12552,12629,12700,12780,12852,12928,13009,13079,13179,13266,13338,13429,13522,13596,13671,13763,13815,13897,13963,14047,14133,14195,14259,14322,14391,14495,14599,14693,14793,14854,14914,14998,15289,15365,15444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70a5ec28bf4682b0ab5b2d0ef3085bdc\\transformed\\play-services-basement-18.3.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5791", "endColumns": "163", "endOffsets": "5950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,211,294,364,432,507", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "136,206,289,359,427,502,575"}, "to": {"startLines": "50,94,95,97,111,163,164", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4719,9932,10002,10148,11190,15449,15524", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "4800,9997,10080,10213,11253,15519,15592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88cfc00e8e0a9ed392dc6923ed9897c3\\transformed\\browser-1.6.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "70,77,78,79", "startColumns": "4,4,4,4", "startOffsets": "7137,7847,7951,8059", "endColumns": "99,103,107,104", "endOffsets": "7232,7946,8054,8159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\559afa6d6ee92008dceb7e133cc79c58\\transformed\\play-services-base-18.0.1\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4805,4908,5061,5187,5293,5433,5559,5682,5955,6120,6226,6383,6512,6665,6822,6885,6944", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "4903,5056,5182,5288,5428,5554,5677,5786,6115,6221,6378,6507,6660,6817,6880,6939,7017"}}]}]}