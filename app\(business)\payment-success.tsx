import React from "react";
import {Text, View, ScrollView} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import FullSizeButton from "../../components/full-size-button";
import {router, useLocalSearchParams} from "expo-router";
import styles from "../../styles/business/payment-success.style";

interface PurchasedProduct {
  id: string;
  name: string;
  seller: string;
  category: string;
  date: string;
  time: string;
  ticketNumber: string;
  value: string;
}

const PaymentSuccess: React.FC = () => {
  const params = useLocalSearchParams();
  const paymentType = (params.paymentType as string) || "pix";

  // Mock data based on the design
  const purchasedProducts: PurchasedProduct[] = [
    {
      id: "1",
      name: "Publicação de oportunidade",
      seller: "Club M",
      category: "Oportunidade de negócio",
      date: "Disponível imediatamente",
      time: "<PERSON><PERSON> vitalício",
      ticketNumber: "5565166616914",
      value: "R$ 150,00"
    }
  ];

  const getPaymentInfo = () => {
    switch (paymentType) {
      case "boleto":
        return {
          method: "Boleto",
          status: "À Vista",
          totalValue: "R$ 150,00"
        };
      case "credit-card":
        return {
          method: "Cartão de crédito",
          status: "À Vista",
          totalValue: "R$ 150,00"
        };
      case "pix":
      default:
        return {
          method: "PIX",
          status: "À Vista",
          totalValue: "R$ 150,00"
        };
    }
  };

  const paymentInfo = getPaymentInfo();

  const handleViewPublication = () => {
    router.push("/(business)/business-center");
  };

  const handleBackToBusinessCenter = () => {
    router.push("/(business)/business-center");
  };

  return (
    <ScreenWithHeader
      screenTitle="Seu anúncio foi publicado com sucesso!"
      backButton
    >
      <ScrollView style={styles.contentContainer}>
        {/* Success Header */}
        <View style={styles.headerContainer}>
          <View style={styles.successIcon}>
            <Text style={styles.successIconText}>✓</Text>
          </View>
          <Text style={styles.title}>
            Seu anúncio foi publicado com sucesso!
          </Text>
          <Text style={styles.subtitle}>
            Que incrível! Agora é só aguardar os contatos chegarem até você.
          </Text>
        </View>

        {/* Product Information */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Informações de pagamento</Text>

          {purchasedProducts.map((product) => (
            <View key={product.id} style={styles.productCard}>
              <View style={styles.productHeader}>
                <View style={styles.productIcon}>
                  <Text style={styles.productIconText}>📢</Text>
                </View>
                <View style={styles.productInfo}>
                  <Text style={styles.productName}>{product.name}</Text>
                  <Text style={styles.productSeller}>{product.seller}</Text>
                  <Text style={styles.productCategory}>{product.category}</Text>
                </View>
                <Text style={styles.productValue}>{product.value}</Text>
              </View>

              <View style={styles.productDetails}>
                <View style={styles.productDetailRow}>
                  <Text style={styles.productDetailLabel}>Data:</Text>
                  <Text style={styles.productDetailValue}>{product.date}</Text>
                </View>
                <View style={styles.productDetailRow}>
                  <Text style={styles.productDetailLabel}>Horário:</Text>
                  <Text style={styles.productDetailValue}>{product.time}</Text>
                </View>
                <View style={styles.productDetailRow}>
                  <Text style={styles.productDetailLabel}>Nº do ingresso:</Text>
                  <Text style={styles.productDetailValue}>
                    {product.ticketNumber}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>

        {/* Payment Summary */}
        <View style={styles.sectionContainer}>
          <View style={styles.paymentSummaryContainer}>
            <View style={styles.paymentMethodRow}>
              <Text style={styles.paymentMethodLabel}>Método de pagamento</Text>
              <Text style={styles.paymentMethodValue}>
                {paymentInfo.method}
              </Text>
            </View>
            <View style={styles.paymentMethodRow}>
              <Text style={styles.paymentMethodLabel}>Status do pagamento</Text>
              <Text style={styles.paymentMethodValue}>
                {paymentInfo.status}
              </Text>
            </View>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Soma total</Text>
              <Text style={styles.totalValue}>{paymentInfo.totalValue}</Text>
            </View>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <FullSizeButton
            text="Ver publicação"
            onPress={handleViewPublication}
          />
          <View style={styles.secondaryButtonContainer}>
            <Text
              style={styles.secondaryButton}
              onPress={handleBackToBusinessCenter}
            >
              Voltar para central de negócios
            </Text>
          </View>
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PaymentSuccess;
