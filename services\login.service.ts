import {
    ForgetPasswordRequest,
    ForgetPasswordResponse,
    LoginRequest,
    LoginResponse,
    ResetPasswordRequest,
    UpsellRequest
} from "../models/login";
import {Observable} from "rxjs";
import ApiService from "./api.service";

class LoginService {
    public static forgetPassword(
        request: ForgetPasswordRequest
    ): Observable<ForgetPasswordResponse> {
        return ApiService.request("POST", "/app/auth/forgot", request);
    }

    public static resetPassword(
        request: ResetPasswordRequest
    ): Observable<void> {
        return ApiService.request("POST", "/auth/reset", request);
    }

    public static login(request: LoginRequest): Observable<LoginResponse> {
        return ApiService.request("POST", "/app/auth/login", request);
    }

    public static upsell(request: UpsellRequest): Observable<LoginResponse> {
        return ApiService.request("POST", "/app/auth/upsell", request);
    }
}

export default LoginService;
