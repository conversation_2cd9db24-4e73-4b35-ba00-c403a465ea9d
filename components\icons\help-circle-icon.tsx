import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface HelpCircleIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const HelpCircleIcon: React.FC<HelpCircleIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#FCFCFD", [props.replaceColor]);

    return (
        <Svg
            width={24}
            height={24}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10ZM9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3M12 17h.01"
            />
        </Svg>
    );
};

export default HelpCircleIcon;
