import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  searchContainer: {
    marginBottom: 24,
  },
  contactsList: {
    flex: 1,
  },
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.borderDefault,
    gap: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    alignItems: "center",
    justifyContent: "center",
  },
  avatarText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 2,
  },
  inviteDate: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  statusText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: 600,
    lineHeight: 14,
  },
  statusInvited: {
    backgroundColor: stylesConstants.colors.alert400,
  },
  statusActive: {
    backgroundColor: stylesConstants.colors.green400,
  },
  statusNotActive: {
    backgroundColor: stylesConstants.colors.error300,
  },
  buttonContainer: {
    marginTop: 24,
  },
});

export default styles;
