# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_pagerview cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnsvg cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/C_/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\C_\Users\vja_p\eclipse-workspace\club-m-app\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\C_\Users\vja_p\eclipse-workspace\club-m-app\android\app\build\generated\autolinking\src\main\jni
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libappmodules.pdb

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libappmodules.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libappmodules.so

build C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o CMakeFiles/appmodules.dir/C_/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_safeareacontext.so C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnscreens.so C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnsvg.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnscreens.so C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnsvg.so C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_safeareacontext.so pagerview_autolinked_build/react_codegen_pagerview rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_safeareacontext.so  C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnscreens.so  C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnsvg.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_FILE = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libappmodules.so
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libappmodules.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_pagerview


#############################################
# Order-only phony target for react_codegen_pagerview

build cmake_object_order_depends_target_react_codegen_pagerview: phony || pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/pagerview-generated.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\pagerview-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  TARGET_COMPILE_PDB = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\
  TARGET_PDB = ""

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview
  TARGET_COMPILE_PDB = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\
  TARGET_PDB = ""

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview
  TARGET_COMPILE_PDB = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\
  TARGET_PDB = ""

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/Props.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview
  TARGET_COMPILE_PDB = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\
  TARGET_PDB = ""

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview
  TARGET_COMPILE_PDB = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\
  TARGET_PDB = ""

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/States.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview
  TARGET_COMPILE_PDB = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\
  TARGET_PDB = ""

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/pagerviewJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\pagerviewJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview
  TARGET_COMPILE_PDB = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_pagerview

build pagerview_autolinked_build/react_codegen_pagerview: phony pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o


#############################################
# Utility command for edit_cache

build pagerview_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\pagerview_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build pagerview_autolinked_build/edit_cache: phony pagerview_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build pagerview_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\pagerview_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build pagerview_autolinked_build/rebuild_cache: phony pagerview_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6f5e9a889ce05b1437b66d76e5e7cc4b/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\6f5e9a889ce05b1437b66d76e5e7cc4b\safeareacontext\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\6f5e9a889ce05b1437b66d76e5e7cc4b\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6f5e9a889ce05b1437b66d76e5e7cc4b/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\6f5e9a889ce05b1437b66d76e5e7cc4b\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\6f5e9a889ce05b1437b66d76e5e7cc4b\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/637075661fc7df10d099d84bc95132a7/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\637075661fc7df10d099d84bc95132a7\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\637075661fc7df10d099d84bc95132a7\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b729e7594bdc0489a288f34cf6c257c7/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b729e7594bdc0489a288f34cf6c257c7\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b729e7594bdc0489a288f34cf6c257c7\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/93234f7e3b55529212e573ce0ffebf12/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\93234f7e3b55529212e573ce0ffebf12\renderer\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\93234f7e3b55529212e573ce0ffebf12\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b729e7594bdc0489a288f34cf6c257c7/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b729e7594bdc0489a288f34cf6c257c7\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b729e7594bdc0489a288f34cf6c257c7\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/93234f7e3b55529212e573ce0ffebf12/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\93234f7e3b55529212e573ce0ffebf12\renderer\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\93234f7e3b55529212e573ce0ffebf12\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/637075661fc7df10d099d84bc95132a7/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\637075661fc7df10d099d84bc95132a7\safeareacontext\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\637075661fc7df10d099d84bc95132a7\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08514487bfd740f1d25d89d48926607b/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\08514487bfd740f1d25d89d48926607b\source\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\08514487bfd740f1d25d89d48926607b\source\codegen\jni
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.so

build C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6f5e9a889ce05b1437b66d76e5e7cc4b/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6f5e9a889ce05b1437b66d76e5e7cc4b/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/637075661fc7df10d099d84bc95132a7/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b729e7594bdc0489a288f34cf6c257c7/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/93234f7e3b55529212e573ce0ffebf12/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b729e7594bdc0489a288f34cf6c257c7/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/93234f7e3b55529212e573ce0ffebf12/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/637075661fc7df10d099d84bc95132a7/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08514487bfd740f1d25d89d48926607b/source/codegen/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_FILE = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.so
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ff8ae22c20ef6e4d191479f4b7fdfdae\components\rnscreens\RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ff8ae22c20ef6e4d191479f4b7fdfdae\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/64218aea3e00fa4153a8335d5549f189/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\64218aea3e00fa4153a8335d5549f189\renderer\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\64218aea3e00fa4153a8335d5549f189\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/11faececd8d77183512524d0983633c9/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\11faececd8d77183512524d0983633c9\react\renderer\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\11faececd8d77183512524d0983633c9\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ff8ae22c20ef6e4d191479f4b7fdfdae\components\rnscreens\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ff8ae22c20ef6e4d191479f4b7fdfdae\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ff8ae22c20ef6e4d191479f4b7fdfdae\components\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ff8ae22c20ef6e4d191479f4b7fdfdae\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50e12c5f08c0eb6bbadcfe1ff0cef3c6/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\50e12c5f08c0eb6bbadcfe1ff0cef3c6\rnscreens\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\50e12c5f08c0eb6bbadcfe1ff0cef3c6\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ff8ae22c20ef6e4d191479f4b7fdfdae\components\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ff8ae22c20ef6e4d191479f4b7fdfdae\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/619a170b7e242551e8af21645e19c63e/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\619a170b7e242551e8af21645e19c63e\cpp\react\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\619a170b7e242551e8af21645e19c63e\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5ec7543ddd869da8cbaa3b0de2ec8a72/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5ec7543ddd869da8cbaa3b0de2ec8a72\react\renderer\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5ec7543ddd869da8cbaa3b0de2ec8a72\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6170f051b402263e29a17be5f116edac/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6170f051b402263e29a17be5f116edac\jni\react\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6170f051b402263e29a17be5f116edac\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d43c4cc4ca6eb5e1f0b03ae096a1e19/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6d43c4cc4ca6eb5e1f0b03ae096a1e19\codegen\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6d43c4cc4ca6eb5e1f0b03ae096a1e19\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6170f051b402263e29a17be5f116edac/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6170f051b402263e29a17be5f116edac\jni\react\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6170f051b402263e29a17be5f116edac\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d43c4cc4ca6eb5e1f0b03ae096a1e19/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6d43c4cc4ca6eb5e1f0b03ae096a1e19\codegen\jni\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6d43c4cc4ca6eb5e1f0b03ae096a1e19\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50ddde5b377eaeb88cda1f2c1faeb57a/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\50ddde5b377eaeb88cda1f2c1faeb57a\renderer\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\50ddde5b377eaeb88cda1f2c1faeb57a\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.so

build C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/64218aea3e00fa4153a8335d5549f189/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/11faececd8d77183512524d0983633c9/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50e12c5f08c0eb6bbadcfe1ff0cef3c6/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/619a170b7e242551e8af21645e19c63e/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5ec7543ddd869da8cbaa3b0de2ec8a72/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6170f051b402263e29a17be5f116edac/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d43c4cc4ca6eb5e1f0b03ae096a1e19/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6170f051b402263e29a17be5f116edac/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d43c4cc4ca6eb5e1f0b03ae096a1e19/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/50ddde5b377eaeb88cda1f2c1faeb57a/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_FILE = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.so
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnscreens.pdb


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Order-only phony target for react_codegen_rnsvg

build cmake_object_order_depends_target_react_codegen_rnsvg: phony || rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\79ee79e28c672b7398b0d91bc82e40ee\common\cpp\react\renderer\components\rnsvg\RNSVGImageShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\79ee79e28c672b7398b0d91bc82e40ee\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\79ee79e28c672b7398b0d91bc82e40ee\common\cpp\react\renderer\components\rnsvg\RNSVGImageState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\79ee79e28c672b7398b0d91bc82e40ee\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/70ca31bc41ac52db676c7cc15e19b44e/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\70ca31bc41ac52db676c7cc15e19b44e\cpp\react\renderer\components\rnsvg\RNSVGLayoutableShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\70ca31bc41ac52db676c7cc15e19b44e\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\79ee79e28c672b7398b0d91bc82e40ee\common\cpp\react\renderer\components\rnsvg\RNSVGShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\79ee79e28c672b7398b0d91bc82e40ee\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\rnsvg.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/ab78cb32376308f1452fed2f863dee37/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\ab78cb32376308f1452fed2f863dee37\codegen\jni\react\renderer\components\rnsvg\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\ab78cb32376308f1452fed2f863dee37\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2d8972c47384b76323ec3e7b13b1dd63\source\codegen\jni\react\renderer\components\rnsvg\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2d8972c47384b76323ec3e7b13b1dd63\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2d8972c47384b76323ec3e7b13b1dd63\source\codegen\jni\react\renderer\components\rnsvg\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2d8972c47384b76323ec3e7b13b1dd63\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2d8972c47384b76323ec3e7b13b1dd63\source\codegen\jni\react\renderer\components\rnsvg\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2d8972c47384b76323ec3e7b13b1dd63\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2d8972c47384b76323ec3e7b13b1dd63\source\codegen\jni\react\renderer\components\rnsvg\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2d8972c47384b76323ec3e7b13b1dd63\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/ab78cb32376308f1452fed2f863dee37/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\ab78cb32376308f1452fed2f863dee37\codegen\jni\react\renderer\components\rnsvg\rnsvgJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\ab78cb32376308f1452fed2f863dee37\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Link the shared library C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.so

build C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnsvg.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnsvg_Debug rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/70ca31bc41ac52db676c7cc15e19b44e/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/ab78cb32376308f1452fed2f863dee37/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/ab78cb32376308f1452fed2f863dee37/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/70312fc86ee51e27d343045c10899056/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/fc98766b9298386f4aefafd293f4f926/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnsvg.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_FILE = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.so
  TARGET_PDB = C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\arm64-v8a\libreact_codegen_rnsvg.pdb


#############################################
# Utility command for edit_cache

build rnsvg_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\rnsvg_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnsvg_autolinked_build/edit_cache: phony rnsvg_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a\rnsvg_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnsvg_autolinked_build/rebuild_cache: phony rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libappmodules.so

build libappmodules.so: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libappmodules.so

build libreact_codegen_rnscreens.so: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnscreens.so

build libreact_codegen_rnsvg.so: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnsvg.so

build libreact_codegen_safeareacontext.so: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_safeareacontext.so

build react_codegen_pagerview: phony pagerview_autolinked_build/react_codegen_pagerview

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnscreens.so

build react_codegen_rnsvg: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnsvg.so

build react_codegen_safeareacontext: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a

build all: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libappmodules.so rnasyncstorage_autolinked_build/all pagerview_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all rnsvg_autolinked_build/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/pagerview_autolinked_build

build pagerview_autolinked_build/all: phony pagerview_autolinked_build/react_codegen_pagerview

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/rnsvg_autolinked_build

build rnsvg_autolinked_build/all: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_rnsvg.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/intermediates/cxx/Debug/5c14151w/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/CMakeFiles/cmake.verify_globs | C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
