import React from "react";
import Svg, {SvgProps, Path} from "react-native-svg";

const PixIcon: React.FC<SvgProps> = (props) => {
    const width = props.width ?? 13;
    const height = props.height ?? 13;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 13 13"
            fill="none"
            {...props}
        >
            <Path
                fill="#32BCAD"
                d="M10.138 9.946c-.51 0-.99-.199-1.35-.56l-1.95-1.95a.37.37 0 0 0-.512 0L4.369 9.395c-.361.36-.84.559-1.35.559h-.385l2.47 2.47a1.975 1.975 0 0 0 2.793 0l2.476-2.477h-.235ZM3.018 3.048c.51 0 .99.198 1.35.559l1.958 1.957c.14.141.37.141.512 0l1.95-1.95c.36-.36.84-.56 1.35-.56h.235L7.897.579a1.975 1.975 0 0 0-2.793 0l-2.47 2.47h.384Z"
            />
            <Path
                fill="#32BCAD"
                d="m12.422 5.103-1.497-1.496a.286.286 0 0 1-.106.021h-.68c-.353 0-.697.143-.946.391l-1.95 1.95a.933.933 0 0 1-1.323 0L3.963 4.012a1.345 1.345 0 0 0-.945-.391h-.836a.284.284 0 0 1-.101-.02L.578 5.103a1.975 1.975 0 0 0 0 2.793l1.503 1.502a.284.284 0 0 1 .1-.02h.837c.352 0 .696-.143.945-.391L5.92 7.03a.959.959 0 0 1 1.324 0l1.95 1.95c.248.248.592.39.944.39h.68c.038 0 .074.01.107.022l1.497-1.496a1.975 1.975 0 0 0 0-2.793Z"
            />
        </Svg>
    );
};

export default PixIcon;
