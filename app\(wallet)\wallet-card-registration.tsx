import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import Screen<PERSON>ithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";

const WalletCardRegistration: React.FC = () => {
  const {t} = useTranslation();
  const [formData, setFormData] = useState({
    cardNumber: "",
    cardName: "",
    expiryDate: "",
    cvv: "",
    isDefault: false
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      let formattedValue = value;

      // Format card number
      if (field === "cardNumber") {
        formattedValue = value
          .replace(/\s/g, "")
          .replace(/(.{4})/g, "$1 ")
          .trim();
        if (formattedValue.length > 19)
          formattedValue = formattedValue.substring(0, 19);
      }

      // Format expiry date
      if (field === "expiryDate") {
        formattedValue = value.replace(/\D/g, "");
        if (formattedValue.length >= 2) {
          formattedValue =
            formattedValue.substring(0, 2) +
            "/" +
            formattedValue.substring(2, 4);
        }
        if (formattedValue.length > 5)
          formattedValue = formattedValue.substring(0, 5);
      }

      // Format CVV
      if (field === "cvv") {
        formattedValue = value.replace(/\D/g, "");
        if (formattedValue.length > 4)
          formattedValue = formattedValue.substring(0, 4);
      }

      setFormData((prev) => ({...prev, [field]: formattedValue}));

      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({...prev, [field]: ""}));
      }
    };

  const detectCardBrand = (number: string) => {
    const cleanNumber = number.replace(/\s/g, "");
    if (cleanNumber.startsWith("4")) return "Visa";
    if (cleanNumber.startsWith("5") || cleanNumber.startsWith("2"))
      return "Mastercard";
    if (cleanNumber.startsWith("3")) return "American Express";
    return "Unknown";
  };

  const getCardBackgroundColor = (brand: string) => {
    switch (brand) {
      case "Visa":
        return "#1A1F71";
      case "Mastercard":
        return "#EB001B";
      case "American Express":
        return "#006FCF";
      default:
        return "#666";
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (
      !formData.cardNumber ||
      formData.cardNumber.replace(/\s/g, "").length < 13
    ) {
      newErrors.cardNumber = t(
        "walletCardRegistration.errors.invalidCard",
        "Número do cartão inválido"
      );
    }

    if (!formData.cardName.trim()) {
      newErrors.cardName = t(
        "walletCardRegistration.errors.nameRequired",
        "Nome é obrigatório"
      );
    }

    if (!formData.expiryDate || formData.expiryDate.length !== 5) {
      newErrors.expiryDate = t(
        "walletCardRegistration.errors.invalidExpiry",
        "Data de validade inválida"
      );
    }

    if (!formData.cvv || formData.cvv.length < 3) {
      newErrors.cvv = t(
        "walletCardRegistration.errors.invalidCvv",
        "CVV inválido"
      );
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      console.log("Card registration:", formData);
      // Handle card registration
    }
  };

  const cardBrand = detectCardBrand(formData.cardNumber);

  return (
    <ScreenWithHeader
      screenTitle={t("walletCardRegistration.title", "Adicionar Cartão")}
      backButton
    >
      <ScrollView style={{flex: 1, padding: 20}}>
        <Text
          style={{
            color: "#fff",
            fontSize: 16,
            marginBottom: 20,
            lineHeight: 22
          }}
        >
          {t(
            "walletCardRegistration.description",
            "Adicione um novo cartão de crédito à sua carteira para facilitar seus pagamentos."
          )}
        </Text>

        {/* Card Preview */}
        <View
          style={{
            backgroundColor: getCardBackgroundColor(cardBrand),
            borderRadius: 12,
            padding: 20,
            marginBottom: 30,
            minHeight: 120
          }}
        >
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "flex-start",
              marginBottom: 20
            }}
          >
            <Text style={{fontSize: 24}}>💳</Text>
            <Text style={{color: "#fff", fontSize: 14, fontWeight: "bold"}}>
              {cardBrand !== "Unknown" ? cardBrand.toUpperCase() : "CARTÃO"}
            </Text>
          </View>

          <Text
            style={{
              color: "#fff",
              fontSize: 18,
              fontWeight: "bold",
              marginBottom: 10,
              letterSpacing: 2
            }}
          >
            {formData.cardNumber || "**** **** **** ****"}
          </Text>

          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center"
            }}
          >
            <Text style={{color: "rgba(255,255,255,0.8)", fontSize: 14}}>
              {formData.cardName ||
                t("walletCardRegistration.cardholderName", "NOME DO PORTADOR")}
            </Text>
            <Text style={{color: "rgba(255,255,255,0.8)", fontSize: 14}}>
              {formData.expiryDate || "MM/AA"}
            </Text>
          </View>
        </View>

        {/* Form */}
        <View style={{gap: 15, marginBottom: 30}}>
          <InputField
            label={t("walletCardRegistration.cardNumber", "Número do Cartão")}
            value={formData.cardNumber}
            onChangeText={handleInputChange("cardNumber")}
            placeholder="0000 0000 0000 0000"
            inputMode="numeric"
            error={errors.cardNumber}
          />

          <InputField
            label={t("walletCardRegistration.cardName", "Nome no Cartão")}
            value={formData.cardName}
            onChangeText={handleInputChange("cardName")}
            placeholder={t(
              "walletCardRegistration.cardNamePlaceholder",
              "Nome como está no cartão"
            )}
            error={errors.cardName}
          />

          <View style={{flexDirection: "row", gap: 10}}>
            <View style={{flex: 1}}>
              <InputField
                label={t("walletCardRegistration.expiryDate", "Validade")}
                value={formData.expiryDate}
                onChangeText={handleInputChange("expiryDate")}
                placeholder="MM/AA"
                inputMode="numeric"
                error={errors.expiryDate}
              />
            </View>
            <View style={{flex: 1}}>
              <InputField
                label={t("walletCardRegistration.cvv", "CVV")}
                value={formData.cvv}
                onChangeText={handleInputChange("cvv")}
                placeholder="123"
                inputMode="numeric"
                isPassword
                error={errors.cvv}
              />
            </View>
          </View>
        </View>

        {/* Default Card Option */}
        <TouchableOpacity
          style={{
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: "rgba(255,255,255,0.1)",
            padding: 15,
            borderRadius: 8,
            marginBottom: 20
          }}
          onPress={() =>
            setFormData((prev) => ({...prev, isDefault: !prev.isDefault}))
          }
        >
          <View
            style={{
              width: 20,
              height: 20,
              borderRadius: 10,
              borderWidth: 2,
              borderColor: formData.isDefault ? "#007AFF" : "#ccc",
              backgroundColor: formData.isDefault ? "#007AFF" : "transparent",
              marginRight: 15
            }}
          />
          <View style={{flex: 1}}>
            <Text style={{color: "#fff", fontSize: 16, fontWeight: "bold"}}>
              {t(
                "walletCardRegistration.setDefault",
                "Definir como cartão padrão"
              )}
            </Text>
            <Text style={{color: "#ccc", fontSize: 14}}>
              {t(
                "walletCardRegistration.setDefaultDesc",
                "Este cartão será usado por padrão nos pagamentos"
              )}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Security Info */}
        <View
          style={{
            backgroundColor: "rgba(255,255,255,0.05)",
            padding: 15,
            borderRadius: 8,
            marginBottom: 20
          }}
        >
          <Text
            style={{
              color: "#fff",
              fontSize: 14,
              fontWeight: "bold",
              marginBottom: 5
            }}
          >
            🔒 {t("walletCardRegistration.security", "Segurança")}
          </Text>
          <Text style={{color: "#ccc", fontSize: 12, lineHeight: 18}}>
            {t(
              "walletCardRegistration.securityDesc",
              "Seus dados são criptografados com tecnologia de ponta. Não armazenamos informações sensíveis em nossos servidores."
            )}
          </Text>
        </View>

        <FullSizeButton
          text={t("walletCardRegistration.addCard", "Adicionar Cartão")}
          onPress={handleSubmit}
        />
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default WalletCardRegistration;
