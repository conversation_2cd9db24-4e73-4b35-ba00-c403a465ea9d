import React, {useState} from "react";
import {Text, View, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import styles from "@/styles/settings/manage-permissions.style";

interface PermissionSettings {
  faceId: boolean;
  location: boolean;
  camera: boolean;
  microphone: boolean;
}

const ManagePermissions: React.FC = () => {
  const {t} = useTranslation();
  const [permissions, setPermissions] = useState<PermissionSettings>({
    faceId: true,
    location: true,
    camera: false,
    microphone: false
  });

  const updatePermission = (key: keyof PermissionSettings, value: boolean) => {
    setPermissions((prev) => ({
      ...prev,
      [key]: value
    }));
  };

  const permissionItems: Array<{
    key: keyof PermissionSettings;
    title: string;
  }> = [
    {key: "faceId", title: "Face ID"},
    {key: "location", title: "Localização"},
    {key: "camera", title: "Câmera"},
    {key: "microphone", title: "Microfone"}
  ];

  return (
    <ScreenWithHeader
      screenTitle={t("permissions.title", "Gerenciar permissões")}
      backButton
      disablePadding
    >
      <ScreenWithHeader.InternalPadding style={styles.contentContainer}>
        {permissionItems.map((item) => (
          <View key={item.key} style={styles.permissionItem}>
            <Text style={styles.permissionTitle}>{item.title}</Text>
            <View style={styles.permissionControl}>
              <Text style={styles.permissionStatus}>
                {permissions[item.key] ? "Habilitado" : "Desabilitado"}
              </Text>
              <View style={styles.toggleContainer}>
                <TouchableOpacity
                  style={[
                    styles.toggleBase,
                    permissions[item.key]
                      ? styles.toggleEnabled
                      : styles.toggleDisabled
                  ]}
                  onPress={() =>
                    updatePermission(item.key, !permissions[item.key])
                  }
                  activeOpacity={0.8}
                >
                  <View
                    style={[
                      styles.toggleButton,
                      permissions[item.key]
                        ? styles.toggleButtonEnabled
                        : styles.toggleButtonDisabled
                    ]}
                  />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        ))}
      </ScreenWithHeader.InternalPadding>
    </ScreenWithHeader>
  );
};

export default ManagePermissions;
