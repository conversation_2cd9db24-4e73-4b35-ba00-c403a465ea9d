import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import <PERSON><PERSON>ithHeader from "@/components/screen-with-header";

import FullSizeButton from "@/components/full-size-button";
import PlusIcon from "@/components/icons/plus-icon";
import MinusIcon from "@/components/icons/minus-icon";
import TrashIcon from "@/components/icons/trash-icon";
import ShoppingCartIcon from "@/components/icons/shopping-cart-icon";
import ProductIcon from "@/components/icons/product-icon";
import styles from "@/styles/events/cart.style";
import stylesConstants from "@/styles/styles-constants";

interface CartItem {
  id: string;
  type: "ticket" | "product" | "service";
  name: string;
  description: string;
  price: number;
  quantity: number;
  eventDate?: string;
  eventLocation?: string;
}

const Cart: React.FC = () => {
  const [cartItems, setCartItems] = useState<CartItem[]>([
    {
      id: "1",
      type: "product",
      name: "Mentoria Coletiva sobre Resoluções Jurídicas",
      description: "Mentoria especializada em questões jurídicas empresariais",
      price: 1500.0,
      quantity: 1
    },
    {
      id: "2",
      type: "product",
      name: "Encontro - Comemoração de 10 anos da Cafeteria...",
      description: "Evento especial de comemoração",
      price: 1200.0,
      quantity: 1
    }
  ]);

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity === 0) {
      setCartItems((prev) => prev.filter((item) => item.id !== itemId));
    } else {
      setCartItems((prev) =>
        prev.map((item) =>
          item.id === itemId ? {...item, quantity: newQuantity} : item
        )
      );
    }
  };

  const getSubtotal = () => {
    return cartItems.reduce(
      (total, item) => total + item.price * item.quantity,
      0
    );
  };

  const getTaxes = () => {
    return getSubtotal() * 0.1; // 10% tax
  };

  const getTotal = () => {
    return getSubtotal() + getTaxes();
  };

  return (
    <ScreenWithHeader screenTitle="Carrinho de compras" backButton>
      <View style={styles.container}>
        {cartItems.length > 0 ? (
          <>
            <ScrollView style={styles.contentContainer}>
              <View style={styles.headerContainer}>
                <Text style={styles.title}>Lista de produtos</Text>
                <Text style={styles.itemCount}>({cartItems.length} itens)</Text>
              </View>

              <View style={styles.cartItemsList}>
                {cartItems.map((item) => (
                  <View key={item.id} style={styles.cartItem}>
                    <View style={styles.itemHeader}>
                      <View style={styles.itemIconContainer}>
                        <ProductIcon
                          replaceColor={stylesConstants.colors.textPrimary}
                        />
                      </View>

                      <View style={styles.itemContent}>
                        <Text style={styles.itemTitle}>{item.name}</Text>
                        <Text style={styles.itemPrice}>
                          R$ {item.price.toFixed(2)}
                        </Text>
                      </View>

                      <TouchableOpacity
                        style={styles.removeButton}
                        onPress={() => updateQuantity(item.id, 0)}
                      >
                        <TrashIcon
                          width={16}
                          height={16}
                          replaceColor={stylesConstants.colors.error600}
                        />
                      </TouchableOpacity>
                    </View>

                    <View style={styles.quantityControls}>
                      <View style={styles.quantityButtonContainer}>
                        <TouchableOpacity
                          style={styles.quantityButton}
                          onPress={() =>
                            updateQuantity(item.id, item.quantity - 1)
                          }
                        >
                          <MinusIcon
                            width={16}
                            height={16}
                            replaceColor={stylesConstants.colors.fullWhite}
                          />
                        </TouchableOpacity>

                        <Text style={styles.quantityText}>{item.quantity}</Text>

                        <TouchableOpacity
                          style={[
                            styles.quantityButton,
                            {
                              backgroundColor:
                                stylesConstants.colors.brand.primary
                            }
                          ]}
                          onPress={() =>
                            updateQuantity(item.id, item.quantity + 1)
                          }
                        >
                          <PlusIcon
                            width={16}
                            height={16}
                            replaceColor={stylesConstants.colors.fullWhite}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            </ScrollView>

            {/* Summary */}
            <View style={styles.summaryContainer}>
              <Text style={styles.title}>Resumo de compra</Text>

              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>
                  Subtotal ({cartItems.length} itens)
                </Text>
                <Text style={styles.summaryValue}>
                  R$ {getSubtotal().toFixed(2)}
                </Text>
              </View>

              <View style={[styles.summaryRow, styles.totalRow]}>
                <Text
                  style={[
                    styles.summaryLabel,
                    {color: stylesConstants.colors.fullWhite, fontWeight: 600}
                  ]}
                >
                  Valor total
                </Text>
                <Text style={styles.totalValue}>
                  R$ {getTotal().toFixed(2)}
                </Text>
              </View>

              <View style={styles.buttonContainer}>
                <FullSizeButton
                  text="Ir para métodos de pagamento"
                  onPress={() => {}}
                />
              </View>
            </View>
          </>
        ) : (
          <View style={styles.emptyCartContainer}>
            <View style={styles.emptyCartIconContainer}>
              <ShoppingCartIcon
                width={48}
                height={48}
                replaceColor={stylesConstants.colors.textPrimary}
              />
            </View>
            <Text style={styles.emptyCartTitle}>Seu carrinho está vazio!</Text>
            <Text style={styles.emptyCartText}>
              Que tal começar a explorar os produtos?{"\n"}
              Navegue pela loja e encontre o que você{"\n"}
              precisa!
            </Text>
            <View style={styles.buttonContainer}>
              <FullSizeButton text="Explorar produtos" onPress={() => {}} />
            </View>
          </View>
        )}
      </View>
    </ScreenWithHeader>
  );
};

export default Cart;
