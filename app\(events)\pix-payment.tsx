import React, {useState, useEffect} from "react";
import {Text, View, ScrollView, TouchableOpacity, Alert} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import {router} from "expo-router";
// Usando estilos inline para manter compatibilidade

const PixPayment: React.FC = () => {
  const {t} = useTranslation();
  const [timeLeft, setTimeLeft] = useState(9 * 60 + 58); // 09:58 baseado na imagem

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const pixCode = "3bPRdKc3Wcc4da7-d8Fc-4d7b-T2";
  const totalValue = "R$ 2.363,00";

  const copyPixCode = async () => {
    try {
      // Implementação nativa para copiar texto
      // Em um ambiente real, você usaria Clipboard do React Native
      // Para este exemplo, vamos simular a funcionalidade

      // Simular a cópia do código
      const textToCopy = pixCode;

      // Mock da funcionalidade de copiar - em produção usar:
      // import { Clipboard } from 'react-native';
      // await Clipboard.setString(textToCopy);

      Alert.alert(
        "Código copiado!",
        "O código PIX foi copiado para a área de transferência."
      );
    } catch (error) {
      Alert.alert("Erro", "Não foi possível copiar o código. Tente novamente.");
    }
  };

  const handleFinishPayment = () => {
    router.push("/(events)/payment-success");
  };

  return (
    <ScreenWithHeader screenTitle="Código de pagamento via PIX" backButton>
      <ScrollView
        style={{flex: 1, paddingHorizontal: 24}}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={{alignItems: "center", marginBottom: 24}}>
          <Text
            style={{
              color: "#fff",
              fontSize: 18,
              fontWeight: "600",
              marginBottom: 8,
              textAlign: "center"
            }}
          >
            Efetuar pagamento
          </Text>
          <Text style={{color: "#ccc", fontSize: 14, textAlign: "center"}}>
            Confira as instruções e efetue o pagamento.
          </Text>
        </View>

        {/* Valor total */}
        <View style={{alignItems: "center", marginBottom: 24}}>
          <Text style={{color: "#ccc", fontSize: 14, marginBottom: 4}}>
            Valor total
          </Text>
          <Text style={{color: "#fff", fontSize: 32, fontWeight: "700"}}>
            {totalValue}
          </Text>
        </View>

        {/* Código PIX */}
        <View
          style={{
            backgroundColor: "#202938",
            borderRadius: 8,
            padding: 16,
            marginBottom: 24,
            width: "100%",
            borderWidth: 1,
            borderColor: "#44445A"
          }}
        >
          <Text style={{color: "#ccc", fontSize: 14, marginBottom: 8}}>
            Digite / copie o código manualmente:
          </Text>

          <View
            style={{
              backgroundColor: "#1F2238",
              padding: 16,
              borderRadius: 8,
              marginBottom: 16
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: 16,
                fontFamily: "monospace",
                textAlign: "center"
              }}
            >
              {pixCode}
            </Text>
          </View>

          <TouchableOpacity
            style={{
              backgroundColor: "#fff",
              padding: 12,
              borderRadius: 8,
              alignItems: "center",
              flexDirection: "row",
              justifyContent: "center"
            }}
            onPress={copyPixCode}
          >
            <Text
              style={{
                color: "#000",
                fontSize: 14,
                fontWeight: "bold",
                marginRight: 8
              }}
            >
              📋
            </Text>
            <Text style={{color: "#000", fontSize: 14, fontWeight: "bold"}}>
              Copiar código PIX
            </Text>
          </TouchableOpacity>
        </View>

        {/* Instruções de pagamento */}
        <View
          style={{
            backgroundColor: "#202938",
            borderRadius: 8,
            padding: 16,
            marginBottom: 24
          }}
        >
          <Text
            style={{
              color: "#fff",
              fontSize: 16,
              fontWeight: "600",
              marginBottom: 12
            }}
          >
            Instruções de pagamento
          </Text>

          <Text
            style={{
              color: "#ccc",
              fontSize: 14,
              lineHeight: 20,
              marginBottom: 8
            }}
          >
            <Text style={{fontWeight: "600"}}>1° passo:</Text> Digite ou copie o
            código QR que foi gerado no campo acima.
          </Text>

          <Text
            style={{
              color: "#ccc",
              fontSize: 14,
              lineHeight: 20,
              marginBottom: 8
            }}
          >
            <Text style={{fontWeight: "600"}}>2° passo:</Text> Acesse sua
            plataforma de pagamento / internet banking.
          </Text>

          <Text
            style={{
              color: "#ccc",
              fontSize: 14,
              lineHeight: 20,
              marginBottom: 8
            }}
          >
            <Text style={{fontWeight: "600"}}>3° passo:</Text> Cole o código,
            confirme o valor e efetue o pagamento.
          </Text>

          <Text
            style={{color: "#ccc", fontSize: 12, lineHeight: 18, marginTop: 8}}
          >
            Pronto! Em alguns minutos o status da sua dívida será atualizado em
            nosso sistema.
          </Text>
        </View>

        {/* Timer */}
        <View style={{alignItems: "center", marginBottom: 24}}>
          <Text style={{color: "#ccc", fontSize: 14, marginBottom: 8}}>
            Você tem{" "}
            <Text style={{color: "#fff", fontWeight: "600"}}>
              {formatTime(timeLeft)} minutos
            </Text>{" "}
            para efetuar o pagamento.
          </Text>
        </View>

        <FullSizeButton
          text="Finalizar pagamento"
          onPress={handleFinishPayment}
        />
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PixPayment;
