import React, {useCallback} from "react";
import {StyleSheet, Text, TouchableOpacity, View} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import stylesConstants from "../../styles/styles-constants";
import styles from "../../styles/components/intro/overlay-top.style";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";

const OverlayTop: React.FC = () => {
    const {t} = useTranslation();
    const router = useRouter();

    const onLoginButtonPress = useCallback(() => {
        router.navigate("/login");
    }, [router.navigate]);

    return (
        <View style={styles.container}>
            <LinearGradient
                locations={[0.0666, 0.7481]}
                start={[0.5, 0]}
                end={[0.5, 1]}
                colors={[
                    stylesConstants.colors.introSlideGradient.end,
                    stylesConstants.colors.introSlideGradient.start,
                ]}
                style={StyleSheet.absoluteFill}
            >
                <TouchableOpacity style={styles.loginButton} onPress={onLoginButtonPress}>
                    <Text style={styles.loginButtonText}>{t("intro.loginButton")}</Text>
                </TouchableOpacity>
            </LinearGradient>
        </View>
    );
};

export default OverlayTop; 