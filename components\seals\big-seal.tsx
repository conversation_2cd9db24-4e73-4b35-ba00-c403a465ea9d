import {ImageSource} from "expo-image";
import React from "react";
import {LinearGradient} from "expo-linear-gradient";
import styles from "../../styles/components/seals/big-seal.style";
import SmallSeal from "./small-seal";

export interface BigSealProps {
    source: ImageSource;
}

const BigSeal: React.FC<BigSealProps> = (props) => {
    return (
        <LinearGradient
            colors={["rgba(255,255,255,0)", "rgba(255,255,255,0.15)"]}
            locations={[0.6, 1]}
            start={{x: 0.5, y: 0}}
            end={{x: 0.5, y: 1}}
            style={[styles.container]}
        >
            <SmallSeal source={props.source} />
        </LinearGradient>
    );
};

export default BigSeal;
