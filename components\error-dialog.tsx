import React, {useCallback, useEffect} from "react";
import {Text, TouchableOpacity, View} from "react-native";
import {ErrorType, useErrorMessage} from "../contexts/error-dialog-context";
import styles from "../styles/components/error-dialog.style";
import {LinearGradient} from "expo-linear-gradient";
import stylesConstants from "../styles/styles-constants";
import WarningIcon from "./icons/warning-icon";
import CloseIcon from "./icons/close-icon";
import ErrorIcon from "./icons/error-icon";
import Animated, {Easing, ReduceMotion, useAnimatedStyle, useSharedValue, withTiming} from "react-native-reanimated";

const ErrorDialog: React.FC = () => {
    const error = useErrorMessage();
    const messagePositionAnimation = useSharedValue(-100);

    const messagePositionAnimationStyle = useAnimatedStyle(() => ({
        top: messagePositionAnimation.value
    }));

    useEffect(() => {
        if(error.currentError) {
            messagePositionAnimation.value = withTiming(30, {
                duration: 300,
                easing: Easing.bezier(0.32, 0, 0.26, 1.60),
                reduceMotion: ReduceMotion.System,
            });
        } else {
            messagePositionAnimation.value = -100;
        }
    }, [error.currentError]);

    const onCloseError = useCallback(() => {
        error.cleanError();
    }, [error.cleanError]);

    if(!error.currentError) {
        return null;
    }

    return (
        <Animated.View style={[styles.container, messagePositionAnimationStyle]}>
            <LinearGradient
                colors={[
                    stylesConstants.colors.error.backgroundGradientStart,
                    stylesConstants.colors.error.backgroundGradientEnd
                ]}
                start={[0, 0]}
                style={styles.linear}
                end={[0, 1]}>
                <View style={styles.iconsContainer}>
                    {error.currentError?.errorType == ErrorType.Warning ? (<WarningIcon style={styles.icon}/>) : (
                        <ErrorIcon style={styles.icon}/>)}
                    <TouchableOpacity style={styles.closeButton} onPress={onCloseError}><CloseIcon/></TouchableOpacity>
                </View>
                <View>
                    <Text style={[styles.text, styles.title]}>{error.currentError?.title}</Text>
                    <Text style={styles.text}>{error.currentError?.description}</Text>
                </View>
            </LinearGradient>
        </Animated.View>
    );
};

export default ErrorDialog;