import React from "react";
import { render, fireEvent } from "@testing-library/react-native";

jest.mock("../../styles/components/input-field.style", () => ({
  container: {},
  label: {},
  field: {},
  inputText: {}
}));

jest.mock("../../styles/styles-constants", () => ({ colors: { gray100: "#ccc" } }));

jest.mock("../../components/icons/eye-icon", () => () => null);
jest.mock("../../components/icons/disable-eye-icon", () => () => null);

import InputField from "../../components/input-field";

describe("InputField", () => {
  it("calls onChangeText with typed value", () => {
    const onChange = jest.fn();
    const { getByPlaceholderText } = render(<InputField label="Email" value="" placeholder="type" onChangeText={onChange} />);
    fireEvent(getByPlaceholderText("type"), "change", { nativeEvent: { text: "abc" } });
    expect(onChange).toHaveBeenCalledWith("abc");
  });

  it("masks password characters", () => {
    const { getByPlaceholderText } = render(<InputField label="Pass" value="abc" placeholder="p" onChangeText={jest.fn()} isPassword />);
    const input = getByPlaceholderText("p");
    expect(input.props.secureTextEntry).toBe(true);
  });
});
