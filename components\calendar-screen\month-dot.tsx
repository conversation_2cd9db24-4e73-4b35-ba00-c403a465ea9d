import React from "react";
import {Text, TouchableOpacity} from "react-native";
import useMonth from "../../hooks/use-month";
import styles from "../../styles/components/calendar-screen/month-dots.style"

export interface MonthDotProps {
    month: number;
    onPress?: () => void;
    active?: boolean;
}

const MonthDot: React.FC<MonthDotProps> = (props) => {
    const monthName = useMonth(props.month);

    return (
        <TouchableOpacity style={[styles.container, props.active && styles.active]} onPress={props.onPress}>
            <Text style={[styles.text, props.active && styles.active]}>{monthName}</Text>
        </TouchableOpacity>
    );
}

export default MonthDot;