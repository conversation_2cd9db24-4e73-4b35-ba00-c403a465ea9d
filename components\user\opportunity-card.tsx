import React, {useMemo, useState} from "react";
import {View, Text} from "react-native";
import {Image} from "expo-image";
import {useTranslation} from "react-i18next";
import stylesConstants from "../../styles/styles-constants";
import Divider from "../divider";
import Button from "../button";
import styles from "../../styles/components/user/opportunity-card.style";
import Avatar from "./avatar";
import useCurrentLocalization from "../../hooks/use-localization";

export interface OpportunityCardProps {
    avatarUrl: string;
    userName: string;
    description: string;
    imageUrl?: string;
    value: number;
    createdAt?: Date;
}

const OpportunityCard: React.FC<OpportunityCardProps> = (props) => {
    const {t} = useTranslation();
    const localization = useCurrentLocalization();
    const [isExpanded, setIsExpanded] = useState(false);

    const value = useMemo(() => {
        return props.value
            ? (props.value / 100).toLocaleString(localization, {
                  maximumFractionDigits: 2
              })
            : 0;
    }, [props.value, localization]);

    const truncatedDescription = useMemo(() => {
        return props.description.length > 150
            ? props.description.substring(0, 150) + "..."
            : props.description;
    }, [props.description]);

    const handleSeeMore = () => {
        setIsExpanded(!isExpanded);
    };

    return (
        <View style={styles.container}>
            <Avatar size={32} borderSize={2} url={props.avatarUrl} />
            <View style={styles.content}>
                <View style={styles.header}>
                    <Text style={styles.name}>{props.userName}</Text>
                    <Text style={styles.label}>
                        {t("user.publishedOpportunity")}
                    </Text>
                    {props.createdAt && (
                        <Text style={styles.date}>
                            {new Date(props.createdAt).toLocaleDateString()}
                            {", às "}
                            {new Date(props.createdAt).toLocaleTimeString()}
                        </Text>
                    )}
                </View>
                <View>
                    <Text style={styles.description}>
                        {isExpanded ? props.description : truncatedDescription}
                        {props.description.length > 150 && (
                            <Text
                                onPress={handleSeeMore}
                                style={styles.seeMoreText}
                            >
                                {isExpanded
                                    ? ` ${t("user.seeLess")}`
                                    : ` ${t("user.seeMore")}`}
                            </Text>
                        )}
                    </Text>
                    <Image style={styles.image} source={props.imageUrl} />
                    <Divider />
                    <View style={styles.footer}>
                        <View style={styles.valueContainer}>
                            <Text style={styles.description}>
                                {t("user.investmentValue")}
                            </Text>
                            <Text style={styles.value}>
                                {t("user.price", {
                                    price: value
                                })}
                            </Text>
                        </View>
                        <Button
                            text={t("user.seeDetals")}
                            backgroundColor={"transparent"}
                            borderColor={stylesConstants.colors.gray25}
                            style={styles.viewOpportunityButton}
                        />
                    </View>
                </View>
            </View>
        </View>
    );
};

export default OpportunityCard;
