import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import styles from "@/styles/events/event-sale.style";
import Badge from "../../components/badge";
import AnnounceIcon from "../../components/icons/announce-icon";
import CartPlusIcon from "../../components/cart-plus-icon";
import stylesConstants from "@/styles/styles-constants";

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  address: string;
  organizer: string;
  type: "Online" | "Presencial";
  price: number;
  isFree: boolean;
  attendees: number;
  additionalInfo: string;
}

const EventSale: React.FC = () => {
  // Mock event data - in real app this would come from API/params
  const [event] = useState<Event>({
    id: "1",
    title: "Encontro anual de empreendedores em Balneário Camboriú - SC",
    description:
      "Lorem ipsum quisque lobortis in eu rhoncus dui nulla lectus sagittis dictum dignissim...",
    date: "14/05/2025",
    time: "19:30 PM - 21:30 PM",
    location: "Expocentro Balneário Camboriú",
    address: "Rua 2300, 300 - Balneário Camboriú, SC",
    organizer: "Sheep Digital Marketing",
    type: "Online", // Changed to Online to match the design
    price: 0.0, // Changed to 0 to show free event
    isFree: true, // Changed to true to show free event version
    attendees: 500,
    additionalInfo: "É necessário o crachá para acessar o local."
  });

  // Badge colors for free event
  const freeBadgeColor = {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderColor: stylesConstants.colors.brand.primary,
    textColor: stylesConstants.colors.fullWhite
  };

  const handlePurchase = () => {
    // Handle purchase logic
    console.log("Purchase event:", event.id);
  };

  const handleAddToCart = () => {
    // Handle add to cart logic
    console.log("Add to cart:", event.id);
  };

  const handleInviteMembers = () => {
    // Handle invite members logic
    console.log("Invite members to event:", event.id);
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.contentContainer}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <Text style={styles.headerTitle}>EVENTO</Text>
          <View style={styles.priceContainer}>
            <Text style={styles.priceText}>
              R$ {event.price.toFixed(2).replace(".", ",")}
            </Text>
            <TouchableOpacity onPress={handleAddToCart}>
              <CartPlusIcon />
            </TouchableOpacity>
          </View>
        </View>

        {/* Event Card */}
        <View style={styles.eventCard}>
          {/* Event icon centered */}
          <View style={styles.eventIconContainer}>
            <AnnounceIcon
              replaceColor={stylesConstants.colors.fullWhite}
              width={32}
              height={32}
            />
          </View>

          {/* Event title */}
          <Text style={styles.eventTitle}>{event.title}</Text>

          {/* Event description */}
          <View style={styles.eventDescriptionContainer}>
            <Text style={styles.eventDescription}>
              {event.description}{" "}
              <Text style={styles.seeMoreText}>Ver mais</Text>
            </Text>
          </View>

          {/* Free event badge */}
          {event.isFree && (
            <View style={styles.freeEventBadgeContainer}>
              <Badge color={freeBadgeColor} text="EVENTO GRATUITO" />
            </View>
          )}

          {/* Purchase button */}
          <TouchableOpacity
            style={styles.purchaseButton}
            onPress={handlePurchase}
          >
            <Text style={styles.purchaseButtonText}>
              Adquira por: R$ {event.price.toFixed(2).replace(".", ",")}
            </Text>
          </TouchableOpacity>

          {/* Attendees info */}
          <View style={styles.attendeesContainer}>
            <Text style={styles.attendeesText}>
              {event.attendees} pessoas marcaram presença nesse evento.
            </Text>
            <TouchableOpacity
              style={styles.inviteButton}
              onPress={handleInviteMembers}
            >
              <Text style={styles.inviteButtonText}>Convidar membro(s)</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Event Details */}
        <View style={styles.eventDetailsContainer}>
          <Text style={styles.sectionTitle}>Detalhes do evento</Text>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Sediado por</Text>
            <Text style={styles.eventDetailValue}>{event.organizer}</Text>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Tipo de evento</Text>
            <View style={styles.eventTypeContainer}>
              <View style={styles.eventTypeIndicator} />
              <Text style={styles.eventDetailValue}>{event.type}</Text>
            </View>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Data do evento</Text>
            <Text style={styles.eventDetailValue}>{event.date}</Text>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Horários do evento</Text>
            <Text style={styles.eventDetailValue}>{event.time}</Text>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Local do evento</Text>
            <Text style={styles.eventDetailValue}>{event.location}</Text>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Informações adicionais</Text>
            <Text style={styles.eventDetailValue}>{event.additionalInfo}</Text>
          </View>
        </View>

        {/* Payment Methods - Only show for paid events */}
        {!event.isFree && (
          <View style={styles.paymentMethodsContainer}>
            <Text style={styles.sectionTitle}>Métodos de pagamento</Text>
            <View style={styles.paymentMethodsGrid}>
              <View style={styles.paymentMethodIcon}>
                <View style={styles.paymentMethodCard} />
              </View>
              <View style={styles.paymentMethodIcon}>
                <View style={styles.paymentMethodCard} />
              </View>
              <View style={styles.paymentMethodIcon}>
                <View style={styles.paymentMethodCard} />
              </View>
              <View style={styles.paymentMethodIcon}>
                <View style={styles.paymentMethodCard} />
              </View>
              <View style={styles.paymentMethodIcon}>
                <View style={styles.paymentMethodCard} />
              </View>
            </View>
            <Text style={styles.installmentText}>
              Opções de parcelamento (parcele em até 12x sem juros).
            </Text>
          </View>
        )}

        {/* Similar Events */}
        <View style={styles.similarEventsContainer}>
          <Text style={styles.sectionTitle}>Eventos semelhantes</Text>

          <View style={styles.similarEventCard}>
            <View style={styles.similarEventBadges}>
              <Badge color={freeBadgeColor} text="TECNOLOGIA" />
              <Badge color={freeBadgeColor} text="EVENTO VIP" />
              <Badge color={freeBadgeColor} text="ECONOMIZE 50%" />
            </View>
            <Text style={styles.similarEventTitle}>
              Workshop de Planejamento e Modelagem de Negócios
            </Text>
            <View style={styles.similarEventPricing}>
              <View>
                <Text style={styles.similarEventOldPrice}>De: R$ 1.200,00</Text>
                <Text style={styles.similarEventPrice}>Por: R$ 600,00</Text>
              </View>
              <TouchableOpacity style={styles.similarEventButton}>
                <Text style={styles.similarEventButtonText}>VER EVENTO</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Purchase Bar */}
      <View style={styles.bottomPurchaseBar}>
        <TouchableOpacity
          style={styles.cartIconButton}
          onPress={handleAddToCart}
        >
          <Text style={styles.cartIconText}>🛒</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.bottomPurchaseButton}
          onPress={handlePurchase}
        >
          <Text style={styles.bottomPurchaseButtonText}>
            {event.isFree
              ? "Comprar agora por R$ 0,00"
              : `Comprar agora por R$ ${event.price
                  .toFixed(2)
                  .replace(".", ",")}`}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default EventSale;
