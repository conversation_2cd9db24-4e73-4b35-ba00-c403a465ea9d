import React, {useCallback, useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import {ScrollView, Text, TouchableOpacity} from "react-native";
import CalendarEventList, {
  CalendarEventData
} from "../../components/calendar-screen/calendar-event-list";
import Day from "../../components/calendar-screen/day";
import MonthDot from "../../components/calendar-screen/month-dot";
import EventIndicatorDots from "../../components/calendar-screen/event-indicator-dots";
import ScreenWithHeader from "../../components/screen-with-header";
import Search from "../../components/search";
import {useBottomModal} from "../../contexts/bottom-modal-context";
import styles from "../../styles/logged-stack/calendar.style";
import stylesConstants from "../../styles/styles-constants";
import CalendarIcon from "../../components/icons/calendar-icon";
import YearModal from "../../components/modals/year-modal";

const Calendar: React.FC = () => {
  const [year, setYear] = useState<number>(new Date().getFullYear());
  const [month, setMonth] = useState<number>(0);
  const [finalDate, setFinalDate] = useState<Date>(new Date(year, month, 1));
  const [searchTerm, setSearchTerm] = useState<string>("");

  const {t} = useTranslation();
  const modal = useBottomModal();

  // Sample event data - in real app this would come from API
  const sampleEvents: CalendarEventData[] = useMemo(
    () => [
      {
        id: "1",
        title:
          "Reunião de alinhamento para oportunidade de negócios na Praia Brava - SC",
        description: "Lorem ipsum quisque lobortis in eu rhoncus dui nulla...",
        location: "Praia dos Amores - Santa Catarina",
        startTime: "08:00 AM",
        endTime: "12:30 PM",
        isPrivate: true,
        isFree: true,
        isAttending: true,
        borderColor: stylesConstants.colors.alert400
      },
      {
        id: "2",
        title: "Encontro anual de empreendedores em Balneário Camboriú - SC",
        description: "Lorem ipsum quisque lobortis in eu rhoncus dui nulla...",
        location: "Centreventos Itajaí - SC",
        startTime: "13:00 PM",
        endTime: "14:15 PM",
        isPrivate: false,
        isFree: true,
        isAttending: true,
        borderColor: stylesConstants.colors.alert400
      }
    ],
    []
  );

  // For demo purposes, show events or empty state based on search
  const eventsToShow = searchTerm.length > 0 ? [] : sampleEvents;

  const handleOnChangeYear = useCallback(
    (newYear: number) => setYear(newYear),
    []
  );

  const handleYearHeaderPress = useCallback(() => {
    modal.openModal?.({
      title: t("calendar.yearModal.title"),
      children: <YearModal onChangeYear={handleOnChangeYear} year={year} />
    });
  }, [modal, t, year]);

  const handleMonthPress = useCallback(
    (month: number) => () => setMonth(month),
    []
  );

  const yearHeader = useMemo(
    () => (
      <TouchableOpacity
        style={styles.rightHeaderYearButton}
        onPress={handleYearHeaderPress}
      >
        <Text style={styles.rightHeaderYearText}>{year}</Text>
        <CalendarIcon width={16} height={16} />
      </TouchableOpacity>
    ),
    [handleYearHeaderPress, year]
  );

  const months = useMemo(() => Array(12).fill(0), []);
  const days = useMemo(() => {
    const daysLength = new Date(year, month + 1, 0).getDate();
    return Array(daysLength).fill(0);
  }, [year, month]);

  const isSelectedDate = useCallback(
    (day: number) =>
      finalDate.getTime() == new Date(year, month, day).getTime(),
    [finalDate, month, year]
  );

  const handleDayPress = useCallback(
    (newDate: Date) => setFinalDate(newDate),
    []
  );

  const handleSearchChange = useCallback(
    (value: string) => setSearchTerm(value),
    []
  );

  return (
    <ScreenWithHeader
      screenTitle={t("calendar.title")}
      backButton={true}
      rightHeaderChild={yearHeader}
    >
      <ScrollView
        style={styles.monthScroll}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.monthList}
        nestedScrollEnabled
      >
        {months.map((_, index) => (
          <MonthDot
            key={index + 1}
            month={index + 1}
            active={index == month}
            onPress={handleMonthPress(index)}
          />
        ))}
      </ScrollView>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        nestedScrollEnabled
        style={styles.dayScroll}
        contentContainerStyle={styles.dayList}
      >
        {days.map((_, index) => (
          <Day
            key={`day-${year}-${month}-${index + 1}`}
            date={new Date(year, month, index + 1)}
            onPress={handleDayPress}
            dots={[stylesConstants.colors.alert400]}
            selected={isSelectedDate(index + 1)}
          />
        ))}
      </ScrollView>
      <EventIndicatorDots />
      <Text style={styles.eventsTodayText}>{t("calendar.eventsToday")}</Text>
      <Search
        searchBarValue={searchTerm}
        onSearchBarChange={handleSearchChange}
        style={styles.search}
      />
      <CalendarEventList
        events={eventsToShow}
        onExploreEvents={() => console.log("Explore events pressed")}
      />
    </ScreenWithHeader>
  );
};

export default Calendar;
