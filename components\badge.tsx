import React, {Children, cloneElement, isValidElement, useMemo} from "react";
import {ColorValue, Text, View} from "react-native";
import styles from "../styles/components/badge.style";

export interface BadgeColor {
    borderColor: ColorValue;
    backgroundColor: ColorValue;
    textColor: ColorValue;
}

export interface BadgeProps {
    text: string;
    color: BadgeColor;
    icon?: React.ReactNode;
}

const Badge: React.FC<BadgeProps> = (props) => {
    const iconProps = useMemo(() => ({width: 12, height: 12}), []);

    return (
        <View style={[
            styles.container,
            {backgroundColor: props.color.backgroundColor, borderColor: props.color.borderColor}]
        }>
            {Children.map(props.icon, (child) => {
                if(isValidElement(child)) {
                    return cloneElement(child, iconProps);
                }

                return child;
            })}
            <Text style={[styles.text, {color: props.color.textColor}]}>{props.text}</Text>
        </View>
    )
};

export default Badge;