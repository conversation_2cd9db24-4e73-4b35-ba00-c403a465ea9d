import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        display: "flex",
        flexDirection: "row",
        columnGap: 6,
        paddingVertical: 8,
        paddingHorizontal: 12,
        justifyContent: "center",
        alignItems: "center",
        gap: 6,
        alignSelf: "stretch",
        borderRadius: 8,
        width: 136,
        height: 36
    },
    containerBackground: {
        borderWidth: 1,
        borderStyle: "solid",
        borderColor: stylesConstants.colors.brand.primary,
        backgroundColor: stylesConstants.colors.brand.primary
    },
    text: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontStyle: "normal",
        fontWeight: 700,
        lineHeight: 18
    },
    innerContainer: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        gap: 6
    }
});

export default styles;
