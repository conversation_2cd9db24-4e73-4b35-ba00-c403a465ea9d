import {View, Text} from "react-native";
import Divider from "../divider";
import OpenProductButton from "./open-product-button";
import ProductPrice from "./product-price";
import Pill from "../pill";
import BookIcon from "../icons/book-icon";
import stylesConstants from "../../styles/styles-constants";
import styles from "../../styles/components/products/recomended-product-card.style";
import React from "react";

export interface RecommendedProductCardProps {
    productId: number;
    title: string;
    description: string;
    price: number;
    icon?: React.ReactNode;
}

const RecommendedProductCard: React.FC<RecommendedProductCardProps> = (props) => {
    return (
        <View style={styles.container}>
            <View style={styles.pillsContainer}>
                <Pill
                    text="E-BOOK"
                    icon={
                        <BookIcon
                            stroke={stylesConstants.colors.gray700}
                            width={12}
                            height={12}
                        />
                    }
                />
                <Pill
                    text="PRODUTO GRATUITO"
                    backgroundColor={stylesConstants.colors.success50}
                    textColor={stylesConstants.colors.success700}
                    borderColor={stylesConstants.colors.success200}
                />
            </View>
            <View style={styles.contentContainer}>
                <View style={styles.image}>{props.icon}</View>
                <View style={styles.titleContainer}>
                    <Text style={styles.title}>{props.title}</Text>
                </View>
            </View>
            <View>
                <Text style={styles.description}>{props.description}</Text>
            </View>
            <Divider />
            <View style={styles.footer}>
                <ProductPrice price={props.price} />
                <View style={styles.footerButton}>
                    <OpenProductButton productId={props.productId} />
                </View>
            </View>
        </View>
    );
};

export default RecommendedProductCard;
