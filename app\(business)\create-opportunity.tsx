import React, {useState, useCallback} from "react";
import {Text, View, ScrollView, TouchableOpacity, Alert} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import InputField from "../../components/input-field";
import FullSizeButton from "../../components/full-size-button";
import InvisibleFullSizeButton from "../../components/invisible-full-size-button";
import styles from "../../styles/business/create-opportunity.style";
import CameraIcon from "../../components/icons/camera-icon";
import MarkerPinIcon from "../../components/icons/marker-pin-icon";
import ChevronDownIcon from "../../components/icons/chevron-down-icon";
import DollarIcon from "../../components/icons/dollar-icon";
import {router} from "expo-router";

const segments = [
  {id: "tech", label: "Tecnologia"},
  {id: "marketing", label: "Marketing"},
  {id: "sales", label: "Vendas"},
  {id: "finance", label: "Finanças"},
  {id: "design", label: "Design"},
  {id: "other", label: "Outros"}
];

const stages = [
  {id: "idea", label: "Ideia"},
  {id: "planning", label: "Planejamento"},
  {id: "development", label: "Desenvolvimento"},
  {id: "testing", label: "Testes"},
  {id: "launch", label: "Lançamento"},
  {id: "growth", label: "Crescimento"}
];

const CreateOpportunity: React.FC = () => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    value: "",
    segment: "",
    currentStage: "",
    targetMarket: "",
    location: "Praia Brava, Itajaí - SC",
    featuredImage: null as string | null
  });

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
    };

  const handleImageUpload = useCallback(() => {
    Alert.alert("Selecionar foto destaque", "Escolha uma opção", [
      {
        text: "Câmera",
        onPress: () => console.log("Open camera")
      },
      {
        text: "Galeria",
        onPress: () => console.log("Open gallery")
      },
      {
        text: "Cancelar",
        style: "cancel"
      }
    ]);
  }, []);

  const handleSegmentSelect = useCallback(() => {
    Alert.alert("Selecione um segmento", "", [
      ...segments.map((segment) => ({
        text: segment.label,
        onPress: () => handleInputChange("segment")(segment.id)
      })),
      {
        text: "Cancelar",
        style: "cancel" as const
      }
    ]);
  }, [handleInputChange]);

  const handleStageSelect = useCallback(() => {
    Alert.alert("Selecione o estágio", "", [
      ...stages.map((stage) => ({
        text: stage.label,
        onPress: () => handleInputChange("currentStage")(stage.id)
      })),
      {
        text: "Cancelar",
        style: "cancel" as const
      }
    ]);
  }, [handleInputChange]);

  const getSelectedSegmentLabel = () => {
    const selected = segments.find((s) => s.id === formData.segment);
    return selected ? selected.label : "Selecione um segmento";
  };

  const getSelectedStageLabel = () => {
    const selected = stages.find((s) => s.id === formData.currentStage);
    return selected ? selected.label : "Selecione o estágio";
  };

  const dollarIcon = useCallback(() => <DollarIcon />, []);

  const handleGoToPayment = useCallback(() => {
    // Basic validation
    if (!formData.title.trim()) {
      Alert.alert("Erro", "Por favor, insira um título para a oportunidade.");
      return;
    }

    if (!formData.segment) {
      Alert.alert("Erro", "Por favor, selecione um segmento.");
      return;
    }

    if (!formData.currentStage) {
      Alert.alert("Erro", "Por favor, selecione o estágio atual.");
      return;
    }

    // Navigate to payment selection with opportunity data
    const opportunityData = {
      ...formData,
      createdAt: new Date().toISOString()
    };

    router.push(
      `/(business)/payment-selection?opportunityData=${encodeURIComponent(
        JSON.stringify(opportunityData)
      )}`
    );
  }, [formData]);

  return (
    <ScreenWithHeader screenTitle="Criar nova oportunidade" backButton>
      <ScrollView style={styles.contentContainer}>
        {/* Image Upload Section */}
        <TouchableOpacity
          style={styles.imageUploadContainer}
          onPress={handleImageUpload}
        >
          <CameraIcon />
          <Text style={styles.imageUploadText}>Selecionar foto destaque</Text>
          <Text style={styles.imageUploadSubtext}>(opcional)</Text>
        </TouchableOpacity>

        <View style={styles.formContainer}>
          {/* Title Field */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Título</Text>
            <InputField
              value={formData.title}
              onChangeText={handleInputChange("title")}
              placeholder="Insira o título da oportunidade"
              maxLength={150}
              showCharacterCount={true}
            />
          </View>

          {/* Description Field */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Descrição</Text>
            <InputField
              value={formData.description}
              onChangeText={handleInputChange("description")}
              placeholder="Insira uma descrição (opcional)"
              multiline={true}
              numberOfLines={4}
              maxLength={250}
              showCharacterCount={true}
              style={styles.textArea}
            />
          </View>

          {/* Value Field */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Valor da oportunidade</Text>
            <InputField
              value={formData.value}
              onChangeText={handleInputChange("value")}
              placeholder="Insira o valor da oportunidade (opc.)"
              icon={dollarIcon}
              inputMode="numeric"
            />
          </View>

          {/* Segment Picker */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Segmento</Text>
            <TouchableOpacity
              style={styles.pickerContainer}
              onPress={handleSegmentSelect}
            >
              <Text
                style={[
                  styles.pickerText,
                  !formData.segment && styles.pickerPlaceholder
                ]}
              >
                {getSelectedSegmentLabel()}
              </Text>
              <ChevronDownIcon />
            </TouchableOpacity>
          </View>

          {/* Current Stage Picker */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Estágio atual</Text>
            <TouchableOpacity
              style={styles.pickerContainer}
              onPress={handleStageSelect}
            >
              <Text
                style={[
                  styles.pickerText,
                  !formData.currentStage && styles.pickerPlaceholder
                ]}
              >
                {getSelectedStageLabel()}
              </Text>
              <ChevronDownIcon />
            </TouchableOpacity>
          </View>

          {/* Target Market Field */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Mercado alvo</Text>
            <InputField
              value={formData.targetMarket}
              onChangeText={handleInputChange("targetMarket")}
              placeholder="Insira o mercado que deseja atingir"
            />
          </View>

          {/* Location Field */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Localização</Text>
            <TouchableOpacity style={styles.locationContainer}>
              <MarkerPinIcon />
              <Text style={styles.locationText}>{formData.location}</Text>
              <ChevronDownIcon />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <FullSizeButton
            text="Ir para o pagamento"
            onPress={handleGoToPayment}
          />
          <InvisibleFullSizeButton
            text="Cancelar"
            onPress={() => router.back()}
          />
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default CreateOpportunity;
