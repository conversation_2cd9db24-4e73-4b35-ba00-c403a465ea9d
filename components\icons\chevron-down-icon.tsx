import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface ChevronDownIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const ChevronDownIcon: React.FC<ChevronDownIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#FCFCFD", [props.replaceColor]);

    return (
        <Svg
            width={20}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 7.5l5 5 5-5"
            />
        </Svg>
    );
};

export default ChevronDownIcon;
