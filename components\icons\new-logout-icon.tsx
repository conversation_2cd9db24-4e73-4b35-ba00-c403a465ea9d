import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface NewLogoutIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const NewLogoutIcon: React.FC<NewLogoutIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#F97066",
    [props.replaceColor]
  );

  return (
    <Svg width={18} height={18} viewBox="0 0 18 18" fill="none" {...props}>
      <Path
        d="M12.3333 13.1663L16.5 8.99961M16.5 8.99961L12.3333 4.83294M16.5 8.99961H6.5M9 13.1663C9 13.4126 9 13.5357 8.99085 13.6424C8.89569 14.7512 8.07905 15.6636 6.98753 15.8807C6.88252 15.9015 6.76001 15.9152 6.51529 15.9424L5.66412 16.0369C4.3854 16.179 3.74601 16.25 3.23805 16.0875C2.56078 15.8708 2.00785 15.3759 1.71765 14.7267C1.5 14.2398 1.5 13.5965 1.5 12.3099V5.68935C1.5 4.40274 1.5 3.75944 1.71765 3.27254C2.00785 2.62334 2.56078 2.12844 3.23805 1.91171C3.74601 1.74916 4.38538 1.8202 5.66412 1.96229L6.51529 2.05686C6.7601 2.08406 6.8825 2.09766 6.98753 2.11854C8.07905 2.33557 8.89569 3.24797 8.99085 4.35678C9 4.46347 9 4.58662 9 4.83294"
        stroke={color}
        strokeWidth={1.66667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default NewLogoutIcon;
