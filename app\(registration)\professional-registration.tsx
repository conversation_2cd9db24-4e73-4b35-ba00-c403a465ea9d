import React, {useState, useCallback} from "react";
import {Text, View, ColorValue} from "react-native";
import Screen from "../../components/screen";
import BackgroundLogoTexture from "../../components/logos/background-logo-texture";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";
import BuildingIcon from "../../components/icons/building-icon";

const ProfessionalRegistration: React.FC = () => {
  const {t} = useTranslation();
  const [formData, setFormData] = useState({
    company: "",
    position: "",
    industry: "",
    experience: ""
  });

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
    };

  const buildingIcon = useCallback(
    (errorColor?: ColorValue) => <BuildingIcon replaceColor={errorColor} />,
    []
  );

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={{flex: 1, padding: 20}}>
          <BackButton />
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#fff",
              textAlign: "center",
              marginBottom: 20
            }}
          >
            {t("professionalRegistration.title", "Dados Profissionais")}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "#fff",
              textAlign: "center",
              marginBottom: 30
            }}
          >
            {t(
              "professionalRegistration.description",
              "Conte-nos sobre sua carreira"
            )}
          </Text>

          <View style={{gap: 15, marginBottom: 30}}>
            <InputField
              label={t("professionalRegistration.company", "Empresa")}
              value={formData.company}
              onChangeText={handleInputChange("company")}
              placeholder={t(
                "professionalRegistration.companyPlaceholder",
                "Nome da empresa"
              )}
              icon={buildingIcon}
            />
            <InputField
              label={t("professionalRegistration.position", "Cargo")}
              value={formData.position}
              onChangeText={handleInputChange("position")}
              placeholder={t(
                "professionalRegistration.positionPlaceholder",
                "Seu cargo atual"
              )}
            />
            <InputField
              label={t("professionalRegistration.industry", "Setor")}
              value={formData.industry}
              onChangeText={handleInputChange("industry")}
              placeholder={t(
                "professionalRegistration.industryPlaceholder",
                "Setor de atuação"
              )}
            />
            <InputField
              label={t("professionalRegistration.experience", "Experiência")}
              value={formData.experience}
              onChangeText={handleInputChange("experience")}
              placeholder={t(
                "professionalRegistration.experiencePlaceholder",
                "Anos de experiência"
              )}
            />
          </View>

          <FullSizeButton
            text={t("professionalRegistration.next", "Próximo")}
            onPress={() => {}}
          />
        </View>
      </Screen>
    </>
  );
};

export default ProfessionalRegistration;
