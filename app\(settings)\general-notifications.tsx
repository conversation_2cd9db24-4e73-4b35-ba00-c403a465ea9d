import React, {useState} from "react";
import {Text, View, ScrollView, Switch, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import styles from "@/styles/settings/general-notifications.style";

interface NotificationSettings {
  generalNotifications: boolean;
  pushNotifications: boolean;
  emailSmsNotifications: boolean;
  newsAndOffers: boolean;
}

const GeneralNotifications: React.FC = () => {
  const {t} = useTranslation();
  const [settings, setSettings] = useState<NotificationSettings>({
    generalNotifications: true,
    pushNotifications: true,
    emailSmsNotifications: true,
    newsAndOffers: false
  });

  const handleToggle =
    (key: keyof NotificationSettings) => (value: boolean) => {
      setSettings((prev) => ({
        ...prev,
        [key]: value
      }));
    };

  const handleRestoreDefaults = () => {
    setSettings({
      generalNotifications: true,
      pushNotifications: true,
      emailSmsNotifications: true,
      newsAndOffers: false
    });
  };

  return (
    <ScreenWithHeader
      screenTitle={t(
        "notifications.generalNotifications",
        "Notificações gerais"
      )}
      backButton
      disableScrollView
    >
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.notificationsSection}>
          {/* Notificações gerais */}
          <View style={styles.notificationItem}>
            <Text style={styles.notificationTitle}>
              {t("notifications.generalNotifications", "Notificações gerais")}
            </Text>
            <View style={styles.notificationControl}>
              <Text style={styles.statusText}>
                {settings.generalNotifications ? "Habilitado" : "Desabilitado"}
              </Text>
              <Switch
                value={settings.generalNotifications}
                onValueChange={handleToggle("generalNotifications")}
                trackColor={{false: "#D0D5DD", true: "#1F9464"}}
                thumbColor="#FFFFFF"
                style={styles.switch}
              />
            </View>
          </View>

          {/* Notificações push */}
          <View style={styles.notificationItem}>
            <Text style={styles.notificationTitle}>
              {t("notifications.pushNotifications", "Notificações push")}
            </Text>
            <View style={styles.notificationControl}>
              <Text style={styles.statusText}>
                {settings.pushNotifications ? "Habilitado" : "Desabilitado"}
              </Text>
              <Switch
                value={settings.pushNotifications}
                onValueChange={handleToggle("pushNotifications")}
                trackColor={{false: "#D0D5DD", true: "#1F9464"}}
                thumbColor="#FFFFFF"
                style={styles.switch}
              />
            </View>
          </View>

          {/* Notificações por E-mail / SMS */}
          <View style={styles.notificationItem}>
            <Text style={styles.notificationTitle}>
              {t(
                "notifications.emailSmsNotifications",
                "Notificações por E-mail / SMS"
              )}
            </Text>
            <View style={styles.notificationControl}>
              <Text style={styles.statusText}>
                {settings.emailSmsNotifications ? "Habilitado" : "Desabilitado"}
              </Text>
              <Switch
                value={settings.emailSmsNotifications}
                onValueChange={handleToggle("emailSmsNotifications")}
                trackColor={{false: "#D0D5DD", true: "#1F9464"}}
                thumbColor="#FFFFFF"
                style={styles.switch}
              />
            </View>
          </View>

          {/* Notificar novidades */}
          <View style={styles.notificationItem}>
            <Text style={styles.notificationTitle}>
              {t(
                "notifications.newsAndOffers",
                "Notificar novidades (ofertas, alertas, etc.)"
              )}
            </Text>
            <View style={styles.notificationControl}>
              <Text style={styles.statusText}>
                {settings.newsAndOffers ? "Habilitado" : "Desabilitado"}
              </Text>
              <Switch
                value={settings.newsAndOffers}
                onValueChange={handleToggle("newsAndOffers")}
                trackColor={{false: "#D0D5DD", true: "#1F9464"}}
                thumbColor="#FFFFFF"
                style={styles.switch}
              />
            </View>
          </View>
        </View>

        {/* Restore defaults button */}
        <TouchableOpacity
          style={styles.restoreButton}
          onPress={handleRestoreDefaults}
        >
          <Text style={styles.restoreButtonText}>
            {t(
              "notifications.restoreDefaults",
              "Restaurar configurações padrão"
            )}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default GeneralNotifications;
