import translationPtBr from "@/locales/pt-br.json";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as Localization from "expo-localization";
import i18n, {Resource} from "i18next";
import {initReactI18next} from "react-i18next";

const resources: Resource = {
  "pt-BR": {
    translation: translationPtBr
  }
};

export async function getCurrentLocalization(): Promise<string> {
  return (
    (await AsyncStorage.getItem("language")) ??
    Localization.getLocales()[0].languageCode ??
    "pt-BR"
  );
}

async function initI18n(): Promise<void> {
  const savedLanguage = await getCurrentLocalization();

  await i18n.use(initReactI18next).init({
    compatibilityJSON: "v4",
    resources,
    lng: savedLanguage,
    fallbackLng: "pt-BR",
    interpolation: {
      escapeValue: false
    }
  });
}

initI18n().catch(console.error);

export default i18n;
