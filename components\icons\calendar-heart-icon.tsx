import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const CalendarHeartIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={20}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke="#F2F4F7"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.67}
                d="M17.5 8.334h-15m15 1.25v-2.25c0-1.4 0-2.1-.273-2.635a2.5 2.5 0 0 0-1.092-1.093c-.535-.272-1.235-.272-2.635-.272h-7c-1.4 0-2.1 0-2.635.272A2.5 2.5 0 0 0 2.772 4.7C2.5 5.233 2.5 5.934 2.5 7.334v7c0 1.4 0 2.1.272 2.635a2.5 2.5 0 0 0 1.093 1.092c.535.273 1.235.273 2.635.273h3.917m2.916-16.667V5M6.667 1.667V5m7.914 8.094c-.583-.65-1.555-.824-2.286-.23-.73.595-.833 1.589-.26 2.292.574.703 2.546 2.344 2.546 2.344s1.973-1.64 2.546-2.344c.574-.703.484-1.704-.26-2.292-.743-.588-1.703-.42-2.286.23Z"
            />
        </Svg>
    );
};

export default CalendarHeartIcon;