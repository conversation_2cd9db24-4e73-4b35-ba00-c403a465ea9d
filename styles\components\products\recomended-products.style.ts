import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        height: "100%"
    },
    header: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center"
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 14,
        fontWeight: "700",
        fontFamily: stylesConstants.fonts.openSans,
        lineHeight: 20
    },
    seeAllText: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 12,
        fontWeight: "600",
        fontFamily: stylesConstants.fonts.openSans,
        lineHeight: 18
    },
    listContainer: {
        marginTop: 12
    },
    listContentContainer: {
        flex: 1,
        gap: 16,
        paddingTop: 12
    },
    fullListContentContainer: {
        flexGrow: 1,
        paddingBottom: 16
    },
    headerContent: {
        gap: 24,
        marginBottom: 24
    },
    sectionHeader: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center"
    },
    itemSeparator: {
        height: 24
    },
    loadingFooter: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        paddingVertical: 20,
        gap: 8
    },
    loadingText: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 12,
        fontWeight: "400",
        fontFamily: stylesConstants.fonts.openSans
    },
    emptyContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        paddingVertical: 40,
        gap: 12
    },
    emptyText: {
        color: stylesConstants.colors.textPrimary,
        fontSize: 14,
        fontWeight: "400",
        fontFamily: stylesConstants.fonts.openSans,
        textAlign: "center"
    }
});

export default styles;
