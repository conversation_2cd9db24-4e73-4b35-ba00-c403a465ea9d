import {Platform, StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        borderStyle: "solid",
        borderColor: stylesConstants.colors.brand.primary,
        backgroundColor: stylesConstants.colors.brand.primary,
        alignSelf: "flex-start",
        padding: 10,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        gap: 8,
        ...Platform.select({
            android: {
                elevation: 1,
                shadowColor: stylesConstants.colors.introShadowColor,
            },
            ios: {
                shadowColor: stylesConstants.colors.introShadowColor,
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.05,
                shadowRadius: 2,
            }
        })
    },
    disabled: {
        opacity: 0.4
    }
});

export default styles;