1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="studio.takasaki.clubm"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:4:3-75
11-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b57352f806b028fc1a828519558f1ef3\transformed\expo.modules.image-2.2.0\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
16-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:5:3-69
16-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:5:20-67
17    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
17-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:6:3-71
17-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:6:20-69
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:7:3-63
18-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:7:20-61
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:8:3-78
19-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:8:20-76
20
21    <queries>
21-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:9:3-15:13
22        <intent>
22-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:10:5-14:14
23            <action android:name="android.intent.action.VIEW" />
23-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
23-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
24
25            <category android:name="android.intent.category.BROWSABLE" />
25-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
25-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
26
27            <data android:scheme="https" />
27-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
27-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
28        </intent>
29
30        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
30-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
30-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
31        <intent>
31-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
32            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
32-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
32-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
33        </intent>
34        <intent>
34-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\819f38e9bb15232d607c90f751fa5696\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
35
36            <!-- Required for opening tabs if targeting API 30 -->
37            <action android:name="android.support.customtabs.action.CustomTabsService" />
37-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\819f38e9bb15232d607c90f751fa5696\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
37-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\819f38e9bb15232d607c90f751fa5696\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
38        </intent>
39    </queries>
40    <!--
41  Allows Glide to monitor connectivity status and restart failed requests if users go from a
42  a disconnected to a connected network state.
43    -->
44    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
44-->[BareExpo:expo.modules.image:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b57352f806b028fc1a828519558f1ef3\transformed\expo.modules.image-2.2.0\AndroidManifest.xml:12:5-79
44-->[BareExpo:expo.modules.image:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b57352f806b028fc1a828519558f1ef3\transformed\expo.modules.image-2.2.0\AndroidManifest.xml:12:22-76
45    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
45-->[host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\748e760ebd3d0286b2ae0af0f7f9fdb7\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:5-76
45-->[host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\748e760ebd3d0286b2ae0af0f7f9fdb7\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:22-73
46    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
46-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:5-81
46-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:22-78
47    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
47-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:5-77
47-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:22-74
48    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
48-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
48-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
49    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
49-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
49-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
50
51    <permission
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\463a0a6d18bcedcdf0dcc1e5161e05be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
52        android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\463a0a6d18bcedcdf0dcc1e5161e05be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
53        android:protectionLevel="signature" />
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\463a0a6d18bcedcdf0dcc1e5161e05be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
54
55    <uses-permission android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\463a0a6d18bcedcdf0dcc1e5161e05be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\463a0a6d18bcedcdf0dcc1e5161e05be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
56    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
56-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdd9e257e4348dcb3490e729872feeff\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
56-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdd9e257e4348dcb3490e729872feeff\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
57    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
58    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
59    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
60    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
61    <!-- for Samsung -->
62    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
63    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
63-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
63-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
64    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
64-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
64-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
65    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
65-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
65-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
66    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
66-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
66-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
67    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
67-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
67-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
68    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
68-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
68-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
69    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
69-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
69-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
70    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
70-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
70-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
71    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
71-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
71-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
72    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
72-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
72-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
73    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
73-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
73-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
74    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
74-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
74-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
75    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
75-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
75-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
76    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
77    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
77-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
77-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\24bb5ac291ebb9f339add43035505c7d\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
78
79    <application
79-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:3-32:17
80        android:name="studio.takasaki.clubm.MainApplication"
80-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:16-47
81        android:allowBackup="true"
81-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:162-188
82        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\463a0a6d18bcedcdf0dcc1e5161e05be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
83        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
83-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:307-376
84        android:debuggable="true"
85        android:extractNativeLibs="false"
86        android:fullBackupContent="@xml/secure_store_backup_rules"
86-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:248-306
87        android:icon="@mipmap/ic_launcher"
87-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:81-115
88        android:label="@string/app_name"
88-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:48-80
89        android:roundIcon="@mipmap/ic_launcher_round"
89-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:116-161
90        android:supportsRtl="true"
90-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:221-247
91        android:theme="@style/AppTheme"
91-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:16:189-220
92        android:usesCleartextTraffic="true" >
92-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\debug\AndroidManifest.xml:6:18-53
93        <meta-data
93-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:17:5-83
94            android:name="expo.modules.updates.ENABLED"
94-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:17:16-59
95            android:value="false" />
95-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:17:60-81
96        <meta-data
96-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:18:5-105
97            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
97-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:18:16-80
98            android:value="ALWAYS" />
98-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:18:81-103
99        <meta-data
99-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:19:5-99
100            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
100-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:19:16-79
101            android:value="0" />
101-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:19:80-97
102
103        <activity
103-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:20:5-31:16
104            android:name="studio.takasaki.clubm.MainActivity"
104-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:20:15-43
105            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|locale|layoutDirection"
105-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:20:44-157
106            android:exported="true"
106-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:20:279-302
107            android:launchMode="singleTask"
107-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:20:158-189
108            android:screenOrientation="portrait"
108-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:20:303-339
109            android:theme="@style/Theme.App.SplashScreen"
109-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:20:233-278
110            android:windowSoftInputMode="adjustResize" >
110-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:20:190-232
111            <intent-filter>
111-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:21:7-24:23
112                <action android:name="android.intent.action.MAIN" />
112-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:22:9-60
112-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:22:17-58
113
114                <category android:name="android.intent.category.LAUNCHER" />
114-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:23:9-68
114-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:23:19-66
115            </intent-filter>
116            <intent-filter>
116-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:25:7-30:23
117                <action android:name="android.intent.action.VIEW" />
117-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
117-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
118
119                <category android:name="android.intent.category.DEFAULT" />
119-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
119-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
120                <category android:name="android.intent.category.BROWSABLE" />
120-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
120-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
121
122                <data android:scheme="clubm" />
122-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
122-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
123            </intent-filter>
124        </activity>
125        <activity
125-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
126            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
126-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
127            android:exported="true"
127-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
128            android:launchMode="singleTask"
128-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
129            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
129-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
130            <intent-filter>
130-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
131                <action android:name="android.intent.action.VIEW" />
131-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
131-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
132
133                <category android:name="android.intent.category.DEFAULT" />
133-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
133-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
134                <category android:name="android.intent.category.BROWSABLE" />
134-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
134-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
135
136                <data android:scheme="expo-dev-launcher" />
136-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
136-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
137            </intent-filter>
138        </activity>
139        <activity
139-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
140            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
140-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
141            android:screenOrientation="portrait"
141-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
142            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
142-->[:expo-dev-launcher] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
143        <activity
143-->[:expo-dev-menu] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
144            android:name="expo.modules.devmenu.DevMenuActivity"
144-->[:expo-dev-menu] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
145            android:exported="true"
145-->[:expo-dev-menu] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
146            android:launchMode="singleTask"
146-->[:expo-dev-menu] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
147            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
147-->[:expo-dev-menu] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
148            <intent-filter>
148-->[:expo-dev-menu] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
149                <action android:name="android.intent.action.VIEW" />
149-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
149-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
150
151                <category android:name="android.intent.category.DEFAULT" />
151-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
151-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
152                <category android:name="android.intent.category.BROWSABLE" />
152-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
152-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
153
154                <data android:scheme="expo-dev-menu" />
154-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
154-->C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
155            </intent-filter>
156        </activity>
157
158        <meta-data
158-->[:expo-modules-core] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
159            android:name="org.unimodules.core.AppLoader#react-native-headless"
159-->[:expo-modules-core] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
160            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
160-->[:expo-modules-core] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
161        <meta-data
161-->[:expo-modules-core] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
162            android:name="com.facebook.soloader.enabled"
162-->[:expo-modules-core] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
163            android:value="true" />
163-->[:expo-modules-core] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
164
165        <activity
165-->[com.facebook.react:react-android:0.79.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\70312fc86ee51e27d343045c10899056\transformed\react-android-0.79.3-debug\AndroidManifest.xml:19:9-21:40
166            android:name="com.facebook.react.devsupport.DevSettingsActivity"
166-->[com.facebook.react:react-android:0.79.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\70312fc86ee51e27d343045c10899056\transformed\react-android-0.79.3-debug\AndroidManifest.xml:20:13-77
167            android:exported="false" />
167-->[com.facebook.react:react-android:0.79.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\70312fc86ee51e27d343045c10899056\transformed\react-android-0.79.3-debug\AndroidManifest.xml:21:13-37
168
169        <provider
169-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
170            android:name="expo.modules.filesystem.FileSystemFileProvider"
170-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
171            android:authorities="studio.takasaki.clubm.FileSystemFileProvider"
171-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
172            android:exported="false"
172-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
173            android:grantUriPermissions="true" >
173-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
174            <meta-data
174-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:70
175                android:name="android.support.FILE_PROVIDER_PATHS"
175-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-67
176                android:resource="@xml/file_system_provider_paths" />
176-->[:expo-file-system] C:\Users\<USER>\eclipse-workspace\club-m-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-67
177        </provider>
178
179        <service
179-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:11:9-17:19
180            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
180-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:12:13-91
181            android:exported="false" >
181-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:13:13-37
182            <intent-filter android:priority="-1" >
182-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
182-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
183                <action android:name="com.google.firebase.MESSAGING_EVENT" />
183-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
183-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
184            </intent-filter>
185        </service>
186
187        <receiver
187-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:19:9-31:20
188            android:name="expo.modules.notifications.service.NotificationsService"
188-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:20:13-83
189            android:enabled="true"
189-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:21:13-35
190            android:exported="false" >
190-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:22:13-37
191            <intent-filter android:priority="-1" >
191-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:13-30:29
191-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:28-49
192                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
192-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:17-88
192-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:25-85
193                <action android:name="android.intent.action.BOOT_COMPLETED" />
193-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:17-79
193-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:25-76
194                <action android:name="android.intent.action.REBOOT" />
194-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:17-71
194-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:25-68
195                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
195-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:17-82
195-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:25-79
196                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
196-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:17-82
196-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:25-79
197                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
197-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:17-84
197-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:25-81
198            </intent-filter>
199        </receiver>
200
201        <activity
201-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:33:9-40:75
202            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
202-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:34:13-92
203            android:excludeFromRecents="true"
203-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:35:13-46
204            android:exported="false"
204-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:36:13-37
205            android:launchMode="standard"
205-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:37:13-42
206            android:noHistory="true"
206-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:38:13-37
207            android:taskAffinity=""
207-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:39:13-36
208            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
208-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:40:13-72
209
210        <receiver
210-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
211            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
211-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
212            android:exported="true"
212-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
213            android:permission="com.google.android.c2dm.permission.SEND" >
213-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
214            <intent-filter>
214-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
215                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
215-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
215-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
216            </intent-filter>
217
218            <meta-data
218-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
219                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
219-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
220                android:value="true" />
220-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
221        </receiver>
222        <!--
223             FirebaseMessagingService performs security checks at runtime,
224             but set to not exported to explicitly avoid allowing another app to call it.
225        -->
226        <service
226-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
227            android:name="com.google.firebase.messaging.FirebaseMessagingService"
227-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
228            android:directBootAware="true"
228-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
229            android:exported="false" >
229-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
230            <intent-filter android:priority="-500" >
230-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
230-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
231                <action android:name="com.google.firebase.MESSAGING_EVENT" />
231-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
231-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77946c4d60f2546b2257f2a5e87b04ac\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
232            </intent-filter>
233        </service>
234        <service
234-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
235            android:name="com.google.firebase.components.ComponentDiscoveryService"
235-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
236            android:directBootAware="true"
236-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
237            android:exported="false" >
237-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
238            <meta-data
238-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
239                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
239-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
241            <meta-data
241-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
242                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
242-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb4a722f45049818a6604f4846572245\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
244            <meta-data
244-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec71fbc01098ab24713fa9eb061f29ee\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
245                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
245-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec71fbc01098ab24713fa9eb061f29ee\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec71fbc01098ab24713fa9eb061f29ee\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
247            <meta-data
247-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec71fbc01098ab24713fa9eb061f29ee\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
248                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
248-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec71fbc01098ab24713fa9eb061f29ee\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
249                android:value="com.google.firebase.components.ComponentRegistrar" />
249-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec71fbc01098ab24713fa9eb061f29ee\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
250            <meta-data
250-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69f9edf46b7c0b0fac70bfaa1b91b78c\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
251                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
251-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69f9edf46b7c0b0fac70bfaa1b91b78c\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
252                android:value="com.google.firebase.components.ComponentRegistrar" />
252-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69f9edf46b7c0b0fac70bfaa1b91b78c\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
253            <meta-data
253-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
254                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
254-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
255                android:value="com.google.firebase.components.ComponentRegistrar" />
255-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
256            <meta-data
256-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59392d88c11b3a55f31d3400980e1183\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
257                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
257-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59392d88c11b3a55f31d3400980e1183\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59392d88c11b3a55f31d3400980e1183\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
259        </service>
260
261        <provider
261-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
262            android:name="com.google.firebase.provider.FirebaseInitProvider"
262-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
263            android:authorities="studio.takasaki.clubm.firebaseinitprovider"
263-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
264            android:directBootAware="true"
264-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
265            android:exported="false"
265-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
266            android:initOrder="100" />
266-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08bb9e67917d242c24fd492e9779d76b\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
267
268        <meta-data
268-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b08123b7696be024b5da2597e22edb0\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
269            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
269-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b08123b7696be024b5da2597e22edb0\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
270            android:value="GlideModule" />
270-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b08123b7696be024b5da2597e22edb0\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
271
272        <activity
272-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\559afa6d6ee92008dceb7e133cc79c58\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
273            android:name="com.google.android.gms.common.api.GoogleApiActivity"
273-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\559afa6d6ee92008dceb7e133cc79c58\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
274            android:exported="false"
274-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\559afa6d6ee92008dceb7e133cc79c58\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
275            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
275-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\559afa6d6ee92008dceb7e133cc79c58\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
276
277        <meta-data
277-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70a5ec28bf4682b0ab5b2d0ef3085bdc\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
278            android:name="com.google.android.gms.version"
278-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70a5ec28bf4682b0ab5b2d0ef3085bdc\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
279            android:value="@integer/google_play_services_version" />
279-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70a5ec28bf4682b0ab5b2d0ef3085bdc\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
280
281        <provider
281-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a66da0af00b6f826b39aad67bdf6754\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
282            android:name="androidx.startup.InitializationProvider"
282-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a66da0af00b6f826b39aad67bdf6754\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
283            android:authorities="studio.takasaki.clubm.androidx-startup"
283-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a66da0af00b6f826b39aad67bdf6754\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
284            android:exported="false" >
284-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a66da0af00b6f826b39aad67bdf6754\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
285            <meta-data
285-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a66da0af00b6f826b39aad67bdf6754\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
286                android:name="androidx.emoji2.text.EmojiCompatInitializer"
286-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a66da0af00b6f826b39aad67bdf6754\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
287                android:value="androidx.startup" />
287-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a66da0af00b6f826b39aad67bdf6754\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
288            <meta-data
288-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4dcd27e19dfe8693c454693cc6ac18a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
289                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
289-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4dcd27e19dfe8693c454693cc6ac18a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
290                android:value="androidx.startup" />
290-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4dcd27e19dfe8693c454693cc6ac18a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
291            <meta-data
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
292                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
292-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
293                android:value="androidx.startup" />
293-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
294        </provider>
295
296        <receiver
296-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
297            android:name="androidx.profileinstaller.ProfileInstallReceiver"
297-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
298            android:directBootAware="false"
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
299            android:enabled="true"
299-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
300            android:exported="true"
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
301            android:permission="android.permission.DUMP" >
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
302            <intent-filter>
302-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
303                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
303-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
303-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
304            </intent-filter>
305            <intent-filter>
305-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
306                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
307            </intent-filter>
308            <intent-filter>
308-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
309                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
310            </intent-filter>
311            <intent-filter>
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
312                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9893e2f97630d414a77f0a2a2e813502\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
313            </intent-filter>
314        </receiver>
315
316        <service
316-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4abeb9a64f266239d148ec22fbbee97\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
317            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
317-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4abeb9a64f266239d148ec22fbbee97\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
318            android:exported="false" >
318-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4abeb9a64f266239d148ec22fbbee97\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
319            <meta-data
319-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4abeb9a64f266239d148ec22fbbee97\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
320                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
320-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4abeb9a64f266239d148ec22fbbee97\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
321                android:value="cct" />
321-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4abeb9a64f266239d148ec22fbbee97\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
322        </service>
323        <service
323-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4a5ad294eee633dcf9fcadee8d4dd69\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
324            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
324-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4a5ad294eee633dcf9fcadee8d4dd69\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
325            android:exported="false"
325-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4a5ad294eee633dcf9fcadee8d4dd69\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
326            android:permission="android.permission.BIND_JOB_SERVICE" >
326-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4a5ad294eee633dcf9fcadee8d4dd69\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
327        </service>
328
329        <receiver
329-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4a5ad294eee633dcf9fcadee8d4dd69\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
330            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
330-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4a5ad294eee633dcf9fcadee8d4dd69\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
331            android:exported="false" />
331-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4a5ad294eee633dcf9fcadee8d4dd69\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
332    </application>
333
334</manifest>
