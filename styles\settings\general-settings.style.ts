import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 0,
    paddingBottom: 24
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    marginBottom: 8
  },
  sectionContainer: {
    marginBottom: 32
  },
  sectionTitle: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16,
    marginBottom: 16,
    textTransform: "uppercase",
    letterSpacing: 0.5
  },
  settingsList: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    overflow: "hidden"
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.08)",
    minHeight: 56
  },
  settingItemLast: {
    borderBottomWidth: 0
  },
  settingIcon: {
    width: 24,
    height: 24,
    marginRight: 12
  },
  settingContent: {
    flex: 1,
    marginLeft: 12
  },
  settingTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  settingDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    marginTop: 2
  },
  settingAction: {
    marginLeft: 12
  },
  settingValue: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  switch: {
    transform: [{scaleX: 0.8}, {scaleY: 0.8}]
  },
  chevronIcon: {
    width: 16,
    height: 16
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 32,
    paddingVertical: 16,
    gap: 12
  },
  logoutText: {
    color: "#F97066",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24
  }
});

export default styles;
