import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        display: "flex",
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        borderColor: "#282A2E",
        borderStyle: "solid",
        borderWidth: 1.5,
        padding: 16
    },
    headerContainer: {
        flex: 1,
        display: "flex",
        justifyContent: "space-between",
        flexDirection: "row",
        alignItems: "center",
        marginBottom: 12
    },
    badgeContainer: {
        display: "flex",
        flexDirection: "row",
        gap: 6
    },
    titleContainer: {
        display: "flex",
        flexDirection: "row",
        flexWrap: "nowrap",
        gap: 10,
        marginBottom: 10
    },
    titleText: {
        fontSize: 14,
        paddingRight: 50,
        fontWeight: 700,
        lineHeight: 20
    },
    iconContainer: {
        padding: 8,
        backgroundColor: "#1F2238",
        borderRadius: 4,
        borderStyle: "solid",
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderStrokesLines
    },
    text: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontStyle: "normal",
        fontWeight: 400,
        lineHeight: 18
    },
    description: {
        paddingBottom: 12,
        borderBottomWidth: 1,
        borderStyle: "solid",
        borderBottomColor: stylesConstants.colors.borderStrokesLines,
        marginBottom: 10
    },
    footerContainer: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "flex-end"
    },
    priceText: {
        fontSize: 14,
        fontWeight: 700,
        lineHeight: 20,
        marginTop: 4
    },
    viewEventButton: {
        display: "flex",
        flexDirection: "row",
        backgroundColor: stylesConstants.colors.brand.brand500,
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        gap: 6,
        alignItems: "center"
    },
    viewEventButtonText: {
        fontWeight: 700
    }
});

export default styles;
