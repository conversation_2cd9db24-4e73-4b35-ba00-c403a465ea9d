import React, {useCallback, useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import {ScrollView, Text, TouchableOpacity, View} from "react-native";
import MonthDot from "../../components/calendar-screen/month-dot";
import TicketCard from "../../components/events-screen/ticket-card";
import CalendarIcon from "../../components/icons/calendar-icon";
import SearchIcon from "../../components/icons/search-icon";
import InputField from "../../components/input-field";
import YearModal from "../../components/modals/year-modal";
import ScreenWithHeader from "../../components/screen-with-header";
import {useBottomModal} from "../../contexts/bottom-modal-context";
import styles from "../../styles/logged-stack/my-tickets.style";
import stylesConstants from "../../styles/styles-constants";

const MyTickets: React.FC = () => {
    const {t} = useTranslation();
    const modal = useBottomModal();
    const [searchValue, setSearchValue] = useState("");
    const [selectedMonth, setSelectedMonth] = useState(4);
    const [year, setYear] = useState<number>(new Date().getFullYear());

    const handleSearchChange = useCallback((value: string) => {
        setSearchValue(value);
    }, []);

    const handleMonthPress = useCallback((month: number) => {
        setSelectedMonth(month);
    }, []);

    const handleOnChangeYear = useCallback(
        (newYear: number) => setYear(newYear),
        []
    );

    const handleYearHeaderPress = useCallback(() => {
        modal.openModal?.({
            title: t("calendar.yearModal.title"),
            children: (
                <YearModal onChangeYear={handleOnChangeYear} year={year} />
            )
        });
    }, [modal, t, year]);

    const searchIcon = useCallback(() => <SearchIcon />, []);

    const yearHeader = useMemo(
        () => (
            <TouchableOpacity
                style={styles.yearHeader}
                onPress={handleYearHeaderPress}
            >
                <Text style={styles.yearText}>{year}</Text>
                <CalendarIcon
                    width={16}
                    height={16}
                    replaceColor={stylesConstants.colors.fullWhite}
                />
            </TouchableOpacity>
        ),
        [handleYearHeaderPress, year]
    );

    const monthNumbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

    const tickets = [
        {
            id: 1,
            title: "Mentoria Coletiva sobre Resoluções Jurídicas",
            organizer: "Arbitralis",
            date: "29/05/2025 - 17:30 PM",
            code: "X9TR-72QK-P5DM"
        },
        {
            id: 2,
            title: "Mentoria Coletiva sobre Resoluções Jurídicas",
            organizer: "Arbitralis",
            date: "29/05/2025 - 17:30 PM",
            code: "X9TR-72QK-P5DM"
        }
    ];

    return (
        <ScreenWithHeader
            screenTitle={t("myTickets.title")}
            backButton={true}
            rightHeaderChild={yearHeader}
        >
            <View style={styles.container}>
                <View style={styles.monthsContainer}>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={styles.monthsList}
                    >
                        {monthNumbers.map((monthNumber) => (
                            <MonthDot
                                key={monthNumber}
                                month={monthNumber}
                                active={selectedMonth === monthNumber}
                                onPress={() => handleMonthPress(monthNumber)}
                            />
                        ))}
                    </ScrollView>
                </View>

                <View style={styles.contentContainer}>
                    <View style={styles.searchSection}>
                        <Text style={styles.sectionTitle}>
                            {t("myTickets.reservedTickets", {count: 4})}
                        </Text>
                        <InputField
                            placeholder={t("myTickets.searchPlaceholder")}
                            value={searchValue}
                            onChangeText={handleSearchChange}
                            icon={searchIcon}
                            style={styles.searchInput}
                        />
                    </View>

                    <ScrollView
                        style={styles.ticketsList}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={
                            styles.ticketsScrollInternalContainer
                        }
                    >
                        {tickets.map((ticket) => (
                            <View
                                key={ticket.id}
                                style={styles.ticketContainer}
                            >
                                <TicketCard
                                    code={ticket.code}
                                    date={ticket.date}
                                    organizer={ticket.organizer}
                                    title={ticket.title}
                                />
                            </View>
                        ))}
                    </ScrollView>
                </View>
            </View>
        </ScreenWithHeader>
    );
};

export default MyTickets;
