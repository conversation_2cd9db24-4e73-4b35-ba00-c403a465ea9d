import React, {useCallback} from "react";
import {
    ColorValue,
    StyleProp,
    Text,
    TouchableHighlight,
    View,
    ViewStyle
} from "react-native";
import styles from "../styles/components/button.style";
import stylesConstants from "../styles/styles-constants";
import {useTranslation} from "react-i18next";
import {useLoading} from "../contexts/loading-context";

export interface ButtonProps {
    onPress?: () => void;
    text: string;
    icon?: React.ReactNode;
    backgroundColor?: ColorValue;
    borderColor?: ColorValue;
    style?: StyleProp<ViewStyle>;
    underlayColor?: ColorValue;
    disableBorder?: boolean;
    disabledColor?: boolean;
}

const Button: React.FC<ButtonProps> = (props) => {
    const {t} = useTranslation();
    const loadingAction = useLoading();

    const onPress = useCallback(() => {
        props.onPress?.();
    }, [props.onPress]);

    return (
        <TouchableHighlight
            style={[
                styles.container,
                loadingAction.currentLoading && styles.disable,
                props.backgroundColor && {
                    backgroundColor: props.backgroundColor
                },
                props.borderColor && {
                    borderColor: props.borderColor
                },
                props.disableBorder && styles.disableBorder,
                props.style,
            ]}
            underlayColor={
                !props.disabledColor ? (props.underlayColor ?? stylesConstants.colors.brand.brand400) : undefined
            }
            disabled={loadingAction.currentLoading}
            onPress={onPress}
        >
            <View style={styles.innerContainer}>
                {props.icon}
                <Text style={styles.text}>{t(props.text)}</Text>
            </View>
        </TouchableHighlight>
    );
};

export default Button;
