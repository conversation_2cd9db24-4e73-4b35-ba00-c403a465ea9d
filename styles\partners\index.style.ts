import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  searchContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.inputBackground,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12
  },
  searchInput: {
    flex: 1,
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24
  },
  categoriesContainer: {
    paddingBottom: 20
  },
  categoriesTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    paddingHorizontal: 24,
    marginBottom: 12
  },
  categoriesScrollView: {
    paddingLeft: 24
  },
  categoriesContent: {
    paddingRight: 24,
    gap: 8
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  categoryButtonActive: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderColor: stylesConstants.colors.brand.primary
  },
  categoryButtonText: {
    color: stylesConstants.colors.gray300,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20
  },
  categoryButtonTextActive: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: "600"
  },
  partnersGrid: {
    paddingHorizontal: 24,
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between"
  },
  partnerCard: {
    width: "47%",
    aspectRatio: 1,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 12,
    padding: 16,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16
  },
  partnerLogoContainer: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center"
  },
  partnerLogo: {
    width: "80%",
    height: "80%"
  },
  partnerName: {
    color: stylesConstants.colors.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    textAlign: "center"
  }
});

export default styles;
