import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        paddingHorizontal: 32,
        paddingVertical: 48
    },
    iconContainer: {
        marginBottom: 24,
        opacity: 0.6
    },
    title: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 18,
        fontWeight: "700",
        lineHeight: 28,
        color: stylesConstants.colors.fullWhite,
        textAlign: "center",
        marginBottom: 12
    },
    description: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: "400",
        lineHeight: 20,
        color: stylesConstants.colors.textPrimary,
        textAlign: "center",
        marginBottom: 32
    },
    button: {
        backgroundColor: stylesConstants.colors.brand.brand400,
        borderRadius: 8,
        paddingHorizontal: 24,
        paddingVertical: 12,
        minWidth: 200
    },
    buttonText: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: "600",
        lineHeight: 20,
        color: stylesConstants.colors.fullWhite,
        textAlign: "center"
    }
});

export default styles;
