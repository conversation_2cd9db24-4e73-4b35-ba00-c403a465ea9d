import React, {useCallback} from "react";
import {
  ScrollView,
  Text,
  View,
  TouchableOpacity,
  Linking,
  Alert
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";

const AboutApp: React.FC = () => {
  const {t} = useTranslation();

  const appInfo = {
    version: "1.0.0",
    buildNumber: "2024.01.15",
    releaseDate: "Janeiro 2024",
    developer: "Takasaki Studio",
    website: "www.clubmbrasil.com.br",
    supportEmail: "<EMAIL>",
    privacyPolicy: "https://clubmbrasil.com.br/privacidade",
    termsOfService: "https://clubmbrasil.com.br/termos"
  };

  const teamMembers = [
    {name: "<PERSON>", role: "CEO & Founder", emoji: "👨‍💼"},
    {name: "<PERSON>", role: "CT<PERSON>", emoji: "👩‍💻"},
    {name: "<PERSON>", role: "Head of Design", emoji: "🎨"},
    {name: "<PERSON>", role: "Product Manager", emoji: "📱"}
  ];

  const features = [
    {
      title: t("aboutApp.features.networking", "Networking Profissional"),
      icon: "🤝"
    },
    {title: t("aboutApp.features.events", "Eventos Exclusivos"), icon: "🎉"},
    {
      title: t("aboutApp.features.opportunities", "Oportunidades de Negócio"),
      icon: "💼"
    },
    {
      title: t("aboutApp.features.benefits", "Benefícios Exclusivos"),
      icon: "🎁"
    },
    {title: t("aboutApp.features.magazine", "Revista Digital"), icon: "📰"},
    {title: t("aboutApp.features.community", "Comunidade Ativa"), icon: "👥"}
  ];

  const handleLinkPress = useCallback(
    (url: string) => {
      Linking.openURL(url).catch(() => {
        Alert.alert(
          t("aboutApp.error", "Erro"),
          t("aboutApp.linkError", "Não foi possível abrir o link."),
          [{text: t("common.ok", "OK")}]
        );
      });
    },
    [t]
  );

  const handleEmailPress = useCallback(() => {
    Linking.openURL(`mailto:${appInfo.supportEmail}`).catch(() => {
      Alert.alert(
        t("aboutApp.error", "Erro"),
        t("aboutApp.emailError", "Não foi possível abrir o cliente de e-mail."),
        [{text: t("common.ok", "OK")}]
      );
    });
  }, [appInfo.supportEmail, t]);

  return (
    <ScreenWithHeader
      screenTitle={t("aboutApp.title", "Sobre o Aplicativo")}
      backButton
    >
      <ScrollView style={{flex: 1, padding: 20}}>
        {/* App Logo and Info */}
        <View
          style={{
            alignItems: "center",
            backgroundColor: "rgba(255,255,255,0.1)",
            padding: 30,
            borderRadius: 12,
            marginBottom: 25
          }}
        >
          <View
            style={{
              width: 80,
              height: 80,
              borderRadius: 20,
              backgroundColor: "#007AFF",
              alignItems: "center",
              justifyContent: "center",
              marginBottom: 15
            }}
          >
            <Text style={{fontSize: 32, color: "#fff", fontWeight: "bold"}}>
              M
            </Text>
          </View>
          <Text
            style={{
              color: "#fff",
              fontSize: 24,
              fontWeight: "bold",
              marginBottom: 5
            }}
          >
            Club M Brasil
          </Text>
          <Text
            style={{
              color: "#ccc",
              fontSize: 14,
              textAlign: "center",
              marginBottom: 10
            }}
          >
            {t(
              "aboutApp.tagline",
              "Conectando profissionais, criando oportunidades"
            )}
          </Text>
          <View
            style={{
              backgroundColor: "rgba(0,122,255,0.2)",
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 15
            }}
          >
            <Text style={{color: "#007AFF", fontSize: 12, fontWeight: "bold"}}>
              v{appInfo.version}
            </Text>
          </View>
        </View>

        {/* App Description */}
        <View
          style={{
            backgroundColor: "rgba(255,255,255,0.05)",
            padding: 20,
            borderRadius: 12,
            marginBottom: 25
          }}
        >
          <Text
            style={{
              color: "#fff",
              fontSize: 16,
              fontWeight: "bold",
              marginBottom: 10
            }}
          >
            {t("aboutApp.aboutTitle", "Sobre o Club M")}
          </Text>
          <Text style={{color: "#ccc", fontSize: 14, lineHeight: 20}}>
            {t(
              "aboutApp.description",
              "O Club M Brasil é uma plataforma exclusiva que conecta profissionais de alto nível, oferecendo oportunidades únicas de networking, eventos exclusivos e benefícios especiais para nossos membros."
            )}
          </Text>
        </View>

        {/* Key Features */}
        <Text
          style={{
            color: "#fff",
            fontSize: 16,
            fontWeight: "bold",
            marginBottom: 15
          }}
        >
          {t("aboutApp.featuresTitle", "Principais Recursos")}
        </Text>
        <View
          style={{
            backgroundColor: "rgba(255,255,255,0.05)",
            borderRadius: 12,
            marginBottom: 25,
            overflow: "hidden"
          }}
        >
          {features.map((feature, index) => (
            <View key={`feature-${feature.title.slice(0, 20)}-${index}`}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  padding: 15
                }}
              >
                <Text style={{fontSize: 20, marginRight: 15}}>
                  {feature.icon}
                </Text>
                <Text style={{color: "#fff", fontSize: 14, flex: 1}}>
                  {feature.title}
                </Text>
              </View>
              {index < features.length - 1 && (
                <View
                  style={{
                    height: 1,
                    backgroundColor: "rgba(255,255,255,0.1)",
                    marginLeft: 50
                  }}
                />
              )}
            </View>
          ))}
        </View>

        {/* Team */}
        <Text
          style={{
            color: "#fff",
            fontSize: 16,
            fontWeight: "bold",
            marginBottom: 15
          }}
        >
          {t("aboutApp.teamTitle", "Nossa Equipe")}
        </Text>
        <View
          style={{
            backgroundColor: "rgba(255,255,255,0.05)",
            borderRadius: 12,
            padding: 20,
            marginBottom: 25
          }}
        >
          <View style={{flexDirection: "row", flexWrap: "wrap", gap: 15}}>
            {teamMembers.map((member, index) => (
              <View
                key={`member-${member.name.slice(0, 20)}-${index}`}
                style={{
                  backgroundColor: "rgba(255,255,255,0.1)",
                  padding: 15,
                  borderRadius: 8,
                  alignItems: "center",
                  flex: 1,
                  minWidth: 140
                }}
              >
                <Text style={{fontSize: 32, marginBottom: 8}}>
                  {member.emoji}
                </Text>
                <Text
                  style={{
                    color: "#fff",
                    fontSize: 12,
                    fontWeight: "bold",
                    marginBottom: 2
                  }}
                >
                  {member.name}
                </Text>
                <Text
                  style={{color: "#ccc", fontSize: 10, textAlign: "center"}}
                >
                  {member.role}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Technical Info */}
        <Text
          style={{
            color: "#fff",
            fontSize: 16,
            fontWeight: "bold",
            marginBottom: 15
          }}
        >
          {t("aboutApp.technicalInfo", "Informações Técnicas")}
        </Text>
        <View
          style={{
            backgroundColor: "rgba(255,255,255,0.05)",
            borderRadius: 12,
            padding: 20,
            marginBottom: 25
          }}
        >
          <View style={{gap: 10}}>
            <View
              style={{flexDirection: "row", justifyContent: "space-between"}}
            >
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t("aboutApp.version", "Versão")}:
              </Text>
              <Text style={{color: "#fff", fontSize: 14}}>
                {appInfo.version}
              </Text>
            </View>
            <View
              style={{flexDirection: "row", justifyContent: "space-between"}}
            >
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t("aboutApp.buildNumber", "Build")}:
              </Text>
              <Text style={{color: "#fff", fontSize: 14}}>
                {appInfo.buildNumber}
              </Text>
            </View>
            <View
              style={{flexDirection: "row", justifyContent: "space-between"}}
            >
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t("aboutApp.releaseDate", "Lançamento")}:
              </Text>
              <Text style={{color: "#fff", fontSize: 14}}>
                {appInfo.releaseDate}
              </Text>
            </View>
            <View
              style={{flexDirection: "row", justifyContent: "space-between"}}
            >
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t("aboutApp.developer", "Desenvolvedor")}:
              </Text>
              <Text style={{color: "#fff", fontSize: 14}}>
                {appInfo.developer}
              </Text>
            </View>
          </View>
        </View>

        {/* Contact and Links */}
        <Text
          style={{
            color: "#fff",
            fontSize: 16,
            fontWeight: "bold",
            marginBottom: 15
          }}
        >
          {t("aboutApp.contactTitle", "Contato e Links")}
        </Text>
        <View
          style={{
            backgroundColor: "rgba(255,255,255,0.05)",
            borderRadius: 12,
            marginBottom: 25,
            overflow: "hidden"
          }}
        >
          <TouchableOpacity
            onPress={() => handleLinkPress(`https://${appInfo.website}`)}
            style={{
              flexDirection: "row",
              alignItems: "center",
              padding: 20
            }}
          >
            <Text style={{fontSize: 20, marginRight: 15}}>🌐</Text>
            <View style={{flex: 1}}>
              <Text
                style={{
                  color: "#fff",
                  fontSize: 14,
                  fontWeight: "bold",
                  marginBottom: 2
                }}
              >
                {t("aboutApp.website", "Website")}
              </Text>
              <Text style={{color: "#007AFF", fontSize: 12}}>
                {appInfo.website}
              </Text>
            </View>
            <Text style={{color: "#007AFF", fontSize: 18}}>→</Text>
          </TouchableOpacity>

          <View
            style={{
              height: 1,
              backgroundColor: "rgba(255,255,255,0.1)",
              marginLeft: 55
            }}
          />

          <TouchableOpacity
            onPress={handleEmailPress}
            style={{
              flexDirection: "row",
              alignItems: "center",
              padding: 20
            }}
          >
            <Text style={{fontSize: 20, marginRight: 15}}>📧</Text>
            <View style={{flex: 1}}>
              <Text
                style={{
                  color: "#fff",
                  fontSize: 14,
                  fontWeight: "bold",
                  marginBottom: 2
                }}
              >
                {t("aboutApp.support", "Suporte")}
              </Text>
              <Text style={{color: "#007AFF", fontSize: 12}}>
                {appInfo.supportEmail}
              </Text>
            </View>
            <Text style={{color: "#007AFF", fontSize: 18}}>→</Text>
          </TouchableOpacity>

          <View
            style={{
              height: 1,
              backgroundColor: "rgba(255,255,255,0.1)",
              marginLeft: 55
            }}
          />

          <TouchableOpacity
            onPress={() => handleLinkPress(appInfo.privacyPolicy)}
            style={{
              flexDirection: "row",
              alignItems: "center",
              padding: 20
            }}
          >
            <Text style={{fontSize: 20, marginRight: 15}}>🔒</Text>
            <View style={{flex: 1}}>
              <Text style={{color: "#fff", fontSize: 14, fontWeight: "bold"}}>
                {t("aboutApp.privacyPolicy", "Política de Privacidade")}
              </Text>
            </View>
            <Text style={{color: "#007AFF", fontSize: 18}}>→</Text>
          </TouchableOpacity>

          <View
            style={{
              height: 1,
              backgroundColor: "rgba(255,255,255,0.1)",
              marginLeft: 55
            }}
          />

          <TouchableOpacity
            onPress={() => handleLinkPress(appInfo.termsOfService)}
            style={{
              flexDirection: "row",
              alignItems: "center",
              padding: 20
            }}
          >
            <Text style={{fontSize: 20, marginRight: 15}}>📋</Text>
            <View style={{flex: 1}}>
              <Text style={{color: "#fff", fontSize: 14, fontWeight: "bold"}}>
                {t("aboutApp.termsOfService", "Termos de Serviço")}
              </Text>
            </View>
            <Text style={{color: "#007AFF", fontSize: 18}}>→</Text>
          </TouchableOpacity>
        </View>

        {/* Copyright */}
        <View
          style={{
            alignItems: "center",
            padding: 20,
            marginBottom: 20
          }}
        >
          <Text style={{color: "#666", fontSize: 12, textAlign: "center"}}>
            © 2024 Club M Brasil.{" "}
            {t("aboutApp.allRightsReserved", "Todos os direitos reservados.")}
          </Text>
          <Text
            style={{
              color: "#666",
              fontSize: 12,
              textAlign: "center",
              marginTop: 5
            }}
          >
            {t("aboutApp.developedBy", "Desenvolvido com ❤️ por")}{" "}
            {appInfo.developer}
          </Text>
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default AboutApp;
