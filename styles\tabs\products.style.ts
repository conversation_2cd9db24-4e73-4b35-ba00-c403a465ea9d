import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    marginBottom: 8
  },
  searchContainer: {
    marginBottom: 20
  },
  categoriesContainer: {
    marginBottom: 24
  },
  categoryTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    marginBottom: 12
  },
  productsList: {
    gap: 16
  }
});

export default styles;
