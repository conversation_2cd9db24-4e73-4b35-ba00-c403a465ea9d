{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-64:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88cfc00e8e0a9ed392dc6923ed9897c3\\transformed\\browser-1.6.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "69,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "6622,7245,7340,7446", "endColumns": "95,94,105,96", "endOffsets": "6713,7335,7441,7538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59ca874c8ef89227cc0c3a48f60e855c\\transformed\\biometric-1.2.0-alpha04\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,196,304,410,497,629,729,834,941,1053,1169,1289,1405,1520,1612,1755,1866,1998,2123,2228,2342,2434,2558,2641,2757,2851,2978", "endColumns": "140,107,105,86,131,99,104,106,111,115,119,115,114,91,142,110,131,124,104,113,91,123,82,115,93,126,98", "endOffsets": "191,299,405,492,624,724,829,936,1048,1164,1284,1400,1515,1607,1750,1861,1993,2118,2223,2337,2429,2553,2636,2752,2846,2973,3072"}, "to": {"startLines": "33,34,68,70,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,153,159,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2929,3070,6516,6718,7013,7145,7543,7648,7755,7867,7983,8103,8219,8334,8426,8569,8680,8812,8937,13563,14083,14175,14299,14382,14498,14592,14719", "endColumns": "140,107,105,86,131,99,104,106,111,115,119,115,114,91,142,110,131,124,104,113,91,123,82,115,93,126,98", "endOffsets": "3065,3173,6617,6800,7140,7240,7643,7750,7862,7978,8098,8214,8329,8421,8564,8675,8807,8932,9037,13672,14170,14294,14377,14493,14587,14714,14813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2e07b8f892dff7e7220c58c45859f4c7\\transformed\\appcompat-1.7.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,409,507,613,699,802,919,997,1073,1164,1257,1349,1443,1543,1636,1731,1824,1915,2006,2086,2186,2286,2382,2484,2584,2683,2833,13677", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "404,502,608,694,797,914,992,1068,1159,1252,1344,1438,1538,1631,1726,1819,1910,2001,2081,2181,2281,2377,2479,2579,2678,2828,2924,13752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\559afa6d6ee92008dceb7e133cc79c58\\transformed\\play-services-base-18.0.1\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4510,4612,4751,4873,4975,5102,5225,5333,5567,5695,5798,5943,6066,6201,6328,6388,6445", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "4607,4746,4868,4970,5097,5220,5328,5430,5690,5793,5938,6061,6196,6323,6383,6440,6511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2684515d4d7b0c87770167e126691f99\\transformed\\material-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,965,1029,1114,1176,1234,1319,1382,1444,1502,1568,1630,1685,1781,1838,1897,1953,2020,2125,2205,2286,2378,2463,2544,2673,2746,2817,2931,3013,3089,3140,3191,3257,3323,3396,3467,3542,3610,3683,3754,3821,3919,4004,4071,4158,4246,4320,4388,4473,4524,4602,4666,4746,4828,4890,4954,5017,5083,5178,5273,5358,5449,5504,5559,5635,5714,5789", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "256,327,395,470,552,633,722,824,901,960,1024,1109,1171,1229,1314,1377,1439,1497,1563,1625,1680,1776,1833,1892,1948,2015,2120,2200,2281,2373,2458,2539,2668,2741,2812,2926,3008,3084,3135,3186,3252,3318,3391,3462,3537,3605,3678,3749,3816,3914,3999,4066,4153,4241,4315,4383,4468,4519,4597,4661,4741,4823,4885,4949,5012,5078,5173,5268,5353,5444,5499,5554,5630,5709,5784,5855"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,71,72,73,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3178,3249,3317,3392,3474,4242,4331,4433,6805,6864,6928,9042,9104,9162,9247,9310,9372,9430,9496,9558,9613,9709,9766,9825,9881,9948,10053,10133,10214,10306,10391,10472,10601,10674,10745,10859,10941,11017,11068,11119,11185,11251,11324,11395,11470,11538,11611,11682,11749,11847,11932,11999,12086,12174,12248,12316,12401,12452,12530,12594,12674,12756,12818,12882,12945,13011,13106,13201,13286,13377,13432,13487,13757,13836,13911", "endLines": "5,35,36,37,38,39,47,48,49,71,72,73,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,155,156,157", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "306,3244,3312,3387,3469,3550,4326,4428,4505,6859,6923,7008,9099,9157,9242,9305,9367,9425,9491,9553,9608,9704,9761,9820,9876,9943,10048,10128,10209,10301,10386,10467,10596,10669,10740,10854,10936,11012,11063,11114,11180,11246,11319,11390,11465,11533,11606,11677,11744,11842,11927,11994,12081,12169,12243,12311,12396,12447,12525,12589,12669,12751,12813,12877,12940,13006,13101,13196,13281,13372,13427,13482,13558,13831,13906,13977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70a5ec28bf4682b0ab5b2d0ef3085bdc\\transformed\\play-services-basement-18.3.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5435", "endColumns": "131", "endOffsets": "5562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\463a0a6d18bcedcdf0dcc1e5161e05be\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3555,3648,3748,3845,3944,4040,4142,13982", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3643,3743,3840,3939,4035,4137,4237,14078"}}]}]}