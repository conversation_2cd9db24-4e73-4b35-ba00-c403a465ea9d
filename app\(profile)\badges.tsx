import React from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";

interface Badge {
    id: string;
    name: string;
    description: string;
    icon: string;
    earned: boolean;
    earnedDate?: string;
    progress?: number;
    requirement?: string;
    category: 'networking' | 'events' | 'business' | 'community' | 'achievement';
}

const Badges: React.FC = () => {
    const {t} = useTranslation();

    const badges: Badge[] = [
        {
            id: '1',
            name: t("badges.networkingMaster.name", "Networking Master"),
            description: t("badges.networkingMaster.desc", "Conectou-se com mais de 50 pessoas"),
            icon: '🤝',
            earned: true,
            earnedDate: '15 de dezembro, 2024',
            category: 'networking'
        },
        {
            id: '2',
            name: t("badges.eventEnthusiast.name", "Entusiasta de Eventos"),
            description: t("badges.eventEnthusiast.desc", "Participou de 10 eventos"),
            icon: '🎉',
            earned: true,
            earnedDate: '10 de dezembro, 2024',
            category: 'events'
        },
        {
            id: '3',
            name: t("badges.mentor.name", "Mentor"),
            description: t("badges.mentor.desc", "Ajudou 5 pessoas em suas carreiras"),
            icon: '🎓',
            earned: true,
            earnedDate: '5 de dezembro, 2024',
            category: 'community'
        },
        {
            id: '4',
            name: t("badges.innovator.name", "Inovador"),
            description: t("badges.innovator.desc", "Compartilhou 3 ideias inovadoras"),
            icon: '💡',
            earned: false,
            progress: 66,
            requirement: '2/3 ideias compartilhadas',
            category: 'business'
        },
        {
            id: '5',
            name: t("badges.speaker.name", "Palestrante"),
            description: t("badges.speaker.desc", "Apresentou em um evento"),
            icon: '🎤',
            earned: false,
            progress: 0,
            requirement: 'Apresentar em 1 evento',
            category: 'achievement'
        },
        {
            id: '6',
            name: t("badges.earlyAdopter.name", "Early Adopter"),
            description: t("badges.earlyAdopter.desc", "Um dos primeiros 100 membros"),
            icon: '🚀',
            earned: true,
            earnedDate: '1 de janeiro, 2024',
            category: 'achievement'
        }
    ];

    const categories = [
        {id: 'all', label: t("badges.categories.all", "Todos"), icon: '🏆'},
        {id: 'networking', label: t("badges.categories.networking", "Networking"), icon: '🤝'},
        {id: 'events', label: t("badges.categories.events", "Eventos"), icon: '🎉'},
        {id: 'business', label: t("badges.categories.business", "Negócios"), icon: '💼'},
        {id: 'community', label: t("badges.categories.community", "Comunidade"), icon: '👥'},
        {id: 'achievement', label: t("badges.categories.achievement", "Conquistas"), icon: '🎯'}
    ];

    const [selectedCategory, setSelectedCategory] = React.useState('all');

    const filteredBadges = selectedCategory === 'all' 
        ? badges 
        : badges.filter(badge => badge.category === selectedCategory);

    const earnedBadges = badges.filter(badge => badge.earned);
    const totalBadges = badges.length;

    return (
        <ScreenWithHeader screenTitle={t("badges.title", "Selos")} backButton>
            <ScrollView style={{flex: 1}}>
                {/* Stats */}
                <View style={{backgroundColor: 'rgba(255,255,255,0.1)', margin: 20, padding: 20, borderRadius: 12}}>
                    <Text style={{color: '#fff', fontSize: 18, fontWeight: 'bold', marginBottom: 15}}>
                        {t("badges.progress", "Seu Progresso")}
                    </Text>
                    
                    <View style={{flexDirection: 'row', justifyContent: 'space-around'}}>
                        <View style={{alignItems: 'center'}}>
                            <Text style={{color: '#fff', fontSize: 24, fontWeight: 'bold'}}>
                                {earnedBadges.length}
                            </Text>
                            <Text style={{color: '#ccc', fontSize: 12}}>
                                {t("badges.earned", "Conquistados")}
                            </Text>
                        </View>
                        
                        <View style={{alignItems: 'center'}}>
                            <Text style={{color: '#fff', fontSize: 24, fontWeight: 'bold'}}>
                                {totalBadges - earnedBadges.length}
                            </Text>
                            <Text style={{color: '#ccc', fontSize: 12}}>
                                {t("badges.remaining", "Restantes")}
                            </Text>
                        </View>
                        
                        <View style={{alignItems: 'center'}}>
                            <Text style={{color: '#fff', fontSize: 24, fontWeight: 'bold'}}>
                                {Math.round((earnedBadges.length / totalBadges) * 100)}%
                            </Text>
                            <Text style={{color: '#ccc', fontSize: 12}}>
                                {t("badges.completion", "Completo")}
                            </Text>
                        </View>
                    </View>
                </View>

                {/* Categories */}
                <ScrollView 
                    horizontal 
                    showsHorizontalScrollIndicator={false}
                    style={{paddingLeft: 20, marginBottom: 20}}
                    contentContainerStyle={{paddingRight: 20}}
                >
                    <View style={{flexDirection: 'row', gap: 10}}>
                        {categories.map((category) => (
                            <TouchableOpacity
                                key={category.id}
                                style={{
                                    backgroundColor: selectedCategory === category.id ? '#007AFF' : 'rgba(255,255,255,0.1)',
                                    paddingHorizontal: 15,
                                    paddingVertical: 8,
                                    borderRadius: 20,
                                    flexDirection: 'row',
                                    alignItems: 'center'
                                }}
                                onPress={() => setSelectedCategory(category.id)}
                            >
                                <Text style={{fontSize: 14, marginRight: 5}}>
                                    {category.icon}
                                </Text>
                                <Text style={{
                                    color: selectedCategory === category.id ? '#fff' : '#ccc',
                                    fontSize: 14,
                                    fontWeight: selectedCategory === category.id ? 'bold' : 'normal'
                                }}>
                                    {category.label}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </ScrollView>

                {/* Badges Grid */}
                <View style={{paddingHorizontal: 20, paddingBottom: 20}}>
                    <View style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        gap: 15,
                        justifyContent: 'space-between'
                    }}>
                        {filteredBadges.map((badge) => (
                            <TouchableOpacity
                                key={badge.id}
                                style={{
                                    backgroundColor: badge.earned ? 'rgba(52,199,89,0.1)' : 'rgba(255,255,255,0.1)',
                                    borderRadius: 12,
                                    padding: 15,
                                    width: '48%',
                                    borderWidth: badge.earned ? 2 : 1,
                                    borderColor: badge.earned ? '#34C759' : 'rgba(255,255,255,0.2)',
                                    opacity: badge.earned ? 1 : 0.7
                                }}
                            >
                                <View style={{alignItems: 'center', marginBottom: 10}}>
                                    <Text style={{fontSize: 32, marginBottom: 5}}>
                                        {badge.icon}
                                    </Text>
                                    {badge.earned && (
                                        <View style={{
                                            backgroundColor: '#34C759',
                                            borderRadius: 10,
                                            paddingHorizontal: 6,
                                            paddingVertical: 2
                                        }}>
                                            <Text style={{color: '#fff', fontSize: 8, fontWeight: 'bold'}}>
                                                {t("badges.earned", "CONQUISTADO")}
                                            </Text>
                                        </View>
                                    )}
                                </View>
                                
                                <Text style={{
                                    color: '#fff',
                                    fontSize: 14,
                                    fontWeight: 'bold',
                                    textAlign: 'center',
                                    marginBottom: 5
                                }}>
                                    {badge.name}
                                </Text>
                                
                                <Text style={{
                                    color: '#ccc',
                                    fontSize: 12,
                                    textAlign: 'center',
                                    lineHeight: 16,
                                    marginBottom: 8
                                }}>
                                    {badge.description}
                                </Text>
                                
                                {badge.earned ? (
                                    <Text style={{
                                        color: '#34C759',
                                        fontSize: 10,
                                        textAlign: 'center'
                                    }}>
                                        {badge.earnedDate}
                                    </Text>
                                ) : (
                                    <View>
                                        {badge.progress !== undefined && (
                                            <View style={{marginBottom: 5}}>
                                                <View style={{
                                                    backgroundColor: 'rgba(255,255,255,0.2)',
                                                    height: 4,
                                                    borderRadius: 2,
                                                    marginBottom: 3
                                                }}>
                                                    <View style={{
                                                        backgroundColor: '#007AFF',
                                                        height: 4,
                                                        borderRadius: 2,
                                                        width: `${badge.progress}%`
                                                    }} />
                                                </View>
                                                <Text style={{
                                                    color: '#007AFF',
                                                    fontSize: 10,
                                                    textAlign: 'center'
                                                }}>
                                                    {badge.progress}%
                                                </Text>
                                            </View>
                                        )}
                                        <Text style={{
                                            color: '#ccc',
                                            fontSize: 10,
                                            textAlign: 'center'
                                        }}>
                                            {badge.requirement}
                                        </Text>
                                    </View>
                                )}
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            </ScrollView>
        </ScreenWithHeader>
    );
};

export default Badges;
