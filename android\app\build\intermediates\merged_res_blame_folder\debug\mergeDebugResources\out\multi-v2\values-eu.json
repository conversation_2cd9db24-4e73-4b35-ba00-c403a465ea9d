{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-64:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59ca874c8ef89227cc0c3a48f60e855c\\transformed\\biometric-1.2.0-alpha04\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,352,463,556,707,819,934,1053,1189,1322,1458,1584,1741,1840,2000,2121,2265,2399,2538,2672,2779,2925,3015,3144,3243,3381", "endColumns": "167,128,110,92,150,111,114,118,135,132,135,125,156,98,159,120,143,133,138,133,106,145,89,128,98,137,107", "endOffsets": "218,347,458,551,702,814,929,1048,1184,1317,1453,1579,1736,1835,1995,2116,2260,2394,2533,2667,2774,2920,3010,3139,3238,3376,3484"}, "to": {"startLines": "33,34,68,70,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,153,159,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3070,3238,7168,7379,7704,7855,8287,8402,8521,8657,8790,8926,9052,9209,9308,9468,9589,9733,9867,14851,15408,15515,15661,15751,15880,15979,16117", "endColumns": "167,128,110,92,150,111,114,118,135,132,135,125,156,98,159,120,143,133,138,133,106,145,89,128,98,137,107", "endOffsets": "3233,3362,7274,7467,7850,7962,8397,8516,8652,8785,8921,9047,9204,9303,9463,9584,9728,9862,10001,14980,15510,15656,15746,15875,15974,16112,16220"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\463a0a6d18bcedcdf0dcc1e5161e05be\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3866,3964,4067,4167,4270,4375,4478,15307", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3959,4062,4162,4265,4370,4473,4592,15403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\559afa6d6ee92008dceb7e133cc79c58\\transformed\\play-services-base-18.0.1\\res\\values-eu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4893,4997,5169,5293,5402,5554,5679,5803,6054,6232,6340,6503,6631,6785,6945,7011,7076", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "4992,5164,5288,5397,5549,5674,5798,5906,6227,6335,6498,6626,6780,6940,7006,7071,7163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2684515d4d7b0c87770167e126691f99\\transformed\\material-1.12.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1136,1203,1302,1370,1431,1519,1582,1648,1712,1783,1846,1900,2009,2068,2131,2185,2259,2384,2474,2552,2641,2724,2804,2949,3032,3114,3252,3343,3426,3478,3531,3597,3668,3748,3819,3899,3977,4055,4128,4203,4310,4397,4484,4575,4668,4740,4816,4908,4959,5041,5107,5191,5277,5339,5403,5466,5534,5641,5750,5846,5951,6007,6064,6147,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "270,377,482,562,669,769,867,982,1065,1131,1198,1297,1365,1426,1514,1577,1643,1707,1778,1841,1895,2004,2063,2126,2180,2254,2379,2469,2547,2636,2719,2799,2944,3027,3109,3247,3338,3421,3473,3526,3592,3663,3743,3814,3894,3972,4050,4123,4198,4305,4392,4479,4570,4663,4735,4811,4903,4954,5036,5102,5186,5272,5334,5398,5461,5529,5636,5745,5841,5946,6002,6059,6142,6227,6304,6381"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,71,72,73,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3367,3474,3579,3659,3766,4597,4695,4810,7472,7538,7605,10006,10074,10135,10223,10286,10352,10416,10487,10550,10604,10713,10772,10835,10889,10963,11088,11178,11256,11345,11428,11508,11653,11736,11818,11956,12047,12130,12182,12235,12301,12372,12452,12523,12603,12681,12759,12832,12907,13014,13101,13188,13279,13372,13444,13520,13612,13663,13745,13811,13895,13981,14043,14107,14170,14238,14345,14454,14550,14655,14711,14768,15068,15153,15230", "endLines": "5,35,36,37,38,39,47,48,49,71,72,73,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,155,156,157", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "320,3469,3574,3654,3761,3861,4690,4805,4888,7533,7600,7699,10069,10130,10218,10281,10347,10411,10482,10545,10599,10708,10767,10830,10884,10958,11083,11173,11251,11340,11423,11503,11648,11731,11813,11951,12042,12125,12177,12230,12296,12367,12447,12518,12598,12676,12754,12827,12902,13009,13096,13183,13274,13367,13439,13515,13607,13658,13740,13806,13890,13976,14038,14102,14165,14233,14340,14449,14545,14650,14706,14763,14846,15148,15225,15302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70a5ec28bf4682b0ab5b2d0ef3085bdc\\transformed\\play-services-basement-18.3.0\\res\\values-eu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5911", "endColumns": "142", "endOffsets": "6049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2e07b8f892dff7e7220c58c45859f4c7\\transformed\\appcompat-1.7.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,14985", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,15063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88cfc00e8e0a9ed392dc6923ed9897c3\\transformed\\browser-1.6.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,104", "endOffsets": "150,252,365,470"}, "to": {"startLines": "69,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "7279,7967,8069,8182", "endColumns": "99,101,112,104", "endOffsets": "7374,8064,8177,8282"}}]}]}