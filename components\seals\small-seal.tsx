import {Image, ImageSource} from "expo-image";
import React from "react";
import {LinearGradient} from "expo-linear-gradient";
import styles from "../../styles/components/seals/small-seal.style";

export interface SmallSealProps {
    source: ImageSource;
}

const SmallSeal: React.FC<SmallSealProps> = (props) => {
    return (
        <LinearGradient
            colors={["rgba(255,255,255,0)", "rgba(255,255,255,0.15)"]}
            locations={[0.6958, 1]}
            start={{x: 0, y: 0.5}}
            end={{x: 1, y: 0.5}}
            style={[styles.container]}
        >
            <Image style={[styles.image]} source={props.source} />
        </LinearGradient>
    );
};

export default SmallSeal;
