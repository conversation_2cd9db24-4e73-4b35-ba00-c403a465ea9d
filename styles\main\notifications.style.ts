import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    marginBottom: 8
  },
  filterContainer: {
    marginBottom: 20,
    paddingHorizontal: 24
  },
  filterList: {
    flexDirection: "row",
    gap: 8
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  filterTabActive: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderColor: stylesConstants.colors.brand.primary
  },
  filterTabText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  filterTabTextActive: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: 600
  },
  notificationsList: {
    flex: 1,
    paddingHorizontal: 24
  },
  notificationItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  notificationItemUnread: {
    borderColor: stylesConstants.colors.brand.primary,
    backgroundColor: stylesConstants.colors.highlightBackground
  },
  notificationContent: {
    flex: 1
  },
  notificationHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12
  },
  notificationIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.mainBackground
  },
  notificationAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20
  },
  notificationTextContainer: {
    flex: 1,
    gap: 8
  },
  clubIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: stylesConstants.colors.brand.primary,
    justifyContent: "center",
    alignItems: "center"
  },
  clubIconText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20
  },
  notificationTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    flex: 1,
    marginRight: 12
  },
  notificationTime: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18
  },
  notificationMessage: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginBottom: 8
  },
  notificationActions: {
    flexDirection: "row",
    gap: 12,
    marginTop: 12
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: stylesConstants.colors.brand.primary,
    alignSelf: "flex-start"
  },
  actionButtonSecondary: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  actionButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: stylesConstants.colors.brand.primary,
    position: "absolute",
    top: 16,
    right: 16
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40
  },
  emptyStateText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    textAlign: "center"
  }
});

export default styles;
