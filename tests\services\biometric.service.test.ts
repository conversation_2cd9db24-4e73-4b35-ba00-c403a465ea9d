import {firstValueFrom} from "rxjs";

const mockHasHardwareAsync = jest.fn();
const mockIsEnrolledAsync = jest.fn();
const mockAuthenticateAsync = jest.fn();

jest.mock("expo-local-authentication", () => ({
    hasHardwareAsync: () => mockHasHardwareAsync(),
    isEnrolledAsync: () => mockIsEnrolledAsync(),
    authenticateAsync: (config: any) => mockAuthenticateAsync(config)
}));

import BiometricService from "../../services/biometric.service";

describe("BiometricService", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("createBiometricConfig", () => {
        it("creates biometric config with translations", () => {
            const mockT = jest.fn((key: string) => key) as any;
            const config = BiometricService.createBiometricConfig(mockT);

            expect(config).toEqual({
                promptMessage: "login.biometricPrompt",
                fallbackLabel: "login.biometricFallback",
                disableDeviceFallback: false
            });
            expect(mockT).toHaveBeenCalledWith("login.biometricPrompt");
            expect(mockT).toHaveBeenCalledWith("login.biometricFallback");
        });
    });

    describe("hasBiometric", () => {
        const config = {
            promptMessage: "Test prompt",
            fallbackLabel: "Test fallback"
        };

        it("returns true when hardware available, enrolled, and authentication succeeds", async () => {
            mockHasHardwareAsync.mockResolvedValue(true);
            mockIsEnrolledAsync.mockResolvedValue(true);
            mockAuthenticateAsync.mockResolvedValue({success: true});

            const result = await firstValueFrom(
                BiometricService.hasBiometric(config)
            );

            expect(result).toBe(true);
            expect(mockHasHardwareAsync).toHaveBeenCalledTimes(1);
            expect(mockIsEnrolledAsync).toHaveBeenCalledTimes(1);
            expect(mockAuthenticateAsync).toHaveBeenCalledWith(config);
        });

        it("returns false when no hardware available", async () => {
            mockHasHardwareAsync.mockResolvedValue(false);
            mockIsEnrolledAsync.mockResolvedValue(true);

            const result = await firstValueFrom(
                BiometricService.hasBiometric(config)
            );

            expect(result).toBe(false);
            expect(mockHasHardwareAsync).toHaveBeenCalledTimes(1);
            expect(mockIsEnrolledAsync).not.toHaveBeenCalled();
            expect(mockAuthenticateAsync).not.toHaveBeenCalled();
        });

        it("returns false when not enrolled", async () => {
            mockHasHardwareAsync.mockResolvedValue(true);
            mockIsEnrolledAsync.mockResolvedValue(false);

            const result = await firstValueFrom(
                BiometricService.hasBiometric(config)
            );

            expect(result).toBe(false);
            expect(mockHasHardwareAsync).toHaveBeenCalledTimes(1);
            expect(mockIsEnrolledAsync).toHaveBeenCalledTimes(1);
            expect(mockAuthenticateAsync).not.toHaveBeenCalled();
        });

        it("returns false when authentication fails", async () => {
            mockHasHardwareAsync.mockResolvedValue(true);
            mockIsEnrolledAsync.mockResolvedValue(true);
            mockAuthenticateAsync.mockResolvedValue({success: false});

            const result = await firstValueFrom(
                BiometricService.hasBiometric(config)
            );

            expect(result).toBe(false);
            expect(mockHasHardwareAsync).toHaveBeenCalledTimes(1);
            expect(mockIsEnrolledAsync).toHaveBeenCalledTimes(1);
            expect(mockAuthenticateAsync).toHaveBeenCalledWith(config);
        });
    });
});
