import React from "react";
import {StyleProp, View, ViewStyle} from "react-native";
import styles from "../styles/components/progress-bar.style";

export interface ProgressBarProps {
    progress: number;
    style?: StyleProp<ViewStyle>;
}

const ProgressBar: React.FC<ProgressBarProps> = (props) => {
    const clamped = Math.min(Math.max(props.progress, 0), 1);

    return (
        <View style={[styles.container, props.style]}>
            <View
                style={[
                    styles.fill,
                    {
                        width: `${clamped * 100}%`
                    }
                ]}
            />
        </View>
    );
};

export default ProgressBar;
