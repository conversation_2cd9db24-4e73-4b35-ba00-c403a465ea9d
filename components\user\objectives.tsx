import React, {useMemo} from "react";
import {useTranslation} from "react-i18next";
import {Text, View} from "react-native";
import styles from "@/styles/components/user/objectives.style";
import ObjectiveCard from "./objective-card";

const Objectives: React.FC = () => {
  const {t} = useTranslation();

  const cards = useMemo(() => {
    const cardList = [];

    for (let i = 0; i < 5; i++) {
      cardList.push(
        <ObjectiveCard
          key={i}
          title={"Solo de piano"}
          description={
            "Lorem ipsum eros odio rutrum fringilla enim dictum vel eget mi eros aliquet ornare sit."
          }
          progress={0.5}
          accomplished={"5 notas tocadas."}
        />
      );
    }

    return cardList;
  }, []);

  return (
    <View>
      <View style={styles.titleContainer}>
        <Text style={styles.title}>{t("objectives.title")}</Text>
        <Text style={styles.seeMore}>{t("objectives.seeMore")}</Text>
      </View>
      <View style={styles.cardsContainer}>{cards}</View>
    </View>
  );
};

export default Objectives;
