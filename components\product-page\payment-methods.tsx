import React from "react";
import {View, Text} from "react-native";
import {useTranslation} from "react-i18next";
import PaymentMethodCard from "./payment-method-card";
import PixIcon from "../icons/payment-methods/pix-icon";
import VisaIcon from "../icons/payment-methods/visa-icon";
import MastercardIcon from "../icons/payment-methods/mastercard-icon";
import ApplePayIcon from "../icons/payment-methods/appple-pay-icon";
import GooglePayIcon from "../icons/payment-methods/google-pay-icon";
import PaypalIcon from "../icons/payment-methods/paypal-icon";
import styles from "../../styles/components/product-page/payment-methods.style";

const PaymentMethods: React.FC = () => {
    const {t} = useTranslation();

    const paymentMethods = [
        {icon: PixIcon},
        {icon: VisaIcon},
        {icon: MastercardIcon},
        {icon: ApplePayIcon},
        {icon: GooglePayIcon},
        {icon: PaypalIcon}
    ];

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Métodos de pagamento</Text>
            <View style={styles.paymentGrid}>
                {paymentMethods.map((method, index) => (
                    <PaymentMethodCard key={index + 1} icon={method.icon} />
                ))}
            </View>
            <Text>
                <Text style={styles.installmentLabel}>
                    Opções de parcelamento
                </Text>
                <Text style={styles.installmentValue}>
                    {` ${t("productPage.installments")}`}
                </Text>
            </Text>
        </View>
    );
};

export default PaymentMethods;
