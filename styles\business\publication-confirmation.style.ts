import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: stylesConstants.colors.mainBackground,
    },
    contentContainer: {
        flex: 1,
        paddingHorizontal: 24,
        paddingVertical: 40,
        justifyContent: "center",
        alignItems: "center",
    },
    iconContainer: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: stylesConstants.colors.success50,
        alignItems: "center",
        justifyContent: "center",
        marginBottom: 24,
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 24,
        fontWeight: 700,
        lineHeight: 32,
        textAlign: "center",
        marginBottom: 16,
    },
    description: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 400,
        lineHeight: 24,
        textAlign: "center",
        marginBottom: 32,
    },
    detailsContainer: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        padding: 16,
        width: "100%",
        marginBottom: 32,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
    },
    detailsTitle: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontWeight: 600,
        lineHeight: 24,
        marginBottom: 12,
    },
    detailRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 8,
    },
    detailLabel: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
    },
    detailValue: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 600,
        lineHeight: 20,
    },
    buttonContainer: {
        width: "100%",
        gap: 16,
    },
});

export default styles;
