import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface BriefcaseIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const BriefcaseIcon: React.FC<BriefcaseIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={24} height={24} fill="none" {...props}>
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M16 20V7a2 2 0 0 0-2-2H10a2 2 0 0 0-2 2v13M16 20H8M16 20l2-2V9a2 2 0 0 0-2-2h-1M8 20l-2-2V9a2 2 0 0 1 2-2h1M12 5V3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v2"
      />
    </Svg>
  );
};

export default BriefcaseIcon;
