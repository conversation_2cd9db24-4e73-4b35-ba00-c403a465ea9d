import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    alignItems: "center",
  },
  headerContainer: {
    marginBottom: 32,
    alignItems: "center",
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    textAlign: "center",
    marginBottom: 8,
  },
  subtitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    textAlign: "center",
  },
  qrCodeContainer: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 12,
    padding: 24,
    marginBottom: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  qrCode: {
    width: 200,
    height: 200,
    backgroundColor: "#f0f0f0",
    borderRadius: 8,
  },
  qrCodePlaceholder: {
    width: 200,
    height: 200,
    backgroundColor: "#f0f0f0",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  qrCodeText: {
    color: "#666",
    fontSize: 14,
    textAlign: "center",
  },
  pixKeyContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    width: "100%",
  },
  pixKeyLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    marginBottom: 8,
  },
  pixKeyValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    backgroundColor: stylesConstants.colors.mainBackground,
    borderRadius: 6,
    padding: 12,
    marginBottom: 12,
    textAlign: "center",
  },
  copyButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 6,
    padding: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  copyButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    marginLeft: 8,
  },
  valueContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    width: "100%",
    alignItems: "center",
  },
  valueLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    marginBottom: 4,
  },
  valueAmount: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
  },
  timerContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    width: "100%",
    alignItems: "center",
  },
  timerLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    marginBottom: 8,
  },
  timerValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 32,
    fontWeight: 700,
    letterSpacing: 2,
  },
  timerText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    marginTop: 4,
  },
  instructionsContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    width: "100%",
  },
  instructionsTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    marginBottom: 12,
  },
  instructionStep: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginBottom: 8,
  },
  instructionStepBold: {
    fontWeight: 600,
    color: stylesConstants.colors.fullWhite,
  },
  instructionNote: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginTop: 8,
  },
  icon: {
    fontSize: 16,
  },
  buttonContainer: {
    width: "100%",
    marginTop: 16,
  },
});

export default styles;
