import React, {useState, useCallback} from "react";
import {Text, View, ColorValue} from "react-native";
import Screen from "../../components/screen";
import BackgroundLogoTexture from "../../components/logos/background-logo-texture";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";
import MarkerPinIcon from "../../components/icons/marker-pin-icon";

const AddressRegistration: React.FC = () => {
  const {t} = useTranslation();
  const [formData, setFormData] = useState({
    zipCode: "",
    street: "",
    number: "",
    complement: "",
    neighborhood: "",
    city: "",
    state: ""
  });

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
    };

  const markerIcon = useCallback(
    (errorColor?: ColorValue) => <MarkerPinIcon replaceColor={errorColor} />,
    []
  );

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={{flex: 1, padding: 20}}>
          <BackButton />
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#fff",
              textAlign: "center",
              marginBottom: 20
            }}
          >
            {t("addressRegistration.title", "Endereço")}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "#fff",
              textAlign: "center",
              marginBottom: 30
            }}
          >
            {t("addressRegistration.description", "Informe seu endereço")}
          </Text>

          <View style={{gap: 15, marginBottom: 30}}>
            <InputField
              label={t("addressRegistration.zipCode", "CEP")}
              value={formData.zipCode}
              onChangeText={handleInputChange("zipCode")}
              placeholder={t(
                "addressRegistration.zipCodePlaceholder",
                "00000-000"
              )}
              icon={markerIcon}
              inputMode="numeric"
            />
            <InputField
              label={t("addressRegistration.street", "Rua")}
              value={formData.street}
              onChangeText={handleInputChange("street")}
              placeholder={t(
                "addressRegistration.streetPlaceholder",
                "Nome da rua"
              )}
            />
            <View style={{flexDirection: "row", gap: 10}}>
              <View style={{flex: 2}}>
                <InputField
                  label={t("addressRegistration.number", "Número")}
                  value={formData.number}
                  onChangeText={handleInputChange("number")}
                  placeholder={t(
                    "addressRegistration.numberPlaceholder",
                    "123"
                  )}
                  inputMode="numeric"
                />
              </View>
              <View style={{flex: 3}}>
                <InputField
                  label={t("addressRegistration.complement", "Complemento")}
                  value={formData.complement}
                  onChangeText={handleInputChange("complement")}
                  placeholder={t(
                    "addressRegistration.complementPlaceholder",
                    "Apto, Bloco..."
                  )}
                />
              </View>
            </View>
            <InputField
              label={t("addressRegistration.neighborhood", "Bairro")}
              value={formData.neighborhood}
              onChangeText={handleInputChange("neighborhood")}
              placeholder={t(
                "addressRegistration.neighborhoodPlaceholder",
                "Nome do bairro"
              )}
            />
            <View style={{flexDirection: "row", gap: 10}}>
              <View style={{flex: 2}}>
                <InputField
                  label={t("addressRegistration.city", "Cidade")}
                  value={formData.city}
                  onChangeText={handleInputChange("city")}
                  placeholder={t(
                    "addressRegistration.cityPlaceholder",
                    "Cidade"
                  )}
                />
              </View>
              <View style={{flex: 1}}>
                <InputField
                  label={t("addressRegistration.state", "UF")}
                  value={formData.state}
                  onChangeText={handleInputChange("state")}
                  placeholder={t("addressRegistration.statePlaceholder", "SP")}
                  maxLength={2}
                />
              </View>
            </View>
          </View>

          <FullSizeButton
            text={t("addressRegistration.next", "Próximo")}
            onPress={() => {}}
          />
        </View>
      </Screen>
    </>
  );
};

export default AddressRegistration;
