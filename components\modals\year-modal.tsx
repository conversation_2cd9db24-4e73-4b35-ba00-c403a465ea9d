import React, {useCallback} from "react";
import BottomModal from "../bottom-modal";
import YearSelector from "../calendar-screen/year-selector";
import FullSizeButton from "../full-size-button";
import InvisibleFullSizeButton from "../invisible-full-size-button";
import {useBottomModal} from "../../contexts/bottom-modal-context";
import {useTranslation} from "react-i18next";

export interface YearModalProps {
    year: number;
    onChangeYear: (newYear: number) => void;
}

const YearModal: React.FC<YearModalProps> = (props) => {
    const modal = useBottomModal();
    const {t} = useTranslation();

    const handleOnModalCloseButtonPress = useCallback(() => {
        modal.closeModal();
    }, [modal.closeModal]);

    const handleOnChangeYear = useCallback((year: number) => props.onChangeYear(year),
        [props.year, props.onChangeYear]);

    return (
        <BottomModal.Container>
            <BottomModal.FeaturedContainer>
                <YearSelector
                    initialYear={props.year}
                    onChangeYear={handleOnChangeYear}
                />
            </BottomModal.FeaturedContainer>
            <BottomModal.ButtonsContainer>
                <FullSizeButton
                    text={t("calendar.yearModal.confirmButton")}
                    onPress={handleOnModalCloseButtonPress}
                />
                <InvisibleFullSizeButton
                    text={t("calendar.yearModal.closeButton")}
                    onPress={handleOnModalCloseButtonPress}
                />
            </BottomModal.ButtonsContainer>
        </BottomModal.Container>
    );
};

export default YearModal;