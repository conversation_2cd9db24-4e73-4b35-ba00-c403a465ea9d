# Club M - React Native App

Club M is a comprehensive mobile application built with React Native and Expo, designed to provide members with access to exclusive benefits, business networking, events, and professional services.

## 📱 Features

- **Authentication & Registration**: Secure login with biometric authentication support
- **Member Benefits**: Access to exclusive partner offers and discounts
- **Business Center**: Professional networking and business tools
- **Events & Calendar**: Event management and scheduling
- **Digital Wallet**: Integrated payment and transaction management
- **Magazine**: Digital publication access
- **Profile Management**: Complete user profile and settings management
- **Notifications**: Push notifications for important updates
- **Multilingual Support**: Internationalization with i18next

## 🛠 Prerequisites

Before you begin, ensure you have the following installed on your development machine:

### Required Software

- **Node.js** (version 18 or higher)
- **Yarn** (version 1.22.22 or higher) - This project uses Yarn as the package manager
- **Git** for version control
- **Expo CLI** - Install globally with `npm install -g @expo/cli`

### Platform-Specific Requirements

#### iOS Development

- **macOS** (required for iOS development)
- **Xcode** (latest version from Mac App Store)
- **iOS Simulator** (included with Xcode)
- **CocoaPods** - Install with `sudo gem install cocoapods`

#### Android Development

- **Android Studio** with Android SDK
- **Java Development Kit (JDK)** version 11 or higher
- **Android Virtual Device (AVD)** or physical Android device
- **Android SDK Platform Tools**

### Environment Setup

1. Configure Android SDK path in your environment variables
2. Set up iOS development certificates (for physical device testing)
3. Install Expo Development Build on your physical device (optional)

## 🚀 Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/Takasaki-Studio/club-m-app.git
   cd club-m-app
   ```

2. **Install dependencies**

   ```bash
   yarn install
   ```

3. **Install iOS dependencies** (macOS only)

   ```bash
   cd ios && pod install && cd ..
   ```

4. **Set up environment configuration**
   - Ensure Firebase configuration files are in place:
     - `keys/GoogleService-Info.plist` (iOS)
     - `keys/google-services.json` (Android)

## 🏃‍♂️ Running the App

### Development Server

Start the Expo development server:

```bash
yarn start
```

### Platform-Specific Commands

#### iOS

```bash
# Run on iOS Simulator
yarn ios

# Run on specific iOS Simulator
yarn ios --simulator="iPhone 15 Pro"
```

#### Android

```bash
# Run on Android Emulator or connected device
yarn android

# Run on specific Android device
yarn android --device
```

#### Web (for testing)

```bash
yarn web
```

### Development Build

For testing native features, you can create a development build:

```bash
# Install EAS CLI globally
npm install -g eas-cli

# Build for development
eas build --profile development --platform ios
eas build --profile development --platform android
```

## 📁 Project Structure

```text
club-m-app/
├── app/                          # App screens and navigation
│   ├── (logged-stack)/          # Authenticated user screens
│   ├── (tabs)/                  # Tab navigation screens
│   ├── auth/                    # Authentication screens
│   ├── business/                # Business center screens
│   ├── events/                  # Events and calendar screens
│   ├── magazine/                # Magazine screens
│   ├── profile/                 # Profile management screens
│   ├── registration/            # User registration flow
│   ├── settings/                # App settings screens
│   ├── wallet/                  # Digital wallet screens
│   ├── _layout.tsx              # Root layout component
│   └── router.tsx               # Navigation configuration
├── components/                   # Reusable UI components
│   ├── icons/                   # Custom icon components
│   ├── modals/                  # Modal components
│   ├── login/                   # Login-specific components
│   └── ...                      # Other component categories
├── contexts/                     # React Context providers
├── hooks/                        # Custom React hooks
├── services/                     # API and external service integrations
├── models/                       # TypeScript type definitions
├── styles/                       # Styling and theme configuration
├── utils/                        # Utility functions and helpers
├── locales/                      # Internationalization files
├── assets/                       # Static assets (images, fonts, etc.)
├── tests/                        # Test files and configurations
├── android/                      # Android-specific native code
├── ios/                          # iOS-specific native code
└── keys/                         # Configuration files (not in version control)
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test --watch

# Run tests with coverage
yarn test --coverage
```

### Test Structure

- **Unit Tests**: Located in `tests/` directory
- **Component Tests**: Using React Native Testing Library
- **Service Tests**: API and business logic testing
- **Hook Tests**: Custom hook testing

## 📦 Available Scripts

| Script         | Description                    |
| -------------- | ------------------------------ |
| `yarn start`   | Start Expo development server  |
| `yarn android` | Run on Android emulator/device |
| `yarn ios`     | Run on iOS simulator/device    |
| `yarn web`     | Run web version (for testing)  |
| `yarn test`    | Run test suite                 |

## 🏗 Build and Deployment

### Development Builds

```bash
# Build for internal testing
eas build --profile development

# Build for preview/staging
eas build --profile preview
```

### Production Builds

```bash
# Build for production
eas build --profile production

# Submit to app stores
eas submit --platform ios
eas submit --platform android
```

### Build Profiles

The project uses EAS Build with the following profiles defined in `eas.json`:

- **development**: Development builds with dev client
- **preview**: Internal distribution builds
- **production**: App store ready builds

## ⚙️ Configuration

### Environment Variables

The app uses Expo's configuration system. Key configurations are in:

- `app.config.js` - Main Expo configuration
- `package.json` - Dependencies and scripts
- Firebase configuration files in `keys/` directory

### Key Configuration Files

- **app.config.js**: Expo app configuration, plugins, and build settings
- **tsconfig.json**: TypeScript configuration
- **jest-setup.js**: Test environment setup
- **metro.config.js**: Metro bundler configuration (if present)

## 🤝 Contributing

### Development Workflow

1. **Fork the repository** and create a feature branch
2. **Follow the existing code style** and conventions
3. **Write tests** for new features and bug fixes
4. **Run tests** to ensure everything works correctly
5. **Update documentation** as needed
6. **Submit a pull request** with a clear description

### Code Style Guidelines

- Use **TypeScript** for all new code
- Follow **React Native best practices**
- Use **consistent naming conventions**:
  - Components: PascalCase
  - Files: kebab-case
  - Functions: camelCase
- Write **meaningful commit messages**
- Add **JSDoc comments** for complex functions

### Testing Requirements

- Write unit tests for new utilities and services
- Add component tests for new UI components
- Ensure test coverage remains above 80%
- Test on both iOS and Android platforms

## 🔧 Troubleshooting

### Common Issues

#### Metro bundler issues

```bash
# Clear Metro cache
yarn start --clear

# Reset Metro cache completely
npx react-native start --reset-cache
```

#### iOS build issues

```bash
# Clean iOS build
cd ios && xcodebuild clean && cd ..

# Reinstall pods
cd ios && pod deintegrate && pod install && cd ..
```

#### Android build issues

```bash
# Clean Android build
cd android && ./gradlew clean && cd ..

# Reset Android project
cd android && ./gradlew cleanBuildCache && cd ..
```

#### Dependency issues

```bash
# Clear node modules and reinstall
rm -rf node_modules yarn.lock
yarn install
```

### Getting Help

- Check the [Expo documentation](https://docs.expo.dev/)
- Review [React Native documentation](https://reactnative.dev/docs/getting-started)
- Search existing issues in the repository
- Create a new issue with detailed reproduction steps

## 📄 License

This project is proprietary software developed by Takasaki Studio for Club M Brasil.

## 🏢 About

**Club M** is developed and maintained by Takasaki Studio. The app provides Club M members with a comprehensive digital platform for accessing exclusive benefits, networking opportunities, and professional services.

For more information, visit [Club M Brasil](https://clubm.com.br).

---

**Package Manager**: This project uses Yarn v1.22.22. Please use `yarn` instead of `npm` for all package management operations.

**React Native Version**: 0.79.3
**Expo SDK Version**: 53.0.10
**TypeScript**: Enabled with strict mode
