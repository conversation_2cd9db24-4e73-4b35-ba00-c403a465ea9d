import React, {useMemo, useState, useRef} from "react";
import {
    StyleProp,
    Text,
    View,
    ViewStyle,
    Dimensions,
    FlatList
} from "react-native";
import PartnerCard from "./partner-card";
import ChevronLeftIcon from "../icons/chevron-left-icon";
import ChevronRightIcon from "../icons/chevron-right-icon";
import styles from "../../styles/components/products/partners.style";

export interface PartnersProps {
    style?: StyleProp<ViewStyle>;
}

const Partners: React.FC<PartnersProps> = (props) => {
    const [currentPage, setCurrentPage] = useState(0);
    const flatListRef = useRef<FlatList>(null);

    const partners = useMemo(
        () => [
            {
                id: 1,
                name: "Partner 1",
                logo: "https://www.google.com/logos/google.jpg"
            },
            {
                id: 2,
                name: "Partner 2",
                logo: "https://www.google.com/logos/google.jpg"
            },
            {
                id: 3,
                name: "Partner 3",
                logo: "https://www.google.com/logos/google.jpg"
            },
            {
                id: 4,
                name: "Partner 4",
                logo: "https://www.google.com/logos/google.jpg"
            },
            {
                id: 5,
                name: "Partner 5",
                logo: "https://www.google.com/logos/google.jpg"
            },
            {
                id: 6,
                name: "Partner 6",
                logo: "https://www.google.com/logos/google.jpg"
            },
            {
                id: 7,
                name: "Partner 7",
                logo: "https://www.google.com/logos/google.jpg"
            },
            {
                id: 8,
                name: "Partner 8",
                logo: "https://www.google.com/logos/google.jpg"
            }
        ],
        []
    );

    const screenWidth = Dimensions.get("window").width;
    const itemWidth = 72;
    const gap = 12;
    const itemsPerPage = Math.floor(screenWidth / (itemWidth + gap));
    const totalPages = Math.ceil(partners.length / itemsPerPage);

    const handleScroll = (event: any) => {
        const scrollPosition = event.nativeEvent.contentOffset.x;
        const pageWidth = itemsPerPage * (itemWidth + gap);
        const page = Math.round(scrollPosition / pageWidth);
        setCurrentPage(page);
    };

    const renderPaginationDots = () => {
        return (
            <View style={styles.paginationContainer}>
                <ChevronLeftIcon />
                <View style={styles.dotsContainer}>
                    {Array.from({length: totalPages}, (_, index) => (
                        <View
                            key={index}
                            style={[
                                styles.dot,
                                currentPage === index
                                    ? styles.activeDot
                                    : styles.inactiveDot
                            ]}
                        />
                    ))}
                </View>
                <ChevronRightIcon />
            </View>
        );
    };

    return (
        <View style={[styles.container, props.style]}>
            <View style={styles.header}>
                <Text style={styles.title}>Parceiros e benefícios</Text>
                <Text style={styles.viewAllText}>Ver todos</Text>
            </View>
            <View style={styles.content}>
                <FlatList
                    ref={flatListRef}
                    data={partners}
                    contentContainerStyle={styles.flatListContainer}
                    renderItem={({item}) => (
                        <PartnerCard name={item.name} logo={item.logo} />
                    )}
                    keyExtractor={(item) => item.id.toString()}
                    horizontal={true}
                    showsHorizontalScrollIndicator={false}
                    scrollEnabled={true}
                    onScroll={handleScroll}
                    scrollEventThrottle={16}
                    snapToInterval={itemsPerPage * (itemWidth + gap)}
                    snapToAlignment="start"
                    decelerationRate="fast"
                />
                {totalPages > 1 && renderPaginationDots()}
            </View>
        </View>
    );
};

export default Partners;
