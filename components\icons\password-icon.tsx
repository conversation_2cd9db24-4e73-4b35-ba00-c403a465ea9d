import Svg, {Path, SvgProps} from "react-native-svg";
import React, {useMemo} from "react";
import {ColorValue} from "react-native";

export interface PasswordIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const PasswordIcon: React.FC<PasswordIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#F2F4F7", [props.replaceColor]);

    return (
        <Svg
            width={20}
            height={20}
            viewBox="0 0 20 20"
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.667}
                d="M10 10h.004m4.163 0h.004m-8.338 0h.004M4.333 5.833h11.334c.933 0 1.4 0 1.756.182.314.16.569.415.729.728.181.357.181.824.181 1.757v3c0 .934 0 1.4-.181 1.757-.16.313-.415.568-.729.728-.356.182-.823.182-1.756.182H4.333c-.933 0-1.4 0-1.756-.182a1.667 1.667 0 0 1-.729-.728c-.181-.357-.181-.823-.181-1.757v-3c0-.933 0-1.4.181-1.757.16-.313.415-.568.729-.728.356-.182.823-.182 1.756-.182ZM10.208 10a.208.208 0 1 1-.416 0 .208.208 0 0 1 .416 0Zm4.167 0a.208.208 0 1 1-.417 0 .208.208 0 0 1 .417 0Zm-8.333 0a.208.208 0 1 1-.417 0 .208.208 0 0 1 .417 0Z"
            />
        </Svg>
    );
};

export default PasswordIcon;