import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const CalendarTimeIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={20}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke="#F2F4F7"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.67}
                d="M9.381 17.456h-5.96a1.754 1.754 0 0 1-1.754-1.754V5.175A1.754 1.754 0 0 1 3.42 3.421h10.526a1.754 1.754 0 0 1 1.755 1.754v3.51"
            />
            <Path
                stroke="#F2F4F7"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.67}
                d="M12.193 1.667v3.509M5.175 1.667v3.509M1.667 8.685h14.035m-4.386 6.14a3.509 3.509 0 1 0 7.018 0 3.509 3.509 0 0 0-7.018 0Z"
            />
            <Path
                stroke="#F2F4F7"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.67}
                d="M14.824 13.506v1.32l.877.876"
            />
        </Svg>
    );
};

export default CalendarTimeIcon;