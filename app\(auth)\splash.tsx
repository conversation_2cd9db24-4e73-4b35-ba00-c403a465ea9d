import React from "react";
import {Text, View} from "react-native";
import Screen from "@/components/screen";
import {useTranslation} from "react-i18next";

const Splash: React.FC = () => {
  const {t} = useTranslation();

  return (
    <Screen>
      <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
        <Text style={{fontSize: 24, fontWeight: "bold", color: "#fff"}}>
          {t("splash.title", "Club M")}
        </Text>
        <Text style={{fontSize: 16, color: "#fff", marginTop: 10}}>
          {t("splash.loading", "Carregando...")}
        </Text>
      </View>
    </Screen>
  );
};

export default Splash;
