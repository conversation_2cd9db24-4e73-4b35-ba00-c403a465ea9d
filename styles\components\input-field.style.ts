import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        gap: 6,
        alignSelf: "stretch"
    },
    label: {
        color: stylesConstants.colors.secondary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontStyle: "normal",
        fontWeight: 600,
        lineHeight: 20,
        marginBottom: 6
    },
    multilineField: {
        height: 120,
        alignItems: "flex-start",
        paddingVertical: 12
    },
    multilineInput: {
        textAlignVertical: "top",
        paddingVertical: 0,
        flex: 1
    },
    characterCount: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontStyle: "normal",
        fontWeight: 400,
        lineHeight: 18,
        color: stylesConstants.colors.gray100,
        marginTop: 6
    },
    field: {
        display: "flex",
        flexDirection: "row",
        paddingHorizontal: 12,
        paddingVertical: 8,
        alignItems: "center",
        gap: 8,
        alignSelf: "stretch",
        borderRadius: 8,
        borderWidth: 1,
        borderStyle: "solid",
        borderColor: stylesConstants.colors.gray100,
        height: 48
    },
    inputText: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        includeFontPadding: false,
        fontStyle: "normal",
        alignItems: "center",
        textAlignVertical: "center",
        fontWeight: 400,
        color: stylesConstants.colors.gray100,
        flex: 1,
        zIndex: 20,
        paddingVertical: 0,
        justifyContent: "center"
    },
    errorBorder: {
        borderColor: stylesConstants.colors.error300
    },
    error: {
        color: stylesConstants.colors.error300
    },
    errorText: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontStyle: "normal",
        fontWeight: 400,
        lineHeight: 20,
        marginTop: 6
    }
});

export default styles;
