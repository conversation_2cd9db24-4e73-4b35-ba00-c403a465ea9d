import React, {useState} from "react";
import {Text, View} from "react-native";
import Screen from "../../components/screen";
import BackgroundLogoTexture from "../../components/logos/background-logo-texture";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";

const PaymentCardRegistration: React.FC = () => {
    const {t} = useTranslation();
    const [formData, setFormData] = useState({
        cardNumber: "",
        cardName: "",
        expiryDate: "",
        cvv: ""
    });

    const handleInputChange = (field: keyof typeof formData) => (value: string) => {
        setFormData(prev => ({...prev, [field]: value}));
    };

    return (
        <>
            <BackgroundLogoTexture />
            <Screen>
                <View style={{flex: 1, padding: 20}}>
                    <BackButton />
                    <Text style={{fontSize: 24, fontWeight: 'bold', color: '#fff', textAlign: 'center', marginBottom: 20}}>
                        {t("paymentCardRegistration.title", "Cadastro de Cartão")}
                    </Text>
                    <Text style={{fontSize: 16, color: '#fff', textAlign: 'center', marginBottom: 30}}>
                        {t("paymentCardRegistration.description", "Adicione um método de pagamento")}
                    </Text>
                    
                    <View style={{gap: 15, marginBottom: 30}}>
                        <InputField
                            label={t("paymentCardRegistration.cardNumber", "Número do Cartão")}
                            value={formData.cardNumber}
                            onChangeText={handleInputChange("cardNumber")}
                            placeholder={t("paymentCardRegistration.cardNumberPlaceholder", "0000 0000 0000 0000")}
                            inputMode="numeric"
                            maxLength={19}
                        />
                        <InputField
                            label={t("paymentCardRegistration.cardName", "Nome no Cartão")}
                            value={formData.cardName}
                            onChangeText={handleInputChange("cardName")}
                            placeholder={t("paymentCardRegistration.cardNamePlaceholder", "Nome como está no cartão")}
                        />
                        <View style={{flexDirection: 'row', gap: 10}}>
                            <View style={{flex: 1}}>
                                <InputField
                                    label={t("paymentCardRegistration.expiryDate", "Validade")}
                                    value={formData.expiryDate}
                                    onChangeText={handleInputChange("expiryDate")}
                                    placeholder={t("paymentCardRegistration.expiryDatePlaceholder", "MM/AA")}
                                    maxLength={5}
                                />
                            </View>
                            <View style={{flex: 1}}>
                                <InputField
                                    label={t("paymentCardRegistration.cvv", "CVV")}
                                    value={formData.cvv}
                                    onChangeText={handleInputChange("cvv")}
                                    placeholder={t("paymentCardRegistration.cvvPlaceholder", "123")}
                                    inputMode="numeric"
                                    maxLength={4}
                                    isPassword
                                />
                            </View>
                        </View>
                    </View>
                    
                    <View style={{backgroundColor: 'rgba(255,255,255,0.1)', padding: 15, borderRadius: 8, marginBottom: 30}}>
                        <Text style={{color: '#fff', fontSize: 14, textAlign: 'center'}}>
                            {t("paymentCardRegistration.securityNote", "🔒 Seus dados estão seguros e criptografados")}
                        </Text>
                    </View>
                    
                    <FullSizeButton
                        text={t("paymentCardRegistration.next", "Próximo")}
                        onPress={() => {}}
                    />
                </View>
            </Screen>
        </>
    );
};

export default PaymentCardRegistration;
