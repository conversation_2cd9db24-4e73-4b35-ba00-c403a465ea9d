import React from "react";
import { render } from "@testing-library/react-native";
import { Text } from "react-native";

jest.mock("../../styles/components/screen.style", () => ({ screenView: { flex: 1 } }));

import Screen from "../../components/screen";

describe("Screen component", () => {
  it("renders children inside container", () => {
    const { getByText } = render(<Screen><Text>Child</Text></Screen>);
    expect(getByText("Child")).toBeTruthy();
  });
});
