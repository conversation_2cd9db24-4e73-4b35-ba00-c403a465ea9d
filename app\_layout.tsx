import React, {useEffect, useMemo} from "react";
import * as SplashScreen from "expo-splash-screen";
import {StatusBar} from "expo-status-bar";
import Animated<PERSON>ogo from "@/components/logos/animated-logo";
import "@/utils/i18n";
import {useReactQueryDevTools} from "@dev-plugins/react-query";
import ApiService from "@/services/api.service";
import ErrorProvider from "@/contexts/error-dialog-context";
import ErrorDialog from "@/components/error-dialog";
import LoadingProvider from "@/contexts/loading-context";
import {Stack} from "expo-router";
import SessionProvider from "@/contexts/session.context";
import BottomModal from "@/components/bottom-modal";
import BottomModalProvider from "@/contexts/bottom-modal-context";
import {onlineManager, QueryClientProvider} from "@tanstack/react-query";
import * as Network from "expo-network";
import styles from "@/styles/router.style";

SplashScreen.preventAutoHideAsync().catch(() => {
  console.error("SplashScreen.preventAutoHideAsync() failed");
});

onlineManager.setEventListener((setOnline) => {
  const eventSubscription = Network.addNetworkStateListener((state) => {
    setOnline(!!state.isConnected);
  });
  return eventSubscription.remove;
});

const Layout: React.FC = () => {
  useReactQueryDevTools(ApiService.queryClient);

  const screenOptions = useMemo(
    () => ({
      contentStyle: styles.container,
      headerShown: false,
      animation: "fade_from_bottom" as const,
      gestureEnabled: true
    }),
    []
  );

  useEffect(() => {
    SplashScreen.hideAsync().catch(() => {
      console.error("SplashScreen.hideAsync() failed");
    });
  }, []);

  return (
    <QueryClientProvider client={ApiService.queryClient}>
      <LoadingProvider>
        <ErrorProvider>
          <BottomModalProvider>
            <SessionProvider>
              <StatusBar style="light" />
              <ErrorDialog />
              <BottomModal />
              <AnimatedLogo>
                <Stack screenOptions={screenOptions} />
              </AnimatedLogo>
            </SessionProvider>
          </BottomModalProvider>
        </ErrorProvider>
      </LoadingProvider>
    </QueryClientProvider>
  );
};

export default Layout;
