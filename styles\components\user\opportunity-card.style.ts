import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        flexDirection: "row",
        width: "100%"
    },
    content: {
        paddingLeft: 12,
        paddingRight: 24,
        gap: 8
    },
    header: {
        flexDirection: "row",
        gap: 4,
        flexWrap: "wrap"
    },
    name: {
        color: stylesConstants.colors.textPrimary,
        fontSize: 12,
        fontWeight: 600,
        lineHeight: 18
    },
    label: {
        color: stylesConstants.colors.gray50,
        fontSize: 11,
        fontWeight: 400,
        lineHeight: 18
    },
    date: {
        color: stylesConstants.colors.gray50,
        fontSize: 11,
        fontWeight: 600,
        lineHeight: 18
    },
    description: {
        color: stylesConstants.colors.textPrimary,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18
    },
    seeMoreText: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 700,
        lineHeight: 18,
        color: stylesConstants.colors.secondaryBrand200
    },
    image: {
        width: "100%",
        height: 120,
        borderRadius: 4,
        marginTop: 10,
        marginBottom: 12
    },
    value: {
        color: stylesConstants.colors.textPrimary,
        fontSize: 14,
        fontWeight: 700,
        lineHeight: 20
    },
    valueContainer: {
        gap: 4
    },
    footer: {
        marginTop: 10,
        flexDirection: "row",
        justifyContent: "space-between"
    },
    viewOpportunityButton: {
        borderWidth: 1.5
    }
});

export default styles;
