import React, {useState, useCallback} from "react";
import {Modal, View, Text, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../full-size-button";
import InvisibleFullSizeButton from "../invisible-full-size-button";
import Input<PERSON>ield from "../input-field";
import CheckCircleIcon from "../icons/check-circle-icon";
import UserIcon from "../icons/user-icon";
import styles from "@/styles/modals/invite-friends-modal.style";

export interface InviteFriendsModalProps {
  onClose: () => void;
}

const InviteFriendsModal: React.FC<InviteFriendsModalProps> = ({onClose}) => {
  const {t} = useTranslation();
  const [name, setName] = useState("");
  const [cpf, setCpf] = useState("");
  const [phone, setPhone] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSendInvite = useCallback(() => {
    if (!name || !cpf || !phone) {
      return;
    }
    setShowSuccess(true);
  }, [name, cpf, phone]);

  const handleBack = useCallback(() => {
    if (showSuccess) {
      setShowSuccess(false);
      setName("");
      setCpf("");
      setPhone("");
    } else {
      onClose();
    }
  }, [showSuccess, onClose]);

  const formatCpf = (value: string) => {
    const numbers = value.replace(/\D/g, "");
    return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
  };

  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, "");
    return numbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  };

  const handleCpfChange = (value: string) => {
    const formatted = formatCpf(value);
    setCpf(formatted);
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhone(value);
    setPhone(formatted);
  };

  return (
    <Modal
      visible={true}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>
            {t("inviteFriends.title", "Convidar amigos")}
          </Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </View>

        {showSuccess && (
          <View style={styles.successContainer}>
            <CheckCircleIcon width={24} height={24} />
            <Text style={styles.successText}>
              {t("inviteFriends.successMessage", "O convite foi enviado com sucesso!")}
            </Text>
          </View>
        )}

        <View style={styles.content}>
          <Text style={styles.description}>
            {t("inviteFriends.description", "Convide amigos para o clube e concorra a prêmios e ganhe até 20% OFF na sua próxima compra ou assinatura no app.")}
          </Text>

          <View style={styles.formContainer}>
            <InputField
              label={t("inviteFriends.nameLabel", "Insira o nome completo do indicado")}
              placeholder={t("inviteFriends.namePlaceholder", "Insira o nome completo do indicado")}
              value={name}
              onChangeText={setName}
              icon={() => <UserIcon />}
            />

            <InputField
              label={t("inviteFriends.cpfLabel", "Insira o CPF do indicado")}
              placeholder={t("inviteFriends.cpfPlaceholder", "Insira o CPF do indicado")}
              value={cpf}
              onChangeText={handleCpfChange}
              inputMode="numeric"
              maxLength={14}
            />

            <InputField
              label={t("inviteFriends.phoneLabel", "Insira o telefone do indicado")}
              placeholder={t("inviteFriends.phonePlaceholder", "Insira o telefone do indicado")}
              value={phone}
              onChangeText={handlePhoneChange}
              inputMode="tel"
              maxLength={15}
            />
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <FullSizeButton
            text={t("inviteFriends.sendButton", "Enviar convite")}
            onPress={handleSendInvite}
          />
          <InvisibleFullSizeButton
            text={t("inviteFriends.backButton", "Voltar")}
            onPress={handleBack}
          />
        </View>
      </View>
    </Modal>
  );
};

export default InviteFriendsModal;
