import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  formContainer: {
    gap: 24,
    marginBottom: 32
  },
  inputGroup: {
    gap: 8
  },
  label: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: "top"
  },
  imageUploadContainer: {
    backgroundColor: "transparent",
    borderRadius: 8,
    padding: 32,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
    gap: 8
  },
  imageUploadText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    textAlign: "center"
  },
  imageUploadSubtext: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    textAlign: "center"
  },
  pickerContainer: {
    backgroundColor: stylesConstants.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault,
    paddingHorizontal: 12,
    paddingVertical: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    minHeight: 48
  },
  pickerText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    flex: 1
  },
  pickerPlaceholder: {
    color: stylesConstants.colors.textPrimary
  },
  locationContainer: {
    backgroundColor: stylesConstants.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault,
    paddingHorizontal: 12,
    paddingVertical: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    minHeight: 48
  },
  locationText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    flex: 1
  },
  buttonContainer: {
    gap: 16
  },
  // Payment section styles
  paymentMethodsContainer: {
    gap: 16,
    marginBottom: 32
  },
  paymentMethodCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "transparent"
  },
  paymentMethodCardSelected: {
    borderColor: stylesConstants.colors.brand.primary
  },
  paymentMethodIcon: {
    width: 40,
    height: 40,
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  paymentMethodInfo: {
    flex: 1,
    marginRight: 12
  },
  paymentMethodName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    marginBottom: 2
  },
  paymentMethodDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: stylesConstants.colors.borderDefault,
    justifyContent: "center",
    alignItems: "center"
  },
  radioButtonSelected: {
    borderColor: stylesConstants.colors.brand.primary
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: stylesConstants.colors.brand.primary
  }
});

export default styles;
