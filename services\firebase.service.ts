import {from, map, Observable} from "rxjs";
import {Platform} from "react-native";
import * as Notifications from "expo-notifications";
import * as Device from "expo-device";
import stylesConstants from "../styles/styles-constants";

class FirebaseService {
    public static initPushNotification(): Observable<string | null> {
        return from(this.registerDeviceInFirebase()).pipe(map((token) => {
            Notifications.setNotificationHandler({
                handleNotification: async () => ({
                    shouldPlaySound: true,
                    shouldSetBadge: true,
                    shouldShowBanner: true,
                    shouldShowList: true,
                })
            });

            return token;
        }))
    }

    private static async registerDeviceInFirebase(): Promise<string | null> {
        if (!Device.isDevice) {
            console.warn("This device does not support push notifications");
            return null;
        }

        if (Platform.OS == "android") {
            await Notifications.setNotificationChannelAsync("default", {
                name: "default",
                importance: Notifications.AndroidImportance.MAX,
                vibrationPattern: [0, 250, 250, 250],
                lightColor: stylesConstants.colors.brand.primary
            });
        }

        const notificationPermissions = await Notifications.getPermissionsAsync();
        let finalStatus = notificationPermissions.status;
        while (finalStatus != "granted") {
            finalStatus = (await Notifications.requestPermissionsAsync()).status;
        }

        const resultToken = await Notifications.getDevicePushTokenAsync();

        return resultToken.data;
    }
}

export default FirebaseService;