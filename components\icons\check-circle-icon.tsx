import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const CheckCircleIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={29}
            viewBox="0 0 29 28"
            height={28}
            fill="none"
            {...props}
        >
            <Path
                stroke="#079455"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2.5}
                d="M26.167 12.933v1.074a11.667 11.667 0 1 1-6.919-10.664m6.919 1.324L14.5 16.345l-3.5-3.5"
            />
        </Svg>
    );
};

export default CheckCircleIcon;