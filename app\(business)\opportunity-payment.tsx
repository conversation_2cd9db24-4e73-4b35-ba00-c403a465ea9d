import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import <PERSON>WithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";

const OpportunityPayment: React.FC = () => {
  const {t} = useTranslation();
  const [selectedPlan, setSelectedPlan] = useState("basic");

  const plans = [
    {
      id: "basic",
      name: t("opportunityPayment.basic.name", "Básico"),
      price: "R$ 49,90",
      duration: t("opportunityPayment.basic.duration", "7 dias"),
      features: [
        t("opportunityPayment.basic.feature1", "Publicação por 7 dias"),
        t("opportunityPayment.basic.feature2", "Até 50 visualizações"),
        t("opportunityPayment.basic.feature3", "Suporte básico")
      ]
    },
    {
      id: "premium",
      name: t("opportunityPayment.premium.name", "Premium"),
      price: "R$ 99,90",
      duration: t("opportunityPayment.premium.duration", "15 dias"),
      features: [
        t("opportunityPayment.premium.feature1", "Publicação por 15 dias"),
        t("opportunityPayment.premium.feature2", "Visualizações ilimitadas"),
        t("opportunityPayment.premium.feature3", "Destaque na busca"),
        t("opportunityPayment.premium.feature4", "Suporte prioritário")
      ]
    },
    {
      id: "enterprise",
      name: t("opportunityPayment.enterprise.name", "Empresarial"),
      price: "R$ 199,90",
      duration: t("opportunityPayment.enterprise.duration", "30 dias"),
      features: [
        t("opportunityPayment.enterprise.feature1", "Publicação por 30 dias"),
        t("opportunityPayment.enterprise.feature2", "Visualizações ilimitadas"),
        t("opportunityPayment.enterprise.feature3", "Destaque premium"),
        t("opportunityPayment.enterprise.feature4", "Analytics detalhados"),
        t("opportunityPayment.enterprise.feature5", "Suporte dedicado")
      ]
    }
  ];

  return (
    <ScreenWithHeader
      screenTitle={t("opportunityPayment.title", "Pagamento da Oportunidade")}
      backButton
    >
      <ScrollView style={{flex: 1, padding: 20}}>
        <Text
          style={{
            color: "#fff",
            fontSize: 16,
            marginBottom: 20,
            lineHeight: 22
          }}
        >
          {t(
            "opportunityPayment.description",
            "Escolha o plano ideal para dar mais visibilidade à sua oportunidade"
          )}
        </Text>

        <View style={{gap: 15, marginBottom: 30}}>
          {plans.map((plan) => (
            <TouchableOpacity
              key={plan.id}
              onPress={() => setSelectedPlan(plan.id)}
              style={{
                backgroundColor:
                  selectedPlan === plan.id
                    ? "rgba(0,122,255,0.2)"
                    : "rgba(255,255,255,0.1)",
                padding: 20,
                borderRadius: 12,
                borderWidth: selectedPlan === plan.id ? 2 : 1,
                borderColor:
                  selectedPlan === plan.id ? "#007AFF" : "rgba(255,255,255,0.2)"
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 10
                }}
              >
                <Text style={{color: "#fff", fontSize: 18, fontWeight: "bold"}}>
                  {plan.name}
                </Text>
                <View style={{alignItems: "flex-end"}}>
                  <Text
                    style={{color: "#fff", fontSize: 20, fontWeight: "bold"}}
                  >
                    {plan.price}
                  </Text>
                  <Text style={{color: "#ccc", fontSize: 12}}>
                    {plan.duration}
                  </Text>
                </View>
              </View>

              <View style={{gap: 5}}>
                {plan.features.map((feature, index) => (
                  <Text
                    key={`feature-${plan.id}-${index}`}
                    style={{color: "#ccc", fontSize: 14}}
                  >
                    ✓ {feature}
                  </Text>
                ))}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View
          style={{
            backgroundColor: "rgba(255,255,255,0.1)",
            padding: 15,
            borderRadius: 8,
            marginBottom: 20
          }}
        >
          <Text
            style={{
              color: "#fff",
              fontSize: 14,
              fontWeight: "bold",
              marginBottom: 5
            }}
          >
            💡 {t("opportunityPayment.tip", "Dica:")}
          </Text>
          <Text style={{color: "#ccc", fontSize: 12, lineHeight: 18}}>
            {t(
              "opportunityPayment.tipText",
              "Oportunidades premium recebem 5x mais candidaturas e são vistas por profissionais mais qualificados."
            )}
          </Text>
        </View>

        <FullSizeButton
          text={t("opportunityPayment.continue", "Continuar para Pagamento")}
          onPress={() => {}}
        />
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default OpportunityPayment;
