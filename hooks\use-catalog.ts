import {useInfiniteQuery} from "@tanstack/react-query";
import ProductService from "@/services/products.service";
import {createInfinityQueryOptions} from "@/utils/react-query-utils";

function useCatalog(initialPage: number = 1, pageSize: number = 5) {
  const catalogQuery = useInfiniteQuery(
    createInfinityQueryOptions(
      ["products", "catalog", pageSize],
      (page, pageSize) => ProductService.getRecommended(page, pageSize),
      initialPage,
      pageSize
    )
  );

  return {
    catalogQuery
  };
}

export default useCatalog;
