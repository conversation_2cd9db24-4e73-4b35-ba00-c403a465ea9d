import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    backgroundImage: {
        width: "100%",
        height: 150,
        marginTop: -60,
        zIndex: -1,
        transform: [
            {
                scale: 1.5
            }
        ],
        opacity: 0.2
    },
    mainContainer: {
        backgroundColor: stylesConstants.colors.mainBackground,
        borderWidth: 1,
        borderColor: stylesConstants.colors.mainBackgroundBorder,
        borderStyle: "solid",
        marginTop: -50,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        height: "100%",
        paddingHorizontal: 24,
        paddingTop: 50
    },
    iconContainer: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        marginTop: -82
    },
    iconWrapper: {
        width: 64,
        height: 64,
        borderRadius: 8,
        justifyContent: "center",
        alignItems: "center",
        padding: 12,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
        backgroundColor: stylesConstants.colors.highlightBackground
    },
    contentContainer: {
        marginTop: 13,
        display: "flex",
        gap: 32
    },
    productInfoContainer: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
    productTitle: {
        fontSize: 16,
        fontWeight: 700,
        lineHeight: 24,
        color: stylesConstants.colors.textPrimary,
        marginBottom: 12
    },
    descriptionContainer: {
        width: "100%"
    },
    descriptionText: {
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 20,
        color: stylesConstants.colors.textPrimary,
        textAlign: "justify"
    },
    seeMoreText: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 700,
        lineHeight: 20,
        color: stylesConstants.colors.secondaryBrand200
    },
    bottomSection: {
        marginTop: 24,
        gap: 24
    },
    acquire: {
        width: "100%",
        marginTop: 24
    }
});

export default styles;
