import React, {createContext, PropsWithChildren, useContext, useMemo, useState} from "react";

interface LoadingContextElements {
    currentLoading: boolean;
    setCurrentLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

const LoadingContext = createContext<Partial<LoadingContextElements>>({});

const LoadingProvider: React.FC<PropsWithChildren> = (props) => {
    const [currentLoading, setCurrentLoading] = useState<boolean>(false);
    const providerValue = useMemo(() => ({currentLoading, setCurrentLoading}),
        [currentLoading]);

    return (
        <LoadingContext.Provider value={providerValue}>
            {props.children}
        </LoadingContext.Provider>
    );
};

export function useLoading() {
    const {setCurrentLoading, currentLoading} = useContext(LoadingContext);

    return {
        currentLoading,
        setCurrentLoading
    };
}

export default LoadingProvider;