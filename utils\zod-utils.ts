import {z} from "zod";
import {TFunction} from "i18next";

export function formatZodError(error: z.ZodError): Record<string, string> {
    const { fieldErrors } = error.flatten();
    return Object.fromEntries(
        Object.entries(fieldErrors).map(([key, errs]) => [
            key,
            errs?.[0] ?? ""
        ])
    );
}

export function formatZodErrorToString(error: z.ZodError, t?: TFunction<"translation">): string {
    const errors= formatZodError(error);
    const messages = Object.values(errors).map(value => t?.(value) ?? value);

    const uniqueMessages = Array.from(new Set(messages));
    return uniqueMessages.join(', ');
}