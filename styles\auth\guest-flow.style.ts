import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: stylesConstants.colors.primary,
        paddingHorizontal: 24,
        paddingVertical: 40,
    },
    content: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    logoContainer: {
        marginBottom: 32,
        alignItems: "center",
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 24,
        fontWeight: 700,
        fontFamily: stylesConstants.fonts.openSans,
        textAlign: "center",
        marginBottom: 16,
        lineHeight: 32,
    },
    description: {
        color: stylesConstants.colors.textPrimary,
        fontSize: 16,
        fontFamily: stylesConstants.fonts.openSans,
        textAlign: "center",
        lineHeight: 24,
        marginBottom: 32,
    },
    buttonContainer: {
        width: "100%",
        gap: 16,
    },
});

export default styles;
