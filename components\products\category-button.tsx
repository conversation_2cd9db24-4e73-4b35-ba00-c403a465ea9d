import {View, Text} from "react-native";
import styles from "../../styles/components/products/category-button.style";

export interface CategoryButtonProps {
    title: string;
    icon: React.ReactNode;
}

const CategoryButton: React.FC<CategoryButtonProps> = (props) => {
    return (
        <View style={styles.container}>
            <View style={styles.iconContainer}>
                {props.icon}
            </View>
            <Text style={styles.title}>
                {props.title}
            </Text>
        </View>
    );
};

export default CategoryButton;
