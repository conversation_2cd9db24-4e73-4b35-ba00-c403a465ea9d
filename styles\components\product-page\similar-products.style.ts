import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 16
    },
    title: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 18,
        fontWeight: "700",
        lineHeight: 28,
        color: stylesConstants.colors.textPrimary
    },
    scrollContainer: {
        gap: 16
    },
    productCard: {
        width: 306,
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault
    },
    productContent: {
        padding: 16,
        gap: 8
    },
    tagsContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 6
    },
    productTitle: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 700,
        lineHeight: 20,
        color: stylesConstants.colors.fullWhite
    },
    priceContainer: {
        gap: 4
    },
    currentPrice: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 700,
        lineHeight: 20,
        color: stylesConstants.colors.textPrimary
    },
    bottomActions: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginTop: 8
    }
});

export default styles;
