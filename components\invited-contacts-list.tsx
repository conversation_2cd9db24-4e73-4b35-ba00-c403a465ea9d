import React, {useCallback} from "react";
import {View, Text, ScrollView, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import ScreenWithHeader from "./screen-with-header";
import InputField from "./input-field";
import InvisibleFullSizeButton from "./invisible-full-size-button";
import styles from "@/styles/components/invited-contacts-list.style";

export interface InvitedContactsListProps {
  onClose: () => void;
}

interface Contact {
  id: string;
  name: string;
  status: "invited" | "active" | "not_active";
  inviteDate: string;
}

const InvitedContactsList: React.FC<InvitedContactsListProps> = ({onClose}) => {
  const {t} = useTranslation();
  const [searchTerm, setSearchTerm] = React.useState("");

  // Mock data - in a real app this would come from an API
  const contacts: Contact[] = [
    {
      id: "1",
      name: "<PERSON>",
      status: "invited",
      inviteDate: "17/03/2025",
    },
    {
      id: "2", 
      name: "<PERSON>",
      status: "invited",
      inviteDate: "12/05/2025",
    },
    {
      id: "3",
      name: "<PERSON><PERSON> <PERSON>",
      status: "not_active",
      inviteDate: "17/03/2025",
    },
    {
      id: "4",
      name: "Candice Wu",
      status: "invited",
      inviteDate: "23/05/2025",
    },
    {
      id: "5",
      name: "Natali Craig",
      status: "active",
      inviteDate: "12/05/2025",
    },
    {
      id: "6",
      name: "Orlando Diggs",
      status: "active",
      inviteDate: "17/03/2025",
    },
  ];

  const getStatusText = (status: Contact["status"]) => {
    switch (status) {
      case "invited":
        return t("contactsList.statusInvited", "Convite enviado");
      case "active":
        return t("contactsList.statusActive", "Membro Ativo");
      case "not_active":
        return t("contactsList.statusNotActive", "Não aceito");
      default:
        return "";
    }
  };

  const getStatusColor = (status: Contact["status"]) => {
    switch (status) {
      case "invited":
        return styles.statusInvited;
      case "active":
        return styles.statusActive;
      case "not_active":
        return styles.statusNotActive;
      default:
        return styles.statusInvited;
    }
  };

  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderContactItem = (contact: Contact) => (
    <View key={contact.id} style={styles.contactItem}>
      <View style={styles.avatar}>
        <Text style={styles.avatarText}>
          {contact.name.split(" ").map(n => n[0]).join("").substring(0, 2)}
        </Text>
      </View>
      <View style={styles.contactInfo}>
        <Text style={styles.contactName}>{contact.name}</Text>
        <Text style={styles.inviteDate}>
          {t("contactsList.invitedOn", "Enviado em:")} {contact.inviteDate}
        </Text>
      </View>
      <View style={[styles.statusBadge, getStatusColor(contact.status)]}>
        <Text style={styles.statusText}>{getStatusText(contact.status)}</Text>
      </View>
    </View>
  );

  return (
    <ScreenWithHeader 
      screenTitle={t("contactsList.title", "Lista de convidados")} 
      backButton
    >
      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <InputField
            placeholder={t("contactsList.searchPlaceholder", "Buscar usuário")}
            value={searchTerm}
            onChangeText={setSearchTerm}
          />
        </View>

        <ScrollView style={styles.contactsList} showsVerticalScrollIndicator={false}>
          {filteredContacts.map(renderContactItem)}
        </ScrollView>

        <View style={styles.buttonContainer}>
          <InvisibleFullSizeButton
            text={t("contactsList.backButton", "Voltar")}
            onPress={onClose}
          />
        </View>
      </View>
    </ScreenWithHeader>
  );
};

export default InvitedContactsList;
