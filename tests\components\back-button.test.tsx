import React from "react";
import {render, fireEvent} from "@testing-library/react-native";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({t: (k: string) => k})
}));

const mockBack = jest.fn();
jest.mock("expo-router", () => ({useRouter: () => ({back: mockBack})}));

jest.mock("@/styles/components/back-button.style", () => ({
  container: {},
  text: {}
}));

jest.mock("@/components/icons/chevron-left-icon", () => () => null);

import BackButton from "@/components/back-button";

describe("BackButton", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("calls router.back when no onPress prop", () => {
    const {getByText} = render(<BackButton />);
    fireEvent.press(getByText("components.backButton"));
    expect(mockBack).toHaveBeenCalledTimes(1);
  });

  it("uses custom onPress prop instead of router.back", () => {
    const customPress = jest.fn();
    const {getByText} = render(<BackButton onPress={customPress} />);
    fireEvent.press(getByText("components.backButton"));
    expect(customPress).toHaveBeenCalledTimes(1);
    expect(mockBack).not.toHaveBeenCalled();
  });
});
