import React from "react";
import {Image} from "expo-image";
import {View} from "react-native";
import styles from "@/styles/components/intro/slide.style";
import {ImageSource} from "expo-image/src/Image.types";

export interface SlideProps {
  id: number;
  image: ImageSource;
  imageLeftPosition?: number;
}

const Slide: React.FC<SlideProps> = (props) => {
  return (
    <View key={props.id} style={styles.container}>
      <Image
        source={props.image}
        style={styles.container}
        contentFit="cover"
        contentPosition={{left: `${props.imageLeftPosition}%`}}
      />
    </View>
  );
};

export default Slide;
