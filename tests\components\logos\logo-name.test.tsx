import React from "react";
import { render } from "@testing-library/react-native";

jest.doMock("react-native-svg", () => {
  const { View } = require("react-native");
  const Mock = (props: any) => <View {...props} />;
  return { __esModule: true, default: Mock, Path: Mock };
});

import LogoName from "../../../components/logos/logo-name";

describe("LogoName", () => {
  it("renders default svg", () => {
    const { toJSON, getByTestId } = render(<LogoName testID="logo" />);
    expect(getByTestId("logo")).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it("forwards props", () => {
    const { getByTestId } = render(<LogoName width={100} height={30} testID="logo" />);
    const svg = getByTestId("logo");
    expect(svg.props.width).toBe(100);
    expect(svg.props.height).toBe(30);
  });
}); 