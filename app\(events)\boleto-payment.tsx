import React, {useState, useEffect} from "react";
import {Text, View, ScrollView, TouchableOpacity, Alert} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import {router} from "expo-router";
import styles from "../../styles/events/boleto-payment.style";

const BoletoPayment: React.FC = () => {
  const {t} = useTranslation();
  const [timeLeft, setTimeLeft] = useState(7 * 24 * 60 * 60 + 6 * 60); // 7 dias e 6 minutos baseado na imagem

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const days = Math.floor(seconds / (24 * 60 * 60));
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
    const mins = Math.floor((seconds % (60 * 60)) / 60);

    if (days > 0) {
      return `${days} dia${days > 1 ? "s" : ""} e ${hours
        .toString()
        .padStart(2, "0")}:${mins.toString().padStart(2, "0")}`;
    }
    return `${hours.toString().padStart(2, "0")}:${mins
      .toString()
      .padStart(2, "0")}`;
  };

  const boletoCode = "34191.79001 01043.510047 91020.150008 1 84750000015000";
  const totalValue = "R$ 150,00";

  const copyBoletoCode = async () => {
    try {
      // Implementação nativa para copiar texto
      // Em um ambiente real, você usaria Clipboard do React Native
      // Para este exemplo, vamos simular a funcionalidade

      // Simular a cópia do código
      const textToCopy = boletoCode;

      // Mock da funcionalidade de copiar - em produção usar:
      // import { Clipboard } from 'react-native';
      // await Clipboard.setString(textToCopy);

      Alert.alert(
        "Código copiado!",
        "O código do boleto foi copiado para a área de transferência."
      );
    } catch (error) {
      Alert.alert("Erro", "Não foi possível copiar o código. Tente novamente.");
    }
  };

  const visualizePDF = () => {
    // Implementação mock para visualizar PDF
    Alert.alert("PDF do boleto", "O PDF do boleto será aberto em breve.");
  };

  const handleFinishPayment = () => {
    router.push("/(events)/payment-success?paymentType=boleto");
  };

  return (
    <ScreenWithHeader screenTitle="Código de pagamento via Boleto" backButton>
      <ScrollView
        style={{flex: 1, paddingHorizontal: 24}}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>Efetuar pagamento</Text>
          <Text style={styles.subtitle}>
            Confira as instruções e efetue o pagamento.
          </Text>
        </View>

        {/* Valor total */}
        <View style={styles.valueContainer}>
          <Text style={styles.valueLabel}>Valor total</Text>
          <Text style={styles.valueAmount}>{totalValue}</Text>
        </View>

        {/* Código do Boleto */}
        <View style={styles.codeContainer}>
          <Text style={styles.codeLabel}>
            Digite / copie o código manualmente:
          </Text>

          <View style={styles.codeDisplay}>
            <Text style={styles.codeText}>{boletoCode}</Text>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.copyButton}
              onPress={copyBoletoCode}
            >
              <Text style={styles.icon}>📋</Text>
              <Text style={styles.copyButtonText}>Copiar código</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.pdfButton} onPress={visualizePDF}>
              <Text style={styles.icon}>👁️</Text>
              <Text style={styles.pdfButtonText}>Visualizar PDF do boleto</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Instruções de pagamento */}
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>Instruções de pagamento</Text>

          <Text style={styles.instructionStep}>
            <Text style={styles.instructionStepBold}>1° passo:</Text> Digite ou
            copie o código que foi gerado no campo acima.
          </Text>

          <Text style={styles.instructionStep}>
            <Text style={styles.instructionStepBold}>2° passo:</Text> Acesse sua
            plataforma de pagamento / internet banking.
          </Text>

          <Text style={styles.instructionStep}>
            <Text style={styles.instructionStepBold}>3° passo:</Text> Cole o
            código, confirme o valor e efetue o pagamento.
          </Text>

          <Text style={styles.instructionNote}>
            Pronto! Em alguns minutos o status da sua dívida será atualizado em
            nosso sistema.
          </Text>
        </View>

        {/* Timer */}
        <View style={styles.timerContainer}>
          <Text style={styles.timerText}>
            Você tem até o dia <Text style={styles.timerHighlight}>07/06</Text>{" "}
            para efetuar o pagamento do boleto antes do vencimento.
          </Text>
        </View>

        <FullSizeButton
          text="Finalizar pagamento"
          onPress={handleFinishPayment}
        />
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default BoletoPayment;
