import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 32
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: stylesConstants.colors.brand.primary,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16
  },
  successIconText: {
    fontSize: 32,
    color: stylesConstants.colors.fullWhite
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    textAlign: "center",
    marginBottom: 8
  },
  subtitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    textAlign: "center"
  },
  sectionContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    marginBottom: 12
  },
  productItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12
  },
  productIcon: {
    width: 40,
    height: 40,
    backgroundColor: stylesConstants.colors.mainBackground,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  productIconText: {
    fontSize: 16
  },
  productInfo: {
    flex: 1
  },
  productName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    marginBottom: 2
  },
  productDetails: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400
  },
  productValue: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600
  },
  paymentMethodContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    backgroundColor: stylesConstants.colors.mainBackground,
    borderRadius: 8
  },
  paymentMethodIcon: {
    width: 32,
    height: 32,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 6,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  paymentMethodIconText: {
    fontSize: 14,
    color: stylesConstants.colors.fullWhite
  },
  paymentMethodInfo: {
    flex: 1
  },
  paymentMethodName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    marginBottom: 2
  },
  paymentMethodDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8
  },
  summaryLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400
  },
  summaryValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    paddingTop: 12,
    marginTop: 12
  },
  totalLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600
  },
  totalValue: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 700
  },
  transactionInfo: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16
  },
  transactionRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8
  },
  transactionLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400
  },
  transactionValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600
  },
  actionButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16
  },
  actionButton: {
    flex: 1,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 4,
    alignItems: "center"
  },
  actionButtonText: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    marginTop: 4
  },
  actionButtonIcon: {
    fontSize: 20
  },
  buttonContainer: {
    marginTop: 16
  }
});

export default styles;
