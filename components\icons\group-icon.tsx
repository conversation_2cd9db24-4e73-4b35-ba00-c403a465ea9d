import React from "react";
import Svg, {SvgProps, Path} from "react-native-svg";

const GroupIcon: React.FC<SvgProps> = (props) => {
    const width = props.width ?? 20;
    const height = props.height ?? 20;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill="none"
            {...props}
        >
            <Path
                stroke={props.stroke ?? "#fff"}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M14.216 12.935a2.5 2.5 0 0 1 3.901 2.266c-1 .35-2.061.485-3.117.399a4.977 4.977 0 0 0-.784-2.665Zm0 0a5 5 0 0 0-8.431 0m9.214 2.664v.026c0 .187-.01.372-.03.555A9.95 9.95 0 0 1 10 17.5a9.952 9.952 0 0 1-4.97-1.32A5 5 0 0 1 5 15.6m0 0a7.498 7.498 0 0 1-3.116-.398 2.5 2.5 0 0 1 3.901-2.267M5 15.599a4.977 4.977 0 0 1 .785-2.664m6.715-7.31a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Zm5 2.5a1.875 1.875 0 1 1-3.751 0 1.875 1.875 0 0 1 3.75 0Zm-11.25 0a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Z"
            />
        </Svg>
    );
};

export default GroupIcon;
