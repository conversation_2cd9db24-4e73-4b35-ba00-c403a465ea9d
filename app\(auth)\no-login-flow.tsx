import React from "react";
import {Text, View} from "react-native";
import Screen from "@/components/screen";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import {useTranslation} from "react-i18next";
import FullSizeButton from "@/components/full-size-button";

const NoLoginFlow: React.FC = () => {
  const {t} = useTranslation();

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            padding: 20
          }}
        >
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#fff",
              textAlign: "center",
              marginBottom: 20
            }}
          >
            {t("noLoginFlow.title", "Fluxo Sem Login")}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "#fff",
              textAlign: "center",
              marginBottom: 40
            }}
          >
            {t("noLoginFlow.description", "Continue navegando sem fazer login")}
          </Text>
          <FullSizeButton
            text={t("noLoginFlow.continue", "Continuar")}
            onPress={() => {}}
          />
        </View>
      </Screen>
    </>
  );
};

export default NoLoginFlow;
