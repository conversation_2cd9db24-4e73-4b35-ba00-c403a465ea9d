import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        display: "flex",
        gap: 12
    },
    title: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        color: stylesConstants.colors.textPrimary,
        lineHeight: 18
    },
    paymentGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8
    },
    installmentLabel: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        color: stylesConstants.colors.textPrimary,
        lineHeight: 18,
        textDecorationLine: "underline"
    },
    installmentValue: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        color: stylesConstants.colors.textPrimary,
        lineHeight: 18
    }
});

export default styles;
