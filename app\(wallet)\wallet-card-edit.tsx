import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity, Alert} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";

const WalletCardEdit: React.FC = () => {
  const {t} = useTranslation();
  const [formData, setFormData] = useState({
    cardNumber: "**** **** **** 1234",
    cardName: "João Silva",
    expiryDate: "12/26",
    isDefault: true
  });
  const [isEditing, setIsEditing] = useState(false);

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
    };

  const handleSave = () => {
    console.log("Card updated:", formData);
    setIsEditing(false);
    // Handle card update
  };

  const handleDelete = () => {
    Alert.alert(
      t("walletCardEdit.deleteTitle", "Remover Cartão"),
      t(
        "walletCardEdit.deleteMessage",
        "Tem certeza que deseja remover este cartão? Esta ação não pode ser desfeita."
      ),
      [
        {
          text: t("walletCardEdit.cancel", "Cancelar"),
          style: "cancel"
        },
        {
          text: t("walletCardEdit.delete", "Remover"),
          style: "destructive",
          onPress: () => {
            console.log("Card deleted");
            // Handle card deletion
          }
        }
      ]
    );
  };

  return (
    <ScreenWithHeader
      screenTitle={t("walletCardEdit.title", "Editar Cartão")}
      backButton
    >
      <ScrollView style={{flex: 1, padding: 20}}>
        {/* Card Preview */}
        <View
          style={{
            backgroundColor: "#1A1F71",
            borderRadius: 12,
            padding: 20,
            marginBottom: 30,
            minHeight: 120
          }}
        >
          {formData.isDefault && (
            <View
              style={{
                position: "absolute",
                top: 10,
                right: 10,
                backgroundColor: "rgba(255,255,255,0.2)",
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 10
              }}
            >
              <Text style={{color: "#fff", fontSize: 10, fontWeight: "bold"}}>
                {t("walletCardEdit.default", "PADRÃO")}
              </Text>
            </View>
          )}

          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "flex-start",
              marginBottom: 20
            }}
          >
            <Text style={{fontSize: 24}}>💳</Text>
            <Text style={{color: "#fff", fontSize: 14, fontWeight: "bold"}}>
              VISA
            </Text>
          </View>

          <Text
            style={{
              color: "#fff",
              fontSize: 18,
              fontWeight: "bold",
              marginBottom: 10,
              letterSpacing: 2
            }}
          >
            {formData.cardNumber}
          </Text>

          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center"
            }}
          >
            <Text style={{color: "rgba(255,255,255,0.8)", fontSize: 14}}>
              {formData.cardName}
            </Text>
            <Text style={{color: "rgba(255,255,255,0.8)", fontSize: 14}}>
              {formData.expiryDate}
            </Text>
          </View>
        </View>

        {/* Edit Toggle */}
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 20
          }}
        >
          <Text style={{color: "#fff", fontSize: 18, fontWeight: "bold"}}>
            {t("walletCardEdit.cardDetails", "Detalhes do Cartão")}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: isEditing ? "#34C759" : "#007AFF",
              paddingHorizontal: 15,
              paddingVertical: 8,
              borderRadius: 20
            }}
            onPress={() => setIsEditing(!isEditing)}
          >
            <Text style={{color: "#fff", fontSize: 14, fontWeight: "bold"}}>
              {isEditing
                ? t("walletCardEdit.done", "Concluído")
                : t("walletCardEdit.edit", "Editar")}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Form */}
        <View style={{gap: 15, marginBottom: 30}}>
          <View>
            <Text style={{color: "#fff", fontSize: 14, marginBottom: 8}}>
              {t("walletCardEdit.cardNumber", "Número do Cartão")}
            </Text>
            <View
              style={{
                backgroundColor: "rgba(255,255,255,0.1)",
                padding: 15,
                borderRadius: 8
              }}
            >
              <Text style={{color: "#ccc", fontSize: 16}}>
                {formData.cardNumber}
              </Text>
            </View>
            <Text style={{color: "#ccc", fontSize: 12, marginTop: 5}}>
              {t(
                "walletCardEdit.cardNumberNote",
                "Por segurança, o número do cartão não pode ser alterado"
              )}
            </Text>
          </View>

          {isEditing ? (
            <InputField
              label={t("walletCardEdit.cardName", "Nome no Cartão")}
              value={formData.cardName}
              onChangeText={handleInputChange("cardName")}
              placeholder={t(
                "walletCardEdit.cardNamePlaceholder",
                "Nome como está no cartão"
              )}
            />
          ) : (
            <View>
              <Text style={{color: "#fff", fontSize: 14, marginBottom: 8}}>
                {t("walletCardEdit.cardName", "Nome no Cartão")}
              </Text>
              <View
                style={{
                  backgroundColor: "rgba(255,255,255,0.1)",
                  padding: 15,
                  borderRadius: 8
                }}
              >
                <Text style={{color: "#ccc", fontSize: 16}}>
                  {formData.cardName}
                </Text>
              </View>
            </View>
          )}

          {isEditing ? (
            <InputField
              label={t("walletCardEdit.expiryDate", "Data de Validade")}
              value={formData.expiryDate}
              onChangeText={handleInputChange("expiryDate")}
              placeholder="MM/AA"
            />
          ) : (
            <View>
              <Text style={{color: "#fff", fontSize: 14, marginBottom: 8}}>
                {t("walletCardEdit.expiryDate", "Data de Validade")}
              </Text>
              <View
                style={{
                  backgroundColor: "rgba(255,255,255,0.1)",
                  padding: 15,
                  borderRadius: 8
                }}
              >
                <Text style={{color: "#ccc", fontSize: 16}}>
                  {formData.expiryDate}
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Default Card Option */}
        <TouchableOpacity
          style={{
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: "rgba(255,255,255,0.1)",
            padding: 15,
            borderRadius: 8,
            marginBottom: 20,
            opacity: isEditing ? 1 : 0.6
          }}
          onPress={() =>
            isEditing &&
            setFormData((prev) => ({...prev, isDefault: !prev.isDefault}))
          }
          disabled={!isEditing}
        >
          <View
            style={{
              width: 20,
              height: 20,
              borderRadius: 10,
              borderWidth: 2,
              borderColor: formData.isDefault ? "#007AFF" : "#ccc",
              backgroundColor: formData.isDefault ? "#007AFF" : "transparent",
              marginRight: 15
            }}
          />
          <View style={{flex: 1}}>
            <Text style={{color: "#fff", fontSize: 16, fontWeight: "bold"}}>
              {t("walletCardEdit.setDefault", "Cartão padrão")}
            </Text>
            <Text style={{color: "#ccc", fontSize: 14}}>
              {t(
                "walletCardEdit.setDefaultDesc",
                "Usar este cartão por padrão nos pagamentos"
              )}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Usage Statistics */}
        <View
          style={{
            backgroundColor: "rgba(255,255,255,0.05)",
            padding: 15,
            borderRadius: 8,
            marginBottom: 20
          }}
        >
          <Text
            style={{
              color: "#fff",
              fontSize: 16,
              fontWeight: "bold",
              marginBottom: 10
            }}
          >
            {t("walletCardEdit.usage", "Uso do Cartão")}
          </Text>
          <View style={{gap: 8}}>
            <View
              style={{flexDirection: "row", justifyContent: "space-between"}}
            >
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t("walletCardEdit.totalTransactions", "Total de transações")}:
              </Text>
              <Text style={{color: "#fff", fontSize: 14}}>12</Text>
            </View>
            <View
              style={{flexDirection: "row", justifyContent: "space-between"}}
            >
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t("walletCardEdit.totalSpent", "Total gasto")}:
              </Text>
              <Text style={{color: "#fff", fontSize: 14}}>R$ 1.249,80</Text>
            </View>
            <View
              style={{flexDirection: "row", justifyContent: "space-between"}}
            >
              <Text style={{color: "#ccc", fontSize: 14}}>
                {t("walletCardEdit.lastUsed", "Último uso")}:
              </Text>
              <Text style={{color: "#fff", fontSize: 14}}>
                15 de dezembro, 2024
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={{gap: 10}}>
          {isEditing && (
            <FullSizeButton
              text={t("walletCardEdit.saveChanges", "Salvar Alterações")}
              onPress={handleSave}
            />
          )}

          <TouchableOpacity
            style={{
              backgroundColor: "rgba(244,67,54,0.2)",
              borderWidth: 1,
              borderColor: "#F44336",
              padding: 15,
              borderRadius: 8,
              alignItems: "center"
            }}
            onPress={handleDelete}
          >
            <Text style={{color: "#F44336", fontSize: 16, fontWeight: "bold"}}>
              {t("walletCardEdit.removeCard", "Remover Cartão")}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default WalletCardEdit;
