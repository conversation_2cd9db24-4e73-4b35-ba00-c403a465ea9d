import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    tabBar: {
        backgroundColor: stylesConstants.colors.mainBackground,
        height: 75,
        borderColor: stylesConstants.colors.gray800,
        borderTopWidth: 1,
        borderLeftWidth: 1,
        borderRightWidth: 1,
        borderStyle: "solid",
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
    },
    topBarIcon: {
        marginTop: 12
    },
    topBarLabel: {
        color: stylesConstants.colors.gray300,
        textAlign: "center",
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 20,
        marginTop: 4,
    },
    tabBarLabelActive: {
        color: stylesConstants.colors.fullWhite,
        fontWeight: 700
    },
    contentStyle: {
        backgroundColor: stylesConstants.colors.mainBackground,
    }
});

export default styles;