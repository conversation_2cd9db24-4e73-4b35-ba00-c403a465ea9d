import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: stylesConstants.colors.mainBackground,
    },
    contentContainer: {
        paddingHorizontal: 24,
        paddingVertical: 16,
    },
    headerContainer: {
        marginBottom: 24,
    },
    title: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 24,
        fontWeight: 700,
        lineHeight: 32,
        marginBottom: 8,
    },
    formContainer: {
        gap: 24,
    },
    inputGroup: {
        gap: 8,
    },
    label: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 14,
        fontWeight: 600,
        lineHeight: 20,
    },
    textArea: {
        minHeight: 120,
        textAlignVertical: "top",
        paddingTop: 12,
    },
    characterCount: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18,
        textAlign: "right",
        marginTop: 4,
    },
    skillsContainer: {
        gap: 12,
    },
    skillsInputContainer: {
        flexDirection: "row",
        gap: 8,
        alignItems: "flex-end",
    },
    skillInput: {
        flex: 1,
    },
    addSkillButton: {
        backgroundColor: stylesConstants.colors.brand.primary,
        borderRadius: 6,
        paddingHorizontal: 12,
        paddingVertical: 8,
        height: 40,
        justifyContent: "center",
    },
    addSkillButtonText: {
        color: stylesConstants.colors.fullWhite,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 600,
        lineHeight: 18,
    },
    skillsList: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8,
        marginTop: 8,
    },
    skillTag: {
        backgroundColor: stylesConstants.colors.brand.brand25,
        borderRadius: 16,
        paddingHorizontal: 12,
        paddingVertical: 6,
        flexDirection: "row",
        alignItems: "center",
        gap: 6,
    },
    skillText: {
        color: stylesConstants.colors.brand.primary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 600,
        lineHeight: 18,
    },
    removeSkillButton: {
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: stylesConstants.colors.brand.primary,
        alignItems: "center",
        justifyContent: "center",
    },
    removeSkillText: {
        color: stylesConstants.colors.fullWhite,
        fontSize: 10,
        fontWeight: 700,
        lineHeight: 12,
    },
    interestsContainer: {
        gap: 12,
    },
    interestsList: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8,
    },
    interestChip: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        borderRadius: 16,
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderWidth: 1,
        borderColor: stylesConstants.colors.borderDefault,
    },
    interestChipActive: {
        backgroundColor: stylesConstants.colors.brand.primary,
        borderColor: stylesConstants.colors.brand.primary,
    },
    interestChipText: {
        color: stylesConstants.colors.textPrimary,
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 12,
        fontWeight: 400,
        lineHeight: 18,
    },
    interestChipTextActive: {
        color: stylesConstants.colors.fullWhite,
        fontWeight: 600,
    },
    buttonContainer: {
        marginTop: 32,
        gap: 16,
    },
});

export default styles;
