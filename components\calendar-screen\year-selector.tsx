import React, {Fragment, useCallback, useMemo, useState} from "react";
import {Text, TouchableOpacity, View} from "react-native";
import styles from "../../styles/components/schedule/year-selector.style";
import {chunkArray} from "../../utils/array";

export interface YearSelectorProps {
  initialYear: number;
  onChangeYear: (year: number) => void;
}

const YearSelector: React.FC<YearSelectorProps> = (props) => {
  const [currentYear, setCurrentYear] = useState<number>(props.initialYear);

  const years = useMemo(() => {
    const currentYear = new Date().getFullYear();
    return chunkArray(
      Array(12)
        .fill(0)
        .map((_, index) => currentYear + index),
      4
    );
  }, []);

  const handleOnPressYear = useCallback(
    (newYear: number) => () => {
      props.onChangeYear(newYear);
      setCurrentYear(newYear);
    },
    [props.initialYear, props.onChangeYear]
  );

  return (
    <View style={styles.grid}>
      {years.map((row) => (
        <Fragment key={`row-${row[0]}`}>
          <View style={styles.row}>
            {row.map((year) => (
              <TouchableOpacity
                key={year}
                onPress={handleOnPressYear(year)}
                style={[styles.year, currentYear == year && styles.active]}
              >
                <Text
                  style={[
                    styles.yearText,
                    currentYear == year && styles.active
                  ]}
                >
                  {year}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Fragment>
      ))}
    </View>
  );
};

export default YearSelector;
