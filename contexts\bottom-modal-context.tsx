import React, {createContext, useCallback, useContext, useMemo, useState} from "react";

export interface BottomModalProps extends React.PropsWithChildren {
    title: string;
}

interface BottomModalContextElements {
    currentModal?: BottomModalProps | null;
    setCurrentModal: React.Dispatch<React.SetStateAction<BottomModalProps | null>>
}

const BottomModalContext = createContext<Partial<BottomModalContextElements>>({});

const BottomModalProvider: React.FC<React.PropsWithChildren> = (props) => {
    const [currentModal, setCurrentModal] = useState<BottomModalProps | null>(null);
    const providerValue = useMemo(() => ({currentModal, setCurrentModal}),
        [currentModal]);

    return (
        <BottomModalContext.Provider value={providerValue}>
            {props.children}
        </BottomModalContext.Provider>
    );
};

export function useBottomModal() {
    const {currentModal, setCurrentModal} = useContext(BottomModalContext);

    const openModal = useCallback((props: BottomModalProps) => {
        setCurrentModal?.(props);
    }, [setCurrentModal]);

    const closeModal = useCallback(() => {
        setCurrentModal?.(null);
    }, [setCurrentModal]);

    return {
        currentModal,
        openModal,
        closeModal
    };
}

export default BottomModalProvider;