import React from "react";
import { render } from "@testing-library/react-native";

jest.mock("react-native-svg", () => {
  const Svg = ({ children, ...rest }: any) => <svg {...rest}>{children}</svg>;
  const Path = (props: any) => <path {...props} />;
  return {
    __esModule: true,
    default: Svg,
    Svg,
    Path
  };
});

import UserIcon from "../../../components/icons/user-icon";

describe("UserIcon", () => {
  it("renders without crash", () => {
    const { toJSON } = render(<UserIcon />);
    expect(toJSON()).not.toBeUndefined();
  });

  it("allows custom dimensions", () => {
    const { getByTestId } = render(<UserIcon width={26} height={26} testID="icon" />);
    const icon = getByTestId("icon");
    expect(icon.props.width).toBe(26);
    expect(icon.props.height).toBe(26);
  });
});
