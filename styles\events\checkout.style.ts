import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    marginBottom: 8
  },
  sectionContainer: {
    marginBottom: 24
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 16
  },
  orderSummaryContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  orderItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12
  },
  orderItemName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    flex: 1
  },
  orderItemPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    paddingTop: 12,
    marginTop: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  totalLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24
  },
  totalValue: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 700,
    lineHeight: 26
  },
  paymentMethodContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  paymentMethodItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.borderDefault
  },
  paymentMethodIcon: {
    width: 24,
    height: 24,
    marginRight: 12
  },
  paymentMethodText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    flex: 1
  },
  buttonContainer: {
    marginTop: 32,
    gap: 16
  },
  // Novos estilos para a implementação pixel-perfect
  productCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12
  },
  productIcon: {
    width: 48,
    height: 48,
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  productInfo: {
    flex: 1,
    marginRight: 12
  },
  productName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 22,
    marginBottom: 4
  },
  productPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 700,
    lineHeight: 24
  },
  removeButton: {
    marginRight: 12
  },
  removeButtonText: {
    color: "#FF6B6B",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 500
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center"
  },
  quantityButton: {
    width: 36,
    height: 36,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center"
  },
  quantityButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: 600,
    lineHeight: 20
  },
  quantityText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 600,
    marginHorizontal: 16,
    minWidth: 24,
    textAlign: "center"
  },
  // Estilos para métodos de pagamento
  paymentMethodCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "transparent"
  },
  paymentMethodCardSelected: {
    borderColor: stylesConstants.colors.brand.primary
  },
  paymentMethodInfo: {
    flex: 1,
    marginLeft: 12
  },
  paymentMethodName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    marginBottom: 2
  },
  paymentMethodDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: stylesConstants.colors.borderDefault,
    justifyContent: "center",
    alignItems: "center"
  },
  radioButtonSelected: {
    borderColor: stylesConstants.colors.brand.primary
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: stylesConstants.colors.brand.primary
  },
  // Estilos para carrinho vazio
  emptyCartContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 32,
    alignItems: "center",
    marginBottom: 24
  },
  emptyCartText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 8,
    textAlign: "center"
  },
  emptyCartSubtext: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    textAlign: "center"
  }
});

export default styles;
