import React from "react";
import { render, fireEvent } from "@testing-library/react-native";

jest.mock("react-native-reanimated", () => require("react-native-reanimated/mock"));

jest.mock("../../../styles/components/login/select-button.style", () => ({ container: {} }));

jest.mock("../../../components/login/option-button", () => {
  const { Text, TouchableOpacity } = require("react-native");
  return ({ option, onPress }: any) => (
    <TouchableOpacity onPress={onPress} testID={`opt-${option.id}`}>
      <Text>{option.label}</Text>
    </TouchableOpacity>
  );
});

import SelectButton from "../../../components/login/select-button";

describe("SelectButton", () => {
  const options = [
    { id: 1, label: "Login" },
    { id: 2, label: "Register" }
  ];

  it("renders labels", () => {
    const { getByText } = render(<SelectButton options={options} activeOption={1} />);
    expect(getByText("Login")).toBeTruthy();
    expect(getByText("Register")).toBeTruthy();
  });

  it("invokes callback on press", () => {
    const handler = jest.fn();
    const { getByTestId } = render(<SelectButton options={options} activeOption={1} activeOptionChange={handler} />);
    fireEvent.press(getByTestId("opt-2"));
    expect(handler).toHaveBeenCalledWith(2);
  });
});
