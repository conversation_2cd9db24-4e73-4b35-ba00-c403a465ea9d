import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface NewChevronRightIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const NewChevronRightIcon: React.FC<NewChevronRightIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FCFCFD",
    [props.replaceColor]
  );

  return (
    <Svg width={8} height={14} viewBox="0 0 8 14" fill="none" {...props}>
      <Path
        d="M1 13L7 7L1 1"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default NewChevronRightIcon;
