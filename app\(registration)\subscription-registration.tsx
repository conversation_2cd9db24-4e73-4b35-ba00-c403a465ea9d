import React from "react";
import {Text, View} from "react-native";
import Screen from "../../components/screen";
import BackgroundLogoTexture from "../../components/logos/background-logo-texture";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";

const SubscriptionRegistration: React.FC = () => {
    const {t} = useTranslation();

    return (
        <>
            <BackgroundLogoTexture />
            <Screen>
                <View style={{flex: 1, padding: 20}}>
                    <BackButton />
                    <View style={{flex: 1, justifyContent: 'center'}}>
                        <Text style={{fontSize: 24, fontWeight: 'bold', color: '#fff', textAlign: 'center', marginBottom: 20}}>
                            {t("subscriptionRegistration.title", "Cadastro de Assinatura")}
                        </Text>
                        <Text style={{fontSize: 16, color: '#fff', textAlign: 'center', marginBottom: 40}}>
                            {t("subscriptionRegistration.description", "Escolha seu plano de assinatura")}
                        </Text>
                    </View>
                    <FullSizeButton
                        text={t("subscriptionRegistration.next", "Próximo")}
                        onPress={() => {}}
                    />
                </View>
            </Screen>
        </>
    );
};

export default SubscriptionRegistration;
