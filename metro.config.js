const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add path aliases
config.resolver.alias = {
  '@': path.resolve(__dirname, './'),
  '@/app': path.resolve(__dirname, './app'),
  '@/components': path.resolve(__dirname, './components'),
  '@/contexts': path.resolve(__dirname, './contexts'),
  '@/hooks': path.resolve(__dirname, './hooks'),
  '@/services': path.resolve(__dirname, './services'),
  '@/models': path.resolve(__dirname, './models'),
  '@/styles': path.resolve(__dirname, './styles'),
  '@/utils': path.resolve(__dirname, './utils'),
  '@/assets': path.resolve(__dirname, './assets'),
  '@/locales': path.resolve(__dirname, './locales'),
  '@/tests': path.resolve(__dirname, './tests'),
};

module.exports = config;
