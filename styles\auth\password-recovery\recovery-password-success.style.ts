import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingVertical: 40,
        paddingHorizontal: 20,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        marginTop: -52
    },
    icon: {
        display: "flex",
        width: 56,
        height: 56,
        padding: 14,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: "50%",
        borderStyle: "solid",
        borderWidth: 10,
        borderColor: stylesConstants.colors.success50,
        backgroundColor: stylesConstants.colors.success100,
        marginBottom: 32
    },
    text: {
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontStyle: "normal",
        fontWeight: 400,
        lineHeight: 24,
        color: stylesConstants.colors.fullWhite,
        textAlign: "center",
    },
    title: {
        fontSize: 20,
        fontStyle: "normal",
        fontWeight: 700,
        lineHeight: 30,
        marginBottom: 8
    },
    footerButton: {
        position: "absolute",
        bottom: 40,
        width: "100%",
        paddingHorizontal: 24
    }
});

export default styles;
