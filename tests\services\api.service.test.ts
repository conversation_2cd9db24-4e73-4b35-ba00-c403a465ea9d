import {firstValueFrom} from "rxjs";

let mockRequest: jest.Mock;

jest.mock("axios", () => {
  mockRequest = jest.fn();
  return {
    __esModule: true,
    default: {
      create: jest.fn(() => ({
        request: mockRequest,
        interceptors: {
          request: {
            use: jest.fn()
          },
          response: {
            use: jest.fn()
          }
        }
      }))
    },
    AxiosError: jest.requireActual("axios").AxiosError
  };
});

import ApiService from "../../services/api.service";

describe("ApiService.request", () => {
  it("returns data on success", async () => {
    mockRequest.mockResolvedValue({data: {ok: true}});
    const result = await firstValueFrom(ApiService.request("GET", "/test"));
    expect(result).toEqual({ok: true});
    expect(mockRequest).toHaveBeenCalledWith({
      url: "/test",
      baseURL: undefined,
      method: "GET",
      data: null,
      params: null
    });
  });

  it("throws status on AxiosError", async () => {
    const err = new (jest.requireActual("axios").AxiosError)("fail");
    err.response = {status: 404, data: {message: "no"}};
    mockRequest.mockRejectedValue(err);
    await expect(
      firstValueFrom(ApiService.request("GET", "/bad"))
    ).rejects.toBe(404);
  });
});
