import React, {useCallback} from "react";
import Screen from "./screen";
import {StyleProp, Text, TouchableOpacity, View, ViewStyle} from "react-native";
import styles from "../styles/components/screen-with-header.style";
import ChevronLeftIcon from "./icons/chevron-left-icon";
import {useRouter} from "expo-router";

export interface ScreenWithHeaderProps extends React.PropsWithChildren {
  screenTitle: string;
  backButton?: boolean;
  disablePadding?: boolean;
  rightHeaderChild?: React.ReactNode;
  disableScrollView?: boolean;
}

type ScreenWithHeaderType = React.FC<ScreenWithHeaderProps> & {
  InternalPadding: React.FC<
    React.PropsWithChildren & {style?: StyleProp<ViewStyle>}
  >;
};

const ScreenWithHeader: ScreenWithHeaderType = (props) => {
  const router = useRouter();

  const handleBackButtonPress = useCallback(() => router.back(), [router]);

  return (
    <Screen disableScrollView={props.disableScrollView}>
      <View
        style={[styles.mainContainer, props.disablePadding && styles.noPadding]}
      >
        <View
          style={[styles.navbar, props.disablePadding && styles.mainPadding]}
        >
          {props.backButton ? (
            <TouchableOpacity
              onPress={handleBackButtonPress}
              style={{zIndex: 2}}
            >
              <ChevronLeftIcon width={24} height={24} />
            </TouchableOpacity>
          ) : (
            <View style={styles.spacer} />
          )}

          <Text style={styles.title}>{props.screenTitle}</Text>

          {props.rightHeaderChild ?? <View style={styles.spacer} />}
        </View>
        {props.children}
      </View>
    </Screen>
  );
};

ScreenWithHeader.InternalPadding = (props) => {
  return (
    <View style={[styles.mainPadding, props.style]}>{props.children}</View>
  );
};

export default ScreenWithHeader;
