import React from "react";
import {render, fireEvent} from "@testing-library/react-native";

// Mock the required modules
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, fallback?: string) => fallback ?? key
  })
}));

jest.mock("expo-router", () => ({
  useRouter: () => ({
    back: jest.fn()
  })
}));

jest.mock("react-native-pager-view", () => {
  const React = require("react");
  const {View} = require("react-native");

  return React.forwardRef((props: any, _ref: any) => {
    return (
      <View testID="pager-view" {...props}>
        {props.children}
      </View>
    );
  });
});

jest.mock("react-native-svg", () => {
  const {View} = require("react-native");

  const Svg = ({children, ...props}: any) => <View {...props}>{children}</View>;
  const Path = (props: any) => <View {...props} />;

  return {
    __esModule: true,
    default: Svg,
    Svg,
    Path
  };
});

jest.mock("expo-image", () => ({
  Image: ({source, style, ...props}: any) => {
    const {View} = require("react-native");
    return <View style={style} {...props} testID="expo-image" />;
  }
}));

import Wallet from "../../app/(wallet)/wallet";

describe("Wallet Screen", () => {
  it("renders without crashing", () => {
    const {toJSON} = render(<Wallet />);
    expect(toJSON()).not.toBeUndefined();
  });

  it("displays the correct screen title", () => {
    const {getByText} = render(<Wallet />);
    expect(getByText("Sua carteira")).toBeTruthy();
  });

  it("displays add card button in header", () => {
    const {getByText} = render(<Wallet />);
    expect(getByText("Ad. cartão")).toBeTruthy();
  });

  it("displays member cards carousel", () => {
    const {getByTestId} = render(<Wallet />);
    expect(getByTestId("pager-view")).toBeTruthy();
  });

  it("displays member card information", () => {
    const {getByText} = render(<Wallet />);
    expect(getByText("Maria Aparecida dos Santos")).toBeTruthy();
    expect(getByText("Membro VIP")).toBeTruthy();
    expect(getByText("1234")).toBeTruthy();
  });

  it("displays card information section", () => {
    const {getByText} = render(<Wallet />);
    expect(getByText("Informações do cartão")).toBeTruthy();
    expect(getByText("Nome do associado")).toBeTruthy();
    expect(getByText("Unidade Federativa")).toBeTruthy();
    expect(getByText("Unidade de Associação")).toBeTruthy();
    expect(getByText("Membro ativo desde")).toBeTruthy();
  });

  it("toggles card information visibility", () => {
    const {getByText, getAllByText} = render(<Wallet />);

    // Initially, information should be hidden
    expect(getByText("Maria Aparecida dos Santos")).toBeTruthy(); // This is in the card
    expect(getAllByText("••••••••••••••••••••••").length).toBeGreaterThan(0); // Hidden info

    // Click the show/hide button
    const toggleButton = getByText("Mostrar");
    fireEvent.press(toggleButton);

    // Information should now be visible
    expect(getByText("Esconder")).toBeTruthy();
  });

  it("displays action buttons", () => {
    const {getByText} = render(<Wallet />);
    expect(getByText("Editar cartão")).toBeTruthy();
    expect(getByText("Excluir cartão")).toBeTruthy();
  });

  it("displays pagination dots", () => {
    const {getByTestId} = render(<Wallet />);
    // Should have pagination dots for the pager view
    const pagerView = getByTestId("pager-view");
    expect(pagerView).toBeTruthy();
  });
});
