{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-64:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2e07b8f892dff7e7220c58c45859f4c7\\transformed\\appcompat-1.7.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,14858", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,14936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\463a0a6d18bcedcdf0dcc1e5161e05be\\transformed\\core-1.13.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "40,41,42,43,44,45,46,166", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3644,3740,3842,3941,4040,4144,4247,15252", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3735,3837,3936,4035,4139,4242,4358,15348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59ca874c8ef89227cc0c3a48f60e855c\\transformed\\biometric-1.2.0-alpha04\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,199,313,419,509,643,747,858,977,1106,1244,1371,1489,1620,1720,1868,1986,2112,2251,2371,2491,2584,2707,2789,2901,2997,3123", "endColumns": "143,113,105,89,133,103,110,118,128,137,126,117,130,99,147,117,125,138,119,119,92,122,81,111,95,125,95", "endOffsets": "194,308,414,504,638,742,853,972,1101,1239,1366,1484,1615,1715,1863,1981,2107,2246,2366,2486,2579,2702,2784,2896,2992,3118,3214"}, "to": {"startLines": "33,34,69,71,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,159,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2979,3123,6890,7094,7399,7533,7942,8053,8172,8301,8439,8566,8684,8815,8915,9063,9181,9307,9446,14660,15501,15594,15717,15799,15911,16007,16133", "endColumns": "143,113,105,89,133,103,110,118,128,137,126,117,130,99,147,117,125,138,119,119,92,122,81,111,95,125,95", "endOffsets": "3118,3232,6991,7179,7528,7632,8048,8167,8296,8434,8561,8679,8810,8910,9058,9176,9302,9441,9561,14775,15589,15712,15794,15906,16002,16128,16224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70312fc86ee51e27d343045c10899056\\transformed\\react-android-0.79.3-debug\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,288,364,446,526,604,684,758", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "126,209,283,359,441,521,599,679,753,827"}, "to": {"startLines": "50,94,109,110,157,158,160,165,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4655,9633,10723,10797,14498,14580,14780,15172,15353,15427", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "4726,9711,10792,10868,14575,14655,14853,15247,15422,15496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70a5ec28bf4682b0ab5b2d0ef3085bdc\\transformed\\play-services-basement-18.3.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "133", "endOffsets": "332"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5713", "endColumns": "137", "endOffsets": "5846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88cfc00e8e0a9ed392dc6923ed9897c3\\transformed\\browser-1.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "70,77,78,79", "startColumns": "4,4,4,4", "startOffsets": "6996,7637,7734,7843", "endColumns": "97,96,108,98", "endOffsets": "7089,7729,7838,7937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\559afa6d6ee92008dceb7e133cc79c58\\transformed\\play-services-base-18.0.1\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,446,567,670,817,936,1048,1147,1302,1403,1551,1672,1814,1958,2017,2075", "endColumns": "100,147,120,102,146,118,111,98,154,100,147,120,141,143,58,57,74", "endOffsets": "297,445,566,669,816,935,1047,1146,1301,1402,1550,1671,1813,1957,2016,2074,2149"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4731,4836,4988,5113,5220,5371,5494,5610,5851,6010,6115,6267,6392,6538,6686,6749,6811", "endColumns": "104,151,124,106,150,122,115,102,158,104,151,124,145,147,62,61,78", "endOffsets": "4831,4983,5108,5215,5366,5489,5605,5708,6005,6110,6262,6387,6533,6681,6744,6806,6885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2684515d4d7b0c87770167e126691f99\\transformed\\material-1.12.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1031,1096,1186,1253,1312,1402,1466,1530,1593,1662,1726,1780,1892,1950,2012,2066,2138,2260,2347,2422,2513,2594,2675,2815,2892,2973,3100,3191,3268,3322,3373,3439,3509,3586,3657,3732,3803,3880,3949,4018,4125,4216,4288,4377,4466,4540,4612,4698,4748,4827,4893,4973,5057,5119,5183,5246,5315,5415,5510,5602,5694,5752,5807,5885,5966,6041", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "267,349,427,504,590,674,772,887,966,1026,1091,1181,1248,1307,1397,1461,1525,1588,1657,1721,1775,1887,1945,2007,2061,2133,2255,2342,2417,2508,2589,2670,2810,2887,2968,3095,3186,3263,3317,3368,3434,3504,3581,3652,3727,3798,3875,3944,4013,4120,4211,4283,4372,4461,4535,4607,4693,4743,4822,4888,4968,5052,5114,5178,5241,5310,5410,5505,5597,5689,5747,5802,5880,5961,6036,6111"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,72,73,74,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3237,3319,3397,3474,3560,4363,4461,4576,7184,7244,7309,9566,9716,9775,9865,9929,9993,10056,10125,10189,10243,10355,10413,10475,10529,10601,10873,10960,11035,11126,11207,11288,11428,11505,11586,11713,11804,11881,11935,11986,12052,12122,12199,12270,12345,12416,12493,12562,12631,12738,12829,12901,12990,13079,13153,13225,13311,13361,13440,13506,13586,13670,13732,13796,13859,13928,14028,14123,14215,14307,14365,14420,14941,15022,15097", "endLines": "5,35,36,37,38,39,47,48,49,72,73,74,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,162,163,164", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "317,3314,3392,3469,3555,3639,4456,4571,4650,7239,7304,7394,9628,9770,9860,9924,9988,10051,10120,10184,10238,10350,10408,10470,10524,10596,10718,10955,11030,11121,11202,11283,11423,11500,11581,11708,11799,11876,11930,11981,12047,12117,12194,12265,12340,12411,12488,12557,12626,12733,12824,12896,12985,13074,13148,13220,13306,13356,13435,13501,13581,13665,13727,13791,13854,13923,14023,14118,14210,14302,14360,14415,14493,15017,15092,15167"}}]}]}