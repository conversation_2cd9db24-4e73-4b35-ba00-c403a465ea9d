import React from "react";
import Svg, {SvgProps, Path} from "react-native-svg";

const HandshakeIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={20}
            height={20}
            viewBox="0 0 20 20"
            fill="none"
            {...props}
        >
            <Path
                stroke={props.stroke ?? "#fff"}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="m9.167 13.167 1.666 1.667a1.767 1.767 0 1 0 2.5-2.5"
            />
            <Path
                stroke={props.stroke ?? "#fff"}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="m11.667 10.667 2.083 2.083a1.768 1.768 0 0 0 2.5-2.5l-3.233-3.233a2.5 2.5 0 0 0-3.534 0l-.733.733a1.768 1.768 0 1 1-2.5-2.5L8.592 2.91a4.825 4.825 0 0 1 5.883-.725l.392.233c.354.214.776.289 1.183.208l1.45-.291"
            />
            <Path
                stroke={props.stroke ?? "#fff"}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="m17.5 1.501.833 9.167h-1.666M2.5 1.5l-.833 9.167 5.416 5.416a1.768 1.768 0 0 0 2.5-2.5M2.5 2.334h6.667"
            />
        </Svg>
    );
};

export default HandshakeIcon;
