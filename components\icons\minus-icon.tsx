import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface MinusIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const MinusIcon: React.FC<MinusIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#FCFCFD", [props.replaceColor]);

    return (
        <Svg
            width={20}
            height={20}
            fill="none"
            {...props}
        >
            <Path
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4.167 10h11.666"
            />
        </Svg>
    );
};

export default MinusIcon;
