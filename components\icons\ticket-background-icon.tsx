import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const TicketBackgroundIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={99}
            height={100}
            fill="none"
            {...props}
        >
            <Path
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={3.5}
                d="M25.5 28v-5.25m0 28.875v-5.25m0 28.875V70M.3 7h71.4c5.88 0 8.82 0 11.067 1.144a10.5 10.5 0 0 1 4.589 4.59C88.5 14.978 88.5 17.918 88.5 23.8v6.825c-10.148 0-18.375 8.227-18.375 18.375S78.352 67.375 88.5 67.375V74.2c0 5.88 0 8.82-1.144 11.067a10.5 10.5 0 0 1-4.59 4.589C80.522 91 77.582 91 71.7 91H.3c-5.88 0-8.82 0-11.067-1.144a10.5 10.5 0 0 1-4.589-4.59C-16.5 83.022-16.5 80.082-16.5 74.2v-6.825c10.148 0 18.375-8.227 18.375-18.375S-6.352 30.625-16.5 30.625V23.8c0-5.88 0-8.82 1.144-11.067a10.5 10.5 0 0 1 4.59-4.589C-8.522 7-5.582 7 .3 7Z"
                opacity={0.1}
            />
        </Svg>
    );
};

export default TicketBackgroundIcon;