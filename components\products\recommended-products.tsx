import React, {useCallback} from "react";
import RecommendedProductCard from "./recommended-product-card";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  ColorValue,
  ListRenderItemInfo
} from "react-native";
import styles from "@/styles/components/products/recomended-products.style";
import BookIcon from "@/components/icons/book-icon";
import {Product} from "@/models/product";
import InputField from "@/components/input-field";
import SearchIcon from "@/components/icons/search-icon";
import Partners from "./partners";
import ProductCategories from "./product-categories";
import {useTranslation} from "react-i18next";
import useCatalog from "@/hooks/use-catalog";

export interface RecommendedProductsProps {}

const RecommendedProducts: React.FC<RecommendedProductsProps> = () => {
  const {catalogQuery} = useCatalog();
  const {t} = useTranslation();

  const handleLoadMore = useCallback(async () => {
    if (catalogQuery.hasNextPage && !catalogQuery.isLoading) {
      await catalogQuery.fetchNextPage();
    }
  }, [catalogQuery]);

  const handleRefresh = useCallback(async () => {
    await catalogQuery.refetch();
  }, [catalogQuery]);

  const renderItem = useCallback((item: ListRenderItemInfo<Product>) => {
    const product = item.item;
    return (
      <RecommendedProductCard
        key={product.id}
        productId={product.id}
        title={product.name}
        description={product.description}
        price={product.value}
        icon={<BookIcon />}
      />
    );
  }, []);

  const renderFooter = useCallback(() => {
    if (!catalogQuery.isLoading || !catalogQuery.hasNextPage) return null;

    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color="#ffffff" />
        <Text style={styles.loadingText}>
          {t("recommendedProducts.loadingMore")}
        </Text>
      </View>
    );
  }, [catalogQuery]);

  const renderEmpty = useCallback(() => {
    if (catalogQuery.isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.emptyText}>
            {t("recommendedProducts.loadingProducts")}
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          {t("recommendedProducts.noProducts")}
        </Text>
      </View>
    );
  }, [catalogQuery]);

  const searchIcon = useCallback((_?: ColorValue) => <SearchIcon />, []);

  const renderHeader = useCallback(
    () => (
      <View style={styles.headerContent}>
        <InputField
          placeholder={t("recommendedProducts.searchPlaceholder")}
          value=""
          onChangeText={() => {}}
          icon={searchIcon}
        />
        <Partners />
        <ProductCategories />
        <View style={styles.sectionHeader}>
          <Text style={styles.title}>
            {t("recommendedProducts.recommended")}
          </Text>
          <Text style={styles.seeAllText}>
            {t("recommendedProducts.seeAll")}
          </Text>
        </View>
      </View>
    ),
    [searchIcon]
  );

  return (
    <FlatList
      data={catalogQuery.data?.pages?.flatMap((x) => x.data)}
      renderItem={renderItem}
      keyExtractor={(item) => item.id.toString()}
      contentContainerStyle={styles.fullListContentContainer}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={renderHeader}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      refreshing={catalogQuery.isLoading}
      onRefresh={handleRefresh}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={5}
    />
  );
};

export default RecommendedProducts;
