import React, {useState, useCallback} from "react";
import {Text, View, ImageBackground} from "react-native";
import Screen from "../../components/screen";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InvisibleFullSizeButton from "../../components/invisible-full-size-button";
import BackButton from "../../components/back-button";
import InviteFriendsModal from "../../components/modals/invite-friends-modal";
import InvitedContactsList from "../../components/invited-contacts-list";
import styles from "@/styles/main/referral.style";
import UsersPlusIcon from "../../components/icons/users-plus-icon";

const Referral: React.FC = () => {
  const {t} = useTranslation();
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showContactsList, setShowContactsList] = useState(false);

  const handleInviteFriends = useCallback(() => {
    setShowInviteModal(true);
  }, []);

  const handleViewContactsList = useCallback(() => {
    setShowContactsList(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setShowInviteModal(false);
  }, []);

  const handleCloseContactsList = useCallback(() => {
    setShowContactsList(false);
  }, []);

  if (showContactsList) {
    return <InvitedContactsList onClose={handleCloseContactsList} />;
  }

  return (
    <>
      <Screen>
        <ImageBackground
          source={require("../../assets/intro-images/intro-slide-1.jpg")}
          style={styles.backgroundImage}
          imageStyle={styles.backgroundImageStyle}
        >
          <View style={styles.overlay} />
          <View style={styles.container}>
            <BackButton />
            <View style={styles.content}>
              <Text style={styles.title}>
                {t("referral.title", "Ganhe prêmios e descontos exclusivos!")}
              </Text>
              <Text style={styles.subtitle}>
                {t(
                  "referral.subtitle",
                  "Indique um amigo para o clube e concorra a prêmios e ganhe até 20% OFF na sua próxima compra ou assinatura no app."
                )}
              </Text>
              <Text style={styles.description}>
                {t(
                  "referral.description",
                  "É só pronto pra expandir seu networking?"
                )}
              </Text>
            </View>
            <View style={styles.buttonContainer}>
              <FullSizeButton
                text={t("referral.inviteFriends", "Convidar amigos")}
                onPress={handleInviteFriends}
                icon={<UsersPlusIcon />}
              />
              <InvisibleFullSizeButton
                text={t("referral.viewContactsList", "Ver lista de convidados")}
                onPress={handleViewContactsList}
              />
            </View>
          </View>
        </ImageBackground>
      </Screen>

      {showInviteModal && <InviteFriendsModal onClose={handleCloseModal} />}
    </>
  );
};

export default Referral;
