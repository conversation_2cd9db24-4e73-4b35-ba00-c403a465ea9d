import React from "react";
import { render } from "@testing-library/react-native";
import ChevronLeftIcon from "../../../components/icons/chevron-left-icon";

jest.doMock("react-native-svg", () => {
    const { View } = require("react-native");

    type MockProps = { [key: string]: unknown; children?: unknown };

    const MockSvg = ({ children, ...rest }: MockProps) => (
        <View {...rest}>{children}</View>
    );

    return {
        __esModule: true,
        default: MockSvg,
        Path: (props: MockProps) => <View {...props} />,
    };
});

describe("ChevronLeft component", () => {
    it("renders correctly and matches snapshot", () => {
        const { toJSON, getByTestId } = render(<ChevronLeftIcon testID="icon" />);

        expect(getByTestId("icon")).toBeTruthy();
        expect(toJSON()).toMatchSnapshot();
    });

    it("forwards props to underlying Svg element", () => {
        const { getByTestId } = render(
            <ChevronLeftIcon width={30} height={30} testID="icon" />
        );

        const svg = getByTestId("icon");
        expect(svg.props.width).toBe(30);
        expect(svg.props.height).toBe(30);
    });
});
