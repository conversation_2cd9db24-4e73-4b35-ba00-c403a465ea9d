import React, {useMemo} from "react";
import Svg, {<PERSON>lip<PERSON><PERSON>, Defs, G, Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface SmileIconProps extends SvgProps {
    replaceColor?: ColorValue;
}

const SmileIcon: React.FC<SmileIconProps> = (props) => {
    const color = useMemo(() => props.replaceColor ?? "#F2F4F7", [props.replaceColor]);

    return (
        <Svg
            width={20}
            height={20}
            viewBox="0 0 20 20"
            fill="none"
            {...props}
        >
            <G clipPath="url(#a)">
                <Path
                    stroke={color}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.667}
                    d="M6.667 11.667s1.25 1.667 3.333 1.667 3.333-1.667 3.333-1.667M12.5 7.5h.008M7.5 7.5h.008M18.333 10a8.333 8.333 0 1 1-16.666 0 8.333 8.333 0 0 1 16.666 0Zm-5.416-2.5a.417.417 0 1 1-.834 0 .417.417 0 0 1 .834 0Zm-5 0a.417.417 0 1 1-.834 0 .417.417 0 0 1 .834 0Z"
                />
            </G>
            <Defs>
                <ClipPath id="a">
                    <Path fill="#fff" d="M0 0h20v20H0z" />
                </ClipPath>
            </Defs>
        </Svg>
    );
};

export default SmileIcon; 