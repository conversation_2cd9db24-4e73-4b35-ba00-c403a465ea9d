import React, {useCallback, useMemo, useRef} from "react";
import {View, Text, TextInput} from "react-native";
import MegaDigit from "./mega-digit";
import styles from "../../styles/components/recovery-password/recovery-digits.style";

export interface RecoveryDigitsProps {
  value: string;
  onChange: (value: string) => void;
}

interface DigitData {
  id: string;
  value: string;
  position: number;
}

const RecoveryDigits: React.FC<RecoveryDigitsProps> = (props) => {
  const megaDigitsRefs = useRef<TextInput[]>([]);

  // Create stable digit data with unique identifiers for each position
  const digitData = useMemo<DigitData[]>(() => {
    const chars = props.value.split("");
    const emptySlots = Array(6).fill("");
    const values = [...chars, ...emptySlots].slice(0, 6);

    return values.map((value, index) => ({
      id: `recovery-digit-${index}`,
      value,
      position: index
    }));
  }, [props.value]);

  const onDigitChange = useCallback(
    (index: number) => (value: string) => {
      const newString =
        props.value.substring(0, index) +
        value +
        props.value.substring(index + 1);
      props.onChange(newString);

      if (value && index < megaDigitsRefs.current.length - 1) {
        megaDigitsRefs.current[index + 1]?.focus();
      }
    },
    [props.value, props.onChange, megaDigitsRefs]
  );

  const onBackspaceHandle = useCallback(
    (index: number) => () => {
      if (index > 0) {
        megaDigitsRefs.current[index - 1]?.focus();
      }
    },
    [megaDigitsRefs]
  );

  const addRef = useCallback(
    (index: number) => (ref: TextInput) => {
      megaDigitsRefs.current[index] = ref;
    },
    []
  );

  return (
    <View style={styles.container}>
      {digitData.map((digit) => (
        <React.Fragment key={digit.id}>
          <MegaDigit
            value={digit.value}
            ref={addRef(digit.position)}
            onChange={onDigitChange(digit.position)}
            onBackspace={onBackspaceHandle(digit.position)}
          />
          {digit.position == 2 && <Text style={styles.hyphen}>-</Text>}
        </React.Fragment>
      ))}
    </View>
  );
};

export default RecoveryDigits;
