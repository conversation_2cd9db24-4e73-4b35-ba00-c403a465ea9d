import React, {useCallback} from "react";
import styles from "@/styles/components/intro/overlay.style";
import {LinearGradient} from "expo-linear-gradient";
import stylesConstants from "@/styles/styles-constants";
import Dots from "./dots";
import {Text, TouchableOpacity, View} from "react-native";
import {useTranslation} from "react-i18next";
import NextButton from "./next-button";
import {useRouter} from "expo-router";

export interface SlideTextProps {
  title: string;
  description: string;
}

export interface OverlayProps {
  currentSlide: number;
  texts: SlideTextProps[];
  slideState: number;
  onChangeSlide?: (slide: number) => void;
}

const Overlay: React.FC<OverlayProps> = (props) => {
  const {t} = useTranslation();
  const router = useRouter();

  const onSkipIntroPress = useCallback(() => {
    router.navigate("/login");
  }, [router.navigate]);

  const onNextSlide = useCallback(() => {
    props.onChangeSlide?.(props.currentSlide + 1);
  }, [props.onChangeSlide, props.currentSlide]);

  const onPreviousSlide = useCallback(() => {
    props.onChangeSlide?.(props.currentSlide - 1);
  }, [props.onChangeSlide, props.currentSlide]);

  const isPreviousButtonDisabled = props.currentSlide === 0;
  const isNextButtonDisabled = props.currentSlide === props.texts.length - 1;

  return (
    <View style={styles.overlay}>
      <LinearGradient
        locations={[0.0666, 0.7481]}
        start={[0.5, 0]}
        end={[0.5, 1]}
        colors={[
          stylesConstants.colors.introSlideGradient.start,
          stylesConstants.colors.introSlideGradient.end
        ]}
      >
        <View style={styles.internalContainer}>
          <Dots
            style={styles.baseMarginBottom}
            dotState={props.slideState + props.currentSlide}
          />
          <Text style={[styles.text, styles.title, styles.baseMarginBottom]}>
            {t(props.texts[props.currentSlide].title)}
          </Text>
          <Text style={styles.text}>
            {t(props.texts[props.currentSlide].description)}
          </Text>
          <View style={styles.lastContainer}>
            <TouchableOpacity
              style={styles.lastContainerText}
              onPress={onSkipIntroPress}
            >
              <Text style={styles.skipIntroText}>
                {isNextButtonDisabled
                  ? t("intro.skipIntroButtonLastCard")
                  : t("intro.skipIntroButton")}
              </Text>
            </TouchableOpacity>
            <View style={styles.buttons}>
              <NextButton
                isPrevious={true}
                onPress={onPreviousSlide}
                disabled={isPreviousButtonDisabled}
              />
              <NextButton
                onPress={onNextSlide}
                disabled={isNextButtonDisabled}
              />
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

export default Overlay;
