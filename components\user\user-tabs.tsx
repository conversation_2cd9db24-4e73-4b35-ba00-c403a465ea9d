import React, {useState} from "react";
import {StyleProp, Text, TouchableOpacity, View, ViewStyle} from "react-native";
import styles from "../../styles/components/user/user-tabs.style";

export interface Tab {
    title: string;
    id: number;
}

export interface UserTabsProps {
    tabs: Tab[];
    currentTab?: number;
    onTabChange?: (id: number) => void;
    style?: StyleProp<ViewStyle>;
}

const UserTabs: React.FC<UserTabsProps> = (props) => {
    const [internalTab, setInternalTab] = useState(props.tabs[0].id);
    const activeTab =
        typeof props.currentTab === "number" ? props.currentTab : internalTab;

    const handleTabPress = (id: number) => {
        if (props.onTabChange) {
            props.onTabChange(id);
        }
        setInternalTab(id);
    };

    return (
        <View style={[styles.container, props.style]}>
            {props.tabs.map((tab) => (
                <TouchableOpacity
                    key={tab.id}
                    style={[
                        styles.tab,
                        activeTab === tab.id && styles.activeTab
                    ]}
                    onPress={() => handleTabPress(tab.id)}
                    activeOpacity={0.7}
                >
                    <Text
                        style={[
                            styles.text,
                            activeTab === tab.id && styles.activeText
                        ]}
                    >
                        {tab.title}
                    </Text>
                </TouchableOpacity>
            ))}
        </View>
    );
};

export default UserTabs;
