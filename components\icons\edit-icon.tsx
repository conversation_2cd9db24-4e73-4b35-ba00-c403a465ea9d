import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const EditIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg width={21} height={20} viewBox="0 0 20 20" fill="none" {...props}>
            <Path
                stroke="#FCFCFD"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.667}
                d="M10.75 16.666h7.5m-15 0h1.395c.408 0 .612 0 .804-.046.17-.04.332-.108.482-.2.168-.103.312-.247.6-.535L17 5.416a1.768 1.768 0 1 0-2.5-2.5L4.031 13.386c-.288.287-.432.432-.535.6-.092.149-.16.311-.2.481-.046.192-.046.396-.046.804v1.395Z"
            />
        </Svg>
    );
};

export default EditIcon;
