import React from "react";
import Svg, {Path, SvgProps} from "react-native-svg";

const CalendarBackgroundIcon: React.FC<SvgProps> = (props) => {
    return (
        <Svg
            width={99}
            height={100}
            fill="none"
            viewBox="0 0 99 100"
            {...props}
        >
            <Path
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={3.5}
                d="M83.25 38.5h-94.5M57-3.5v21m-42-21v21m-1.05 84h44.1c8.82 0 13.231 0 16.6-1.717a15.75 15.75 0 0 0 6.883-6.883c1.717-3.369 1.717-7.78 1.717-16.6V32.2c0-8.82 0-13.231-1.717-16.6a15.75 15.75 0 0 0-6.883-6.883C71.281 7 66.87 7 58.05 7h-44.1C5.13 7 .719 7-2.65 8.717A15.75 15.75 0 0 0-9.533 15.6c-1.717 3.369-1.717 7.78-1.717 16.6v44.1c0 8.82 0 13.231 1.717 16.6a15.75 15.75 0 0 0 6.883 6.883c3.369 1.717 7.78 1.717 16.6 1.717Z"
                opacity={0.1}
            />
        </Svg>
    );
};

export default CalendarBackgroundIcon;