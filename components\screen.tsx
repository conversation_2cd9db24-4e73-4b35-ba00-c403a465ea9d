import React, {PropsWithChildren} from "react";
import {
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
  View
} from "react-native";
import Constants from "expo-constants";
import styles from "@/styles/components/screen.style";

export interface ScreenProps extends PropsWithChildren {
  disableScrollView?: boolean;
}

const Screen: React.FC<ScreenProps> = (props) => {
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <KeyboardAvoidingView
        style={styles.screenView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={
          Platform.OS === "ios" ? Constants.statusBarHeight : 0
        }
      >
        {props.disableScrollView ? (
          <View style={styles.containerFull}>{props.children}</View>
        ) : (
          <ScrollView
            style={styles.containerFull}
            contentContainerStyle={styles.growFull}
            keyboardShouldPersistTaps="handled"
            nestedScrollEnabled={true}
          >
            {props.children}
          </ScrollView>
        )}
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
};

export default Screen;
