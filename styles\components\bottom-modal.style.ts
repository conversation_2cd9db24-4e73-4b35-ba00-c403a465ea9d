import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
    container: {
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "center",
        backgroundColor: stylesConstants.colors.mainBackground,
        position: "absolute",
        zIndex: 1000,
        bottom: 0,
        paddingBottom: 16,
        paddingHorizontal: 24,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        borderStyle: "solid",
        borderColor: "#282A2E",
        borderTopWidth: 1.5,
        borderLeftWidth: 1.5,
        borderRightWidth: 1.5,
    },
    baseAnimationPosition: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "black",
        zIndex: 999,
    },
    backgroundTouch: {
        flex: 1,
    },
    header: {
        display: "flex",
        height: 64,
        flexDirection: "column",
        paddingTop: 8,
        alignItems: "center",
    },
    notch: {
        backgroundColor: stylesConstants.colors.fullWhite,
        width: 44,
        height: 4,
        borderRadius: 4,
        marginBottom: 24
    },
    title: {
        color: stylesConstants.colors.textPrimary,
        textAlign: "center",
        fontFamily: stylesConstants.fonts.openSans,
        fontSize: 16,
        fontStyle: "normal",
        fontWeight: 600,
        lineHeight: 24,
    },
    bodyContainer: {
        paddingVertical: 12,
        display: "flex",
        flexDirection: "column",
        gap: 24
    },
    bodyFeaturedContainer: {
        backgroundColor: stylesConstants.colors.secondaryBackground,
        padding: 16,
        minWidth: "100%",
        borderRadius: 8,
    },
    buttonsContainer: {
        paddingVertical: 16,
        display: "flex",
        flexDirection: "column",
        gap: 10
    }
});

export default styles;